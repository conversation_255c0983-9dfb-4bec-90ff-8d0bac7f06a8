<template>
        <el-menu
            class="sidebar-el-menu"
            :default-active="onRoutes"
            :collapse="collapse"
            text-color="#ffffff"
            active-text-color="#000000"
            unique-opened
            :default-openeds="showOpeneds"
            router
            @select="selectNeed"
        >
        <div class="sidebar-header" v-if="!collapse">
            欢迎登录<br>
            税源地管理系统
        </div>
        <div class="sidebar-header" v-else>
            税源地
        </div>
            <template v-for="item in items">
                <template v-if="item.subs">
                    <el-submenu :index="item.index" :key="item.index">
                        <template slot="title">
                            <i :class="item.icon"></i>
                            <span slot="title">{{ item.title }}</span>
                        </template>
                        <template v-for="subItem in item.subs">
                            <el-submenu v-if="subItem.subs" :index="subItem.index" :key="subItem.index">
                                <template slot="title">{{ subItem.title }}</template>
                                <el-menu-item v-for="(threeItem, i) in subItem.subs" :key="i" :index="threeItem.index"
                                    >{{ threeItem.title }}
                                </el-menu-item>
                            </el-submenu>
                            <el-menu-item
                                v-else
                                :index="subItem.index"
                                :key="subItem.index"
                                :class="[indexUrl == subItem.index ? 'activeChose' : '']"
                                >{{ subItem.title }}
                            </el-menu-item>
                        </template>
                    </el-submenu>
                </template>
                <template v-else>
                    <el-menu-item :index="item.index" :key="item.index">
                        <i :class="item.icon"></i>
                        <span slot="title">{{ item.title }}</span>
                    </el-menu-item>
                </template>
            </template>
        </el-menu>
</template>

<script>
import bus from '../common/bus';
import { listTreeMenuByUser } from '@/api/system/login';
import auth from '@/api/auth.js';
import { getCurrentInstance } from 'vue';
export default {
    props: {
        collapse: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            items: [],
            indexUrl: '',
            showOpeneds: [],
            myCollapse: false
        };
    },
    computed: {
        onRoutes() {
            return this.$route.path.replace('/', '');
        }
    },
    created() {
        const { proxy } = getCurrentInstance();
        this.indexUrl = proxy.$router.currentRoute.fullPath;
        this.menuList();
    },
    methods: {
        //获取菜单信息
        menuList() {
            listTreeMenuByUser().then((res) => {
                let menuList = [];
                res.data.forEach((item) => {
                    //一级菜单
                    let firstItem = {
                        icon: 'iconfont ' + item.icon,
                        index: item.vuePath || item.id,
                        title: item.name
                    };
                    //二级菜单
                    if (item.children.length > 0) {
                        let subList = [];
                        item.children.forEach((items) => {
                            let subItem = {
                                index: items.vuePath,
                                title: items.name
                            };
                            subList.push(subItem);
                            if (items.vuePath == this.indexUrl) {
                                this.showOpeneds.push(item.id);
                            }
                        });
                        firstItem.subs = subList;
                    }
                    menuList.push(firstItem);
                });

                this.items = menuList;

                //设置按钮权限
                let menu = [];
                this.permissionsList(res.data, menu);
                auth.doPermissions(menu);
            });
        },
        //按钮权限列表
        permissionsList(data, result) {
            data.forEach((item) => {
                if (item.children) {
                    this.permissionsList(item.children, result);
                }
                if (item.permission) {
                    result.push(item.permission);
                }
            });
        },
        selectNeed(index, indexPath) {
            // debugger
            this.indexUrl = index;
        }
    }
};
</script>

<style scoped>
.sidebar {
    width: 210px!important;
}

.sidebar-el-menu {
    width: 64px;
    height: 100%;
    color: #fff;
    overflow-y: auto;
    position: relative;
    background: var(--sidebar-gradient);
}

.sidebar-el-menu::after {
    content: '';
    position: absolute;
    top: -10px;
    left: 0;
    width: 100%;
    height: 200px;
    background-image: url("~@/assets/img/sidebar_bg.png");
    background-repeat: no-repeat;
    background-position: left top;
    background-size: contain;
    opacity: 0.5;
    pointer-events: none;
    display: var(--sidebar-decoration-display);
}

.sidebar-el-menu:not(.el-menu--collapse) {
    /* background-color: #C9070D; */
    width: 210px;
    height: 100%;
    color: #fff;
    background: var(--sidebar-gradient);
}

.sidebar-el-menu:not(.el-menu--collapse)::after {
    width: 100%;
    height: 400px;
    background-position: left top;
    display: var(--sidebar-decoration-display);
}

.sidebar-header {
    width: 100%;
    text-align: center;
    padding-top: 15px;
    padding-bottom: 20px;
    font-size: 18px;
    line-height: 24px;
}

.el-menu-item .iconfont,
.el-submenu .iconfont {
    margin-right: 5px;
    width: 24px;
    text-align: center;
    font-size: 18px;
    vertical-align: middle;
}

:deep(.el-menu) {
    border-right: none;
    background: var(--sidebar-background) !important;
}

:deep(.el-submenu__title) {
    font-size: 16px;
    font-weight: 600;
    background: transparent !important;

    &:hover {
        background-color: rgba(255, 255, 255, 0.2) !important;
    }
}

:deep(.el-menu-item) {
    background: transparent !important;

    &.el-menu--collapse {
        width: 64px;
        text-align: center;
    }

    &:hover {
        background-color: rgba(255, 255, 255, 0.2) !important;
    }
    &.activeChose {
        background: var(--sidebar-active-bg) !important;
        color: #fff !important;
        font-weight: 600 !important;
    }

    &.is-opened {
        background-color: rgba(255, 255, 255, 0.2) !important;
        color: #fff !important;
    }
}
</style>

<style>
.el-menu--popup {
    background-color: var(--sidebar-background) !important;
}
</style>
