<template>
    <div class="header" v-loading.fullscreen.lock="fullscreenLoading" element-loading-text="切换中请稍后~">
        <!-- 折叠按钮 -->
        <div class="collapse-btn" @click="collapseChage">
            <i v-if="!collapse" class="el-icon-s-fold"></i>
            <i v-else class="el-icon-s-unfold"></i>
            <!-- <i class="el-icon-s-fold"></i> -->
        </div>
        <div class="header-right">
            <div class="header-user-con">
                <!-- 下载中心 -->
                <div @click="toJumpDownPlace" class="btn-bell">
                    <el-tooltip effect="dark" :content="`下载中心`" placement="bottom">
                        <i class="bi-save"></i>
                    </el-tooltip>
                </div>
                <!-- 消息中心 -->
                <div class="btn-bell">
                    <el-tooltip effect="dark" :content="`消息中心`" placement="bottom">
                        <router-link to="/tabs">
                            <i class="bi-bell"></i>
                        </router-link>
                    </el-tooltip>
                    <!-- <span class="btn-bell-badge" v-if="message.title!=undefined"></span> -->
                </div>
                <!-- 主题切换 -->
                <div class="theme-switcher-container">
                    <el-tooltip effect="dark" :content="`切换主题`" placement="bottom">
                        <theme-switcher></theme-switcher>
                    </el-tooltip>
                </div>
                <el-select v-model="nowQy" placeholder="请选择" @change="toChangeInfo" filterable>
                    <el-option v-for="item in options" :key="item.enterpriseId" :label="item.enterpriseName" :value="item.enterpriseId">
                    </el-option>
                </el-select>
                <!-- 用户头像 -->
                <div class="user-avator" style="display: inherit">
                    <el-avatar v-if="!user.avatar" :size="32">
                        {{ user.name }}
                    </el-avatar>
                    <img v-else :src="user.avatar" />
                </div>
                <!-- 用户名下拉菜单 -->
                <el-dropdown class="user-name" trigger="click" @command="handleCommand">
                    <span class="el-dropdown-link"">
                        {{ user.name }}
                        <i class="el-icon-caret-bottom"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                        <el-dropdown-item divided command="loginout">退出登录</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </div>
        </div>
    </div>
</template>
<script>
import { Message } from 'element-ui';
import bus from '../common/bus';
import { SESSION_STORAGE_KEY } from '@/api/const';
import auth from '@/api/auth.js';
import { logout, getUserEnterprise, changeUserEnterprise, getUserInfo, getLogo } from '@/api/system/login';
import { mapState, mapMutations } from 'vuex';
import { OSS_URL } from '@/api/config';
import ThemeSwitcher from './ThemeSwitcher.vue';
export default {
    components: {
        ThemeSwitcher
    },
    props: {
        collapse: {
            type: Boolean,
            default: false
        },
        // message: Object
    },
    data() {
        return {
            fullscreen: false,
            user: {
                name: 'admin',
                avatar: '/'
            },
            options: [],
            nowQy: '',
            fullscreenLoading: false,
            logoUrl: '',
            myCollapse: false,
        };
    },
    computed: {
        ...mapState({
            userInfo: (state) => state.user.userInfo
        })
    },
    watch: {
        userInfo(val, oldval) {
            if (val.avatar) {
                this.user = {
                    name: val.name,
                    avatar: val.avatar.indexOf('http') > -1 ? val.avatar : OSS_URL + val.avatar
                };
                this.$forceUpdate();
            } else {
                this.user = {
                    name: val.name,
                    avatar: ''
                };
                this.$forceUpdate();
            }
        }
    },
    methods: {
        toJumpDownPlace() {
            this.$router.push('/system/downloadCenter');
        },
        //头像渲染失败
        // 用户名下拉菜单选择事件
        handleCommand(command) {
            if (command == 'loginout') {
                this.$confirm(`确定要退出吗？`, '提示', {
                    type: 'warning'
                })
                    .then(() => {
                        logout().then((res) => {
                            if (res.code == 0) {
                                auth.doLogout();
                            } else {
                                Message.error(res.msg);
                            }
                        });
                    })
                    .catch(() => {});
            } else if (command == 'profile') {
                this.$router.push('/system/user/profile');
            }
        },
        // 侧边栏折叠
        collapseChage() {
            this.myCollapse = !this.myCollapse;
            localStorage.setItem('sidebarCollapse', this.myCollapse ? 1 : 0);
            this.$emit('input', this.myCollapse);
        },
        // 全屏事件
        handleFullScreen() {
            let element = document.documentElement;
            if (this.fullscreen) {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.webkitCancelFullScreen) {
                    document.webkitCancelFullScreen();
                } else if (document.mozCancelFullScreen) {
                    document.mozCancelFullScreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
            } else {
                if (element.requestFullscreen) {
                    element.requestFullscreen();
                } else if (element.webkitRequestFullScreen) {
                    element.webkitRequestFullScreen();
                } else if (element.mozRequestFullScreen) {
                    element.mozRequestFullScreen();
                } else if (element.msRequestFullscreen) {
                    // IE11
                    element.msRequestFullscreen();
                }
            }
            this.fullscreen = !this.fullscreen;
        },
        toChangeInfo() {
            changeUserEnterprise({
                id: this.nowQy
            }).then((res) => {
                this.$router.push('/');
                this.fullscreenLoading = true;
                setTimeout(() => {
                    window.location.reload();
                }, 1300);
                setTimeout(() => {
                    this.fullscreenLoading = false;
                }, 1400);
            });
        },
        async getThisUrlLogo() {
            const url = window.location.href;
            let urlList = url.split('/?');
            let res = await getLogo(urlList[0]);
            if (res.data && res.data.logoUrl) {
                this.logoUrl = OSS_URL + res.data.logoUrl;
            }
        },
        async fetchTaxAuth() {
            const userRes = await getUserInfo();
            if (userRes && userRes.code === 0 && userRes.data) {
                const taxBelongId = userRes.data.belongId;
                const entRes = await getUserEnterprise();
                if (entRes && entRes.code === 0 && entRes.data) {
                    const current = entRes.data.find(e => e.enterpriseId === taxBelongId);
                    const taxBelongName = current ? current.enterpriseName : '';
                    this.setTaxAuth({ id: taxBelongId, name: taxBelongName });
                }
            }
        },
        ...mapMutations({
            setUser: 'user/setUser',
            setTaxAuth: 'taxAuth/setTaxAuth'
        })
    },
    mounted() {
        let user = JSON.parse(sessionStorage.getItem(SESSION_STORAGE_KEY.USERINFO));
        this.myCollapse = parseInt(localStorage.getItem('sidebarCollapse')) > 0;
        this.$emit('input', this.myCollapse);
        if (user) {
            this.setUser(user);
        }
        getUserInfo().then((res) => {
            if (res.code == 0) {
                this.nowQy = res.data.belongId;
            }
        });
        getUserEnterprise().then((res) => {
            if (res.code == 0) {
                this.options = res.data;
            }
        });
        this.getThisUrlLogo();
        this.fetchTaxAuth();
    }
};
</script>
<style scoped>
.header {
    position: relative;
    box-sizing: border-box;
    width: 100%;
    height: 56px;
    font-size: 22px;
    color: #333;
    background: var(--header-background);
}

.collapse-btn {
    float: left;
    padding: 0;
    cursor: pointer;
    line-height: 56px;
}

.collapse-btn:hover {
    background-color: var(--collapse-btn-hover-bg) !important;
}

.header .logo {
    float: left;
    width: 250px;
    line-height: 56px;
    display: flex;
    align-items: center;
}

.header-right {
    float: right;
    padding-right: 20px;
}

.header-user-con {
    display: flex;
    height: 56px;
    align-items: center;
}

.btn-fullscreen {
    /* transform: rotate(45deg); */
    margin-right: 5px;
    font-size: 24px;
}

.btn-bell,
.btn-fullscreen {
    position: relative;
    width: 22px;
    font-size:18px;
    text-align: center;
    border-radius: 15px;
    cursor: pointer;
    margin-right: 20px;
}

.btn-bell-badge {
    position: absolute;
    right: 0;
    top: -2px;
    width: 8px;
    height: 8px;
    border-radius: 4px;
    background: #f56c6c;
    color: #fff;
}

.user-name {
    margin-left: 10px;
}

.user-avator {
    margin-left: 20px;
}

.user-avator img {
    display: block;
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

.el-dropdown-link {
    color: var(--dropdown-link-color);
    cursor: pointer;
}

.el-dropdown-menu__item {
    text-align: center;
}
.logo-size-head {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: block;
    margin-right: 20px;
}

.bi-save, .bi-bell {
    color: var(--icon-color);
}

.theme-switcher-container {
    margin-right: 20px;
    display: flex;
    align-items: center;
}

.el-input__inner {
    border: 0px!important;
}

.el-avatar {
    font-size: 12px;
}

.el-icon-s-fold, .el-icon-s-unfold, .bi-save, .bi-bell, .el-dropdown-link {
    color: var(--header-icon-color);
}
</style>
