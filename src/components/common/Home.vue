<template>
    <el-container class="wrapper">
        <v-sidebar :collapse="collapse"></v-sidebar>
        <el-container style="width: 100%;">
            <el-header height="56px"><v-head v-model="collapse"></v-head></el-header>
                <div class="content-box">
                    <div class="content">
                        <transition name="move" mode="out-in">
                            <keep-alive :include="tagsList">
                                <router-view></router-view>
                            </keep-alive>
                        </transition>
                        <el-backtop target=".content"></el-backtop>
                    </div>
                </div>
                <el-dialog :visible.sync="dialogFormVisible" top="10vh" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false">
                    <div slot="title" style="display: flex; justify-content: center; align-items: center;">
                        <span style="font-size: 25px; font-weight: 600;">通 知</span>
                    </div>
                    <div style="margin: -20px 20px 0 20px; ">
                        <div>
                            <span style="font-size: 20px">{{ message.title }}</span>
                        </div>
                        <div style="margin-top: 10px;">
                            <div v-html="message.content"></div>
                        </div>
                        <div style="font-size:12px;text-align: right; margin-top: 10px;">
                            通知时间: {{ message.createTime }}
                        </div>
                    </div>
                    <div slot="footer" class="dialog-footer">
                        <el-button type="primary" @click="onDigClose">我知道了</el-button>
                    </div>
                </el-dialog>
        </el-container>
    </el-container>
</template>

<script>
import vHead from './Header.vue';
import vSidebar from './Sidebar.vue';
import vTags from './Tags.vue';
import bus from './bus';
import { getSpotNews, oneRead } from '@/api/message/message.js';
export default {
    data() {
        return {
            collapse: false,
            tagsList: [],
            collapse: false,
            hasMsg: false,
            message: {},
            dialogFormVisible: false,
            mssageInfo: {},
            noticeInterval: null
        };
    },
    components: {
        vHead,
        vSidebar,
        vTags
    },
    created() {
        bus.$on('collapse-content', (msg) => {
            this.collapse = msg;
        });
        // 只有在标签页列表里的页面才使用keep-alive，即关闭标签之后就不保存到内存中了。
        bus.$on('tags', (msg) => {
            let arr = [];
            for (let i = 0, len = msg.length; i < len; i++) {
                msg[i].name && arr.push(msg[i].name);
            }
            this.tagsList = arr;
        });
        this.getNoticeMsg();
        // 每5分钟请求一次
        this.noticeInterval = setInterval(() => {
            this.getNoticeMsg();
        }, 60000);
    },
    beforeDestroy() {
        if (this.noticeInterval) {
            clearInterval(this.noticeInterval);
        }
    },
    methods: {
        //查看是否有消息
        async getNoticeMsg() {
            let res = await getSpotNews();
            if (res.data.data) {
                this.message = res.data.data;
                this.dialogFormVisible = true;
                this.toReadMsg();
            }
        },
        async toReadMsg() {
            let res = await oneRead({ ids: [this.message.id] });
            if (res.data.code != 0) return false;
            this.dialogFormVisible = true;
            this.hasMsg = false;
        },
        onDigClose() {
            this.dialogFormVisible = false;
            this.getNoticeMsg();
        }
    }
};
</script>
<style scoped>
.messages_in {
    position: fixed;
    top: 60px;
    left: 50%;
    color: #000;
    display: flex;
    justify-content: center;
    transition: 0.4s;
    transform: translate(-50%,-40%);
    z-index: -1;
    opacity: 0;

}
.messages_in.active {
    transform: translate(-50%,0%);
    opacity: 1;
    z-index: 9999;

}
.messages_detail {
    border-radius: 4px;
    border: 1px solid #ebeef5;
    background-color: #fff;
    overflow: hidden;
    color: #303133;
    box-shadow: 3px 3px 5px 1px rgba(0, 0, 0, 0.4);
    padding: 10px 20px;
    display: flex;
    align-items: center;
    font-size: 14px;
    transition: opacity 0.3s, transform 0.4s, top 0.4s;
}
.laba_icon {
    width: 22 px;
    display: block;
    object-fit: cover;
    height: 22px;
    margin-right: 10px;
}
.btn_color {
    user-select: none;
    cursor: pointer;
    color: #409eff;
    margin: 0px 30px 0 20px;
}
.el-header {
    padding: 0;
    background: var(--header-background);
}
</style>
