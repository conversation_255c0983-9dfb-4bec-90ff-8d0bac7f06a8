<template>
    <div>
        <el-dialog title="导出格式选择" :visible.sync="toshowDia" @close="closeDia" append-to-body width="30%">
            <div class="choose-type-box">
                <div class="choose-type-item">
                    <div @click="toChangeType(0)" class="box-size">
                        <div :class="[form.isFile == 0 ? 'top_box active' : 'top_box']">
                            <img src="@/assets/img/excel_blue.png" v-if="form.isFile == 0" class="img_1" alt="" />
                            <img src="@/assets/img/excel_gray.png" v-if="form.isFile != 0" class="img_1" alt="" />
                        </div>
                        <div class="top_bottom">
                            <img src="@/assets/img/<EMAIL>" class="row-icon" v-if="form.isFile == 0" alt="" />
                            <img src="@/assets/img/Ellipse.png" class="row-icon" v-if="form.isFile != 0" alt="" />
                            <div :style="{ color: form.isFile == 0 ? '#439EF9' : '#838383' }">仅excel格式</div>
                        </div>
                    </div>
                </div>
                <div class="choose-type-item">
                    <div @click="toChangeType(1)" class="box-size">
                        <div :class="[form.isFile == 1 ? 'top_box active' : 'top_box']">
                            <img src="@/assets/img/group_gray.png" class="img_2" alt="" v-if="form.isFile != 1" />
                            <img src="@/assets/img/group_blue.png" v-if="form.isFile == 1" class="img_2" alt="" />
                        </div>
                        <div class="top_bottom">
                            <img src="@/assets/img/<EMAIL>" class="row-icon" v-if="form.isFile == 1" alt="" />
                            <img src="@/assets/img/Ellipse.png" class="row-icon" v-if="form.isFile != 1" alt="" />
                            <div :style="{ color: form.isFile == 1 ? '#439EF9' : '#838383' }">excel+pdf格式</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="button_box">
                <div style="flex: 1"></div>
                <el-button @click="closeDia">取消</el-button>
                <el-button type="primary" @click="toSureCode">确认</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import auth from '@/api/auth.js';
export default {
    name: 'exportDia',
    data() {
        return {
            toshowDia: false,
            form: {
                isFile: 0
            },
            showInit: 0
        };
    },
    props: {},
    watch: {},
    methods: {
        closeDia() {},
        toSureCode() {},
        toJumpPassword() {},
        toChangeType(id) {
            this.form.isFile = id;
        }
    },
    created() {
        let userInfo = JSON.parse(sessionStorage.getItem('USERINFO'));
        this.showInit = userInfo.init;
    }
};
</script>

<style scoped>
.button_box {
    display: flex;
    align-items: center;
    margin-top: 20px;
}
.attention_word {
    font-size: 16px;
    color: #333;
}
.attention_word span {
    font-size: 16px;
    color: #449ef9;
}
.box-size{
    user-select: none;
    cursor: pointer;
}
.choose-type-box {
    display: flex;
    align-items: center;
}
.choose-type-item {
    width: 50%;
    display: flex;
    justify-content: center;

}
.top_box {
    width: 118px;
    height: 118px;
    box-shadow: 0px 5px 5px 0px transparent;
    border-radius: 6px 6px 6px 6px;
    background-color: #fbfbfb;
    border: 1.5px solid transparent;
    display: flex;
    align-items: center;
    justify-content: center;
}
.top_box.active {
    width: 118px;
    height: 118px;
    box-shadow: 0px 5px 5px 0px rgba(0, 0, 0, 0.1);
    border-radius: 6px 6px 6px 6px;
    background-color: #fff;
    border: 1.5px solid #439ef9;
    display: flex;
    align-items: center;
    justify-content: center;
}
.img_1 {
    width: 52px;
    height: 52px;
}
.img_2 {
    width: 98px;
    height: 38px;
}
.top_bottom {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    margin-top: 18px;
}
.row-icon {
    width: 14px;
    height: 14px;
    margin-right: 6px;
    display: block;
    margin-top: 2px;
}
</style>
