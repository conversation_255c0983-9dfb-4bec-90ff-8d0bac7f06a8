<template>
  <el-dropdown trigger="click" @command="handleCommand">
    <span class="el-dropdown-link">
      <span class="theme-name">{{ getCurrentThemeLabel }}</span>
      <i class="el-icon-arrow-down el-icon--right"></i>
    </span>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item 
        v-for="theme in themes" 
        :key="theme.name" 
        :command="theme.name"
        :class="{ 'is-active': currentTheme === theme.name }">
        {{ theme.label }}
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
import { themes, getCurrentTheme, setTheme } from '@/utils/theme.js';

export default {
  name: 'ThemeSwitcher',
  data() {
    return {
      themes,
      currentTheme: getCurrentTheme()
    };
  },
  computed: {
    getCurrentThemeLabel() {
      const theme = this.themes.find(t => t.name === this.currentTheme);
      return theme ? theme.label : '默认主题';
    }
  },
  methods: {
    handleCommand(command) {
      this.currentTheme = setTheme(command);
      this.$message({
        message: `已切换到${this.themes.find(t => t.name === command).label}`,
        type: 'success'
      });
    }
  },
  mounted() {
    // 监听主题变化事件
    window.addEventListener('themeChange', () => {
      this.currentTheme = getCurrentTheme();
    });
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('themeChange', () => {
      this.currentTheme = getCurrentTheme();
    });
  }
};
</script>

<style scoped>
.el-dropdown-menu__item.is-active {
  color: #409EFF;
  background-color: #ecf5ff;
}

.theme-name {
  color: var(--header-icon-color);
  font-size: 14px;
}

.el-icon-arrow-down {
  color: var(--header-icon-color);
  margin-left: 4px;
}

/* 在登录页面上的特殊样式 */
.login-wrap .theme-name,
.login-wrap .el-icon-arrow-down,
.theme-switcher-corner .theme-name,
.theme-switcher-corner .el-icon-arrow-down {
  color: #fff;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.theme-switcher-corner .el-dropdown-link {
  padding: 5px 10px;
  border-radius: 4px;
  display: inline-block;
}

.theme-switcher-corner .el-dropdown-link:hover {
  background-color: rgba(255, 255, 255, 0.3);
  cursor: pointer;
}
</style> 