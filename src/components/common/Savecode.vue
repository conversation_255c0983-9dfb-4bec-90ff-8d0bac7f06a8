<template>
    <div>
        <el-dialog title="请输入安全操作密码" :visible.sync="toshowDia" @close="closeDia" append-to-body width="40%" @open="toShowDiaInfo">
            <el-form v-if="showInit == 1">
                <el-form-item label="安全操作码">
                    <el-input v-model="form.password" autocomplete="off" show-password style="width: 70%"></el-input>
                </el-form-item>
                <div class="button_box">
                    <div style="flex: 1"></div>
                    <el-button @click="closeDia">取消</el-button>
                    <el-button type="primary" @click="toSureCode">确认</el-button>
                </div>
            </el-form>
            <div v-if="showInit == 0">
                <div class="attention_word">
                    您还未设置安全密码，<span>请先设置安全码&nbsp;&nbsp;<i class="el-icon-arrow-down"></i></span>
                </div>
                <el-form>
                    <el-form-item label="设置密码">
                        <el-input v-model="forms.password" autocomplete="off" show-password style="width: 50%"></el-input>
                    </el-form-item>
                    <el-form-item label="重复密码">
                        <el-input v-model="forms.surePassword" autocomplete="off" show-password style="width: 50%"></el-input>
                    </el-form-item>
                </el-form>
                <div class="button_box">
                    <div style="flex: 1"></div>
                    <el-button @click="closeDia">取消</el-button>
                    <el-button type="primary" @click="toJumpPassword">设置密码</el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import auth from '@/api/auth.js';
import { getUserInfo } from '@/api/system/login';
export default {
    name: 'saveCode',
    data() {
        return {
            toshowDia: false,
            form: {
                password: ''
            },
            forms: {
                password: '',
                surePassword: ''
            },
            showInit: 0
        };
    },
    props: {},
    watch: {},
    methods: {
        closeDia() {},
        toSureCode() {},
        toJumpPassword() {},
        toShowDiaInfo() {
            getUserInfo().then((res) => {
                if (res.code == 0) {
                    this.showInit = res.data.init;
                }
            });
        }
    },
};
</script>

<style scoped>
.button_box {
    display: flex;
    align-items: center;
    margin-top: 20px;
}
.attention_word {
    font-size: 16px;
    color: #333;
    margin-bottom: 20px;
}
.attention_word span {
    font-size: 16px;
    color: #449ef9;
}
</style>
