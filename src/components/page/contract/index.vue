<template>
    <div>
        <el-card shadow='never'>
            <div slot='header' class='clearfix'>
                <span>企业合同列表</span>
            </div>
            <el-form :model='form' :inline='true' class='demo-form-inline'>
                <el-row type='flex' align='bottom'>
                    <el-form-item label="合同名称：">
                        <el-input v-model='form.contractName' placeholder="请输入合同名称" style='width: 220px;' clearable></el-input>
                    </el-form-item>
                    <el-form-item label="签署方名称：">
                        <el-input v-model='form.signatoryName' placeholder="请输入签署方名称" style='width: 200px;' clearable></el-input>
                    </el-form-item>
                    <el-form-item label="状态：" prop="status">
                        <el-select v-model="form.status" placeholder="请选择状态" filterable clearable>
                            <el-option :key="1" label="待制作" :value="1"></el-option>
                            <el-option :key="2" label="待认证" :value="2"></el-option>
                            <el-option :key="3" label="认证中" :value="3"></el-option>
                            <el-option :key="4" label="待发起" :value="4"></el-option>
                            <el-option :key="5" label="待签署" :value="5"></el-option>
                            <el-option :key="6" label="已签署" :value="6"></el-option>
                            <el-option :key="7" label="已取消" :value="7"></el-option>
                        </el-select>
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-form-item label="合同编号：">
                        <el-input v-model='form.contractNo' placeholder="请输入合同编号" style='width: 220px;' clearable></el-input>
                    </el-form-item>
                    <el-form-item label="创建时间：">
                        <el-date-picker
                            v-model="form.date"
                            type="daterange"
                            range-separator="~"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            value-format="yyyy-MM-dd"
                            clearable
                            @change="toChangeTime"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" icon="el-icon-search" size="medium" @click="queryOnboardingList">查询</el-button>
                        <el-button type="primary" icon="el-icon-plus" size="medium" @click="showCreateDialog">创建合同</el-button>
                    </el-form-item>
                </el-row>
            </el-form>
            <el-table :data='tableData.records' style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                <el-table-column prop="contractNo" label="合同编号" width="210"></el-table-column>
                <el-table-column prop="contractName" label="合同名称" min-width="180"></el-table-column>
                <el-table-column prop="status" label="状态" width="100">
                    <template slot-scope="scope">
                        <el-tag :type="contractStatusMap[scope.row.status].type" size="mini">
                            {{ contractStatusMap[scope.row.status].label }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="firstName" label="甲方名称" min-width="220"></el-table-column>
                <el-table-column prop="secondName" label="乙方名称" min-width="220"></el-table-column>
                <el-table-column prop="firstSignedTime" label="甲方签署时间" width="170"></el-table-column>
                <el-table-column prop="secondSignedTime" label="乙方签署时间" width="170"></el-table-column>
                <el-table-column prop="createTime" label="创建时间" width="170"></el-table-column>
                <el-table-column label="操作" fixed='right' width="240" align="center">
                    <template slot-scope="scope">
                        <el-button type="text" @click="viewDetail(scope.row)" style="font-size: 13px;">查看详情</el-button>
                        <el-button type="text" v-if="scope.row.contractAttachmentUrl" @click="downloadAttachment(scope.row.contractAttachmentUrl)" style="font-size: 13px;">下载合同</el-button>
                        <el-button type="text" v-if="scope.row.reportAttachmentUrl" @click="downloadAttachment(scope.row.reportAttachmentUrl)" style="font-size: 13px;">下载存证</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    @current-change="handleCurrentPageChange"
                    :current-page="tableData.currentPage"
                    :page-size="tableData.pageSize"
                    layout="total, prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
        </el-card>
        
        <!-- 创建合同对话框 -->
        <el-dialog title="创建合同" :visible.sync="createDialogVisible" width="600px">
            <el-form :model="createForm" ref="createFormRef" label-width="115px" :rules="createRules">
                <el-form-item label="合同名称：" prop="contractType">
                    <el-select v-model="createForm.contractType" placeholder="请选择合同类型" style="width: 100%">
                        <el-option label="共享经济服务协议" :value="1"></el-option>
                    </el-select>
                </el-form-item>
                
                <el-divider content-position="left">甲方信息</el-divider>
                
                <el-form-item label="企业名称：" prop="firstName">
                    <el-input v-model="createForm.firstName" placeholder="请输入企业名称"></el-input>
                </el-form-item>
                
                <el-form-item label="签署手机号：" prop="firstSignPhone">
                    <el-input v-model="createForm.firstSignPhone" placeholder="请输入签署手机号"></el-input>
                </el-form-item>
                
                <el-divider content-position="left">乙方信息</el-divider>
                
                <el-form-item label="税地名称：" prop="secondName">
                    <el-input v-model="createForm.secondName" placeholder="请输入税地名称"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="createDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitCreateForm" :loading="createLoading">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { getContractList, createContract } from '@/api/contract.js'
import {cdnHost} from '@/config/env.js'
export default {
    data() {
        return {
            form: {
                contractName: '',
                contractNo: '',
                signatoryName: '',
                status: ''  
            },
            contractStatusMap: {
                1: {
                    type: 'info',
                    label: '待制作'
                },
                2: {
                    type: 'warning',
                    label: '待认证'
                },
                3: {
                    type: 'warning',
                    label: '认证中'
                },
                4: {
                    type: 'warning',
                    label: '待发起'
                },
                5: {
                    type: 'warning',
                    label: '待签署'
                },
                6: {
                    type: 'success',
                    label: '已签署'
                },
                7: {
                    type: 'danger',
                    label: '已取消'
                }
            },
            tableData: {
                records: [],
                currentPage: 1,
                pageSize: 10,
                total: 0
            },
            // 创建合同相关数据
            createDialogVisible: false,
            createLoading: false,
            createForm: {
                contractName: '共享经济服务协议',
                contractType: 1,
                firstName: '',
                firstSignPhone: '',
                secondName: ''
            },
            createRules: {
                contractType: [
                    { required: true, message: '请选择合同类型', trigger: 'change' }
                ],
                firstName: [
                    { required: true, message: '请输入企业名称', trigger: 'blur' }
                ],
                firstSignPhone: [
                    { required: true, message: '请输入签署手机号', trigger: 'blur' },
                    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
                ],
                secondName: [
                    { required: true, message: '请输入税地名称', trigger: 'blur' }
                ]
            }
        }
    },
    created() {
        this.getContractList()
    },
    methods: {
        // 显示创建合同对话框
        showCreateDialog() {
            this.createDialogVisible = true;
            // 重置表单
            if (this.$refs.createFormRef) {
                this.$refs.createFormRef.resetFields();
            }
        },
        
        // 提交创建合同表单
        submitCreateForm() {
            this.$refs.createFormRef.validate(valid => {
                if (valid) {
                    this.createLoading = true;
                    createContract(this.createForm).then(res => {
                        this.createLoading = false;
                        if (res.data.code === 0) {
                            this.$message.success('创建合同成功');
                            this.createDialogVisible = false;
                            this.getContractList(); // 刷新列表
                        }
                    }).catch(err => {
                        this.createLoading = false;
                        this.$message.error('创建合同失败：' + err.message);
                    });
                }
            });
        },
        
        handleCurrentPageChange(currentPage) {
            this.tableData.currentPage = currentPage
            this.getContractList()  
        },
        queryOnboardingList() {
            this.tableData.currentPage = 1
            this.getContractList()
        },
        getContractList() {
            getContractList({
                ...this.form, 
                currentPage: this.tableData.currentPage, 
                pageSize: this.tableData.pageSize
            }).then(res => {
                if(res.data.code == 0) {
                    this.tableData.records = res.data.data.records
                    this.tableData.total = res.data.data.total
                }
            })
        },
        downloadAttachment(url) {
            if(url == null || url == '') {
                this.$message.warning('暂无附件')
                return
            }
            if(url.startsWith('http')) {
                window.open(url)
            } else {
                if(url.charAt(0) === '/') {
                    url = url.substring(1)
                }
                window.open(cdnHost + url)
            }
        },
        viewDetail(row) {
            this.$router.push({ path: '/contract/detail', query: { id: row.id } })
        },
        toChangeTime(date) {
            if (date) {
                this.form.createTimeStart = date[0] + ' ' + '00:00:00';
                this.form.createTimeEnd = date[1] + ' ' + '23:59:59';
            } else {
                this.form.createTimeStart = '';
                this.form.createTimeEnd = '';
            }
        },
    }
}
</script>