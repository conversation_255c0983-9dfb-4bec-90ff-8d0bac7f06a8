<template>
    <div>
        <el-card shadow='never'>
            <div slot='header' class='clearfix'>
                <el-row :span="24">
                    <el-col :span="12">
                        <el-button type="text" @click="navigatorBack" style="font-size: 19px;"><i class="el-icon-d-arrow-left"></i> 返回</el-button>
                        <el-divider direction="vertical"></el-divider>
                        <span>合同详情</span>
                    </el-col>
                    <el-col :span="12" style="text-align: right;">
                        <el-button type='info' v-if='contractDetail.status === 1' icon='el-icon-edit' @click='handleMakeTemplate'>制作模板</el-button>
                        <el-button type="info" v-if='contractDetail.status === 1' icon='el-icon-check' @click='handleConfirmTemplate'>确认模板</el-button>
                        <el-button type="primary" v-if='contractDetail.status === 2 || contractDetail.status === 3' icon='el-icon-s-custom' @click='handleInitiateAuth'>邀请认证</el-button>
                        <el-button type="success" v-if='contractDetail.status === 4' icon='el-icon-s-check' @click='handleInitiateSigning'>发起签约</el-button>
                        <el-button type="danger" v-if='contractDetail.status < 5' icon='el-icon-close' @click='handleCancelContract'>取消合同</el-button>
                    </el-col>
                </el-row>
            </div>
            
            <!-- 添加步骤条 -->
            <el-row v-if="contractDetail.status !== 7" style="margin: 0px 0 50px;">
                <el-col :span="24">
                    <el-steps :active="activeStep" finish-status="success" align-center>
                        <el-step 
                            title="创建合同" 
                            description="创建合同基本信息"
                            icon="el-icon-document-add">
                        </el-step>
                        <el-step 
                            title="制作模板" 
                            description="设计并确认合同模板内容"
                            icon="el-icon-edit-outline">
                        </el-step>
                        <el-step 
                            title="邀请认证" 
                            description="向企业发送身份认证邀请短信"
                            icon="el-icon-user">
                        </el-step>
                        <el-step 
                            title="发起签约" 
                            description="向企业发送签约邀请短信"
                            icon="el-icon-position">
                        </el-step>
                        <el-step 
                            title="企业签署"
                            description="企业通过短信链接签署在线合同"
                            icon="el-icon-s-check">
                        </el-step>
                        <el-step
                            title="文件下载"
                            description="系统下载合同及存证文件"
                            icon="el-icon-download">
                        </el-step>
                    </el-steps>
                </el-col>
            </el-row>

            <el-row>
                <el-col :span="24">
                    <el-form label-width="120px" :model="contractDetail">
                        <el-row>
                            <el-col :span="5">
                                <el-form-item label="合同名称:">
                                    <span>{{ contractDetail.contractName }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="7">
                                <el-form-item label="合同编号:">
                                    <span>{{ contractDetail.contractNo }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="创建时间:">
                                    <span>{{ contractDetail.createTime }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6" v-if="contractDetail.contractAttachmentUrl">
                                <el-form-item label="合同文件:">
                                    <el-button 
                                        type="text" 
                                        @click="downloadAttachment(contractDetail.contractAttachmentUrl)"
                                        style="font-size: 13px;">
                                        下载
                                    </el-button>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="5">
                                <el-form-item label="状态:">
                                    <el-tag :type="contractStatusMap[contractDetail.status].type">
                                        {{ contractStatusMap[contractDetail.status].label }}
                                    </el-tag>
                                </el-form-item>
                            </el-col>
                            <el-col :span="7">
                                <el-form-item label="模板编号:">
                                    <div style="display: inline-flex; align-items: center;">
                                        <span>{{ contractDetail.templateNo }}</span>
                                        <el-button v-if="contractDetail.status > 1 && contractDetail.status < 5"
                                            type="text"
                                            icon="el-icon-view"
                                            @click="handlePreviewTemplate"
                                            style="padding: 0 0 0 5px; height: 20px;">查看</el-button>
                                    </div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="创建人:">
                                    <span>{{ contractDetail.creatorName }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6" v-if="contractDetail.reportAttachmentUrl">
                                <el-form-item label="存证文件:">
                                    <el-button 
                                        type="text" 
                                        @click="downloadAttachment(contractDetail.reportAttachmentUrl)"
                                        style="font-size: 13px;">
                                        下载
                                    </el-button>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </el-col>
            </el-row>

            <el-row>
                <el-col :span="12">
                    <el-divider content-position="left">甲方信息</el-divider>
                    <el-row>
                        <el-col :span="24">
                            <el-form label-width="120px" :model="contractDetail">
                                <el-form-item label="企业名称:">
                                    <span>{{ contractDetail.firstName }}</span>
                                </el-form-item>
                                <el-form-item label="签署手机号:">
                                    <div style="display: inline-flex; align-items: center;">
                                        <span>{{ contractDetail.firstSignPhone || '--' }}</span>
                                        <el-button 
                                            v-if="contractDetail.status < 4 && contractDetail.status > 0 && contractDetail.firstSignPhone" 
                                            type="text" 
                                            icon="el-icon-edit" 
                                            @click="handleEditPhone('first')" 
                                            style="padding: 0 0 0 5px; height: 20px;"></el-button>
                                    </div>
                                </el-form-item>
                                <el-form-item label="签署方式:">
                                    <span>{{ contractDetail.firstSignType === 1 ? '手动签约' : '静默签约' }}</span>
                                </el-form-item>
                                <el-form-item label="认证状态:">
                                    {{ contractDetail.firstAuthenticated ? '已认证' : '未认证' }}
                                </el-form-item>
                                <el-form-item label="签署状态:">
                                    {{ contractDetail.firstSigned ? '已签署' : '未签署' }}
                                </el-form-item>
                                <el-form-item label="签署时间:" v-if="contractDetail.firstSignedTime">
                                    <span>{{ contractDetail.firstSignedTime }}</span>
                                </el-form-item>
                            </el-form>
                        </el-col>
                    </el-row>
                </el-col>
                <el-col :span="12">
                    <el-divider content-position="left">乙方信息</el-divider>
                    <el-row>
                        <el-col :span="24">
                            <el-form label-width="120px" :model="contractDetail">
                                <el-form-item label="企业名称:">
                                    <span>{{ contractDetail.secondName }}</span>
                                </el-form-item>
                                <el-form-item label="签署手机号:">
                                    <div style="display: inline-flex; align-items: center;">
                                        <span>{{ contractDetail.secondSignPhone || '--' }}</span>
                                        <el-button 
                                            v-if="contractDetail.status < 4 && contractDetail.status > 0 && contractDetail.secondSignPhone" 
                                            type="text" 
                                            icon="el-icon-edit" 
                                            @click="handleEditPhone('second')" 
                                            style="padding: 0 0 0 5px; height: 20px;"></el-button>
                                    </div>
                                </el-form-item>
                                <el-form-item label="签署方式:">
                                    <span>{{ contractDetail.secondSignType === 1 ? '手动签约' : '静默签约' }}</span>
                                </el-form-item>
                                <el-form-item label="认证状态:">
                                    {{ contractDetail.secondAuthenticated ? '已认证' : '未认证' }}
                                </el-form-item>
                                <el-form-item label="签署状态:">
                                    {{ contractDetail.secondSigned ? '已签署' : '未签署' }}
                                </el-form-item>
                                <el-form-item label="签署时间:" v-if="contractDetail.secondSignedTime">
                                    <span>{{ contractDetail.secondSignedTime }}</span>
                                </el-form-item>
                            </el-form>
                        </el-col>
                    </el-row>
                </el-col>
            </el-row>
        </el-card>
        <el-dialog title="编辑签署手机号" :visible.sync="phoneDialogVisible" width="30%" :close-on-click-modal="false">
            <el-form :model="phoneForm" ref="phoneForm" :rules="phoneRules" label-width="0">
                <el-form-item prop="phone">
                    <el-input v-model="phoneForm.phone" placeholder="请输入手机号" maxlength="11"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="phoneDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitEditPhone">确定</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import {
    getContractDetail,
    getTemplateMakingUrl,
    confirmTemplateMakingResult,
    initiatePartyAuthentication,
    initiateContractSigning,
    getTemplatePreviewUrl,
    updateSignPhone,
    cancelContract
} from '@/api/contract'
import {cdnHost} from '@/config/env.js'
export default {
    name: 'ContractDetail',
    data() {
        return {
            phoneDialogVisible: false,
            phoneForm: {
                phone: '',
                type: ''
            },
            phoneRules: {
                phone: [
                    { required: true, message: '请输入手机号', trigger: 'blur' },
                    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
                ]
            },
            contractId: '',
            contractDetail: {},
            contractStatusMap: {
                1: {
                    type: 'info',
                    label: '待制作'
                },
                2: {
                    type: 'warning',
                    label: '待认证'
                },
                3: {
                    type: 'warning',
                    label: '认证中'
                },
                4: {
                    type: 'warning',
                    label: '待发起'
                },
                5: {
                    type: 'warning',
                    label: '待签署'
                },
                6: {
                    type: 'success',
                    label: '已签署'
                },
                7: {
                    type: 'danger',
                    label: '已取消'
                }
            }
        }
    },
    computed: {
        activeStep() {
            if (this.contractDetail.reportAttachmentUrl && this.contractDetail.contractAttachmentUrl) {
                return 6; // 已下载
            }

            const status = this.contractDetail.status;
            // 根据合同状态返回当前步骤
            const statusStepMap = {
                1: 1,  // 待制作：显示到第一步
                2: 2,  // 待认证：显示到第二步
                3: 2,  // 认证中：显示到第二步
                4: 3,  // 待发起：显示到第三步
                5: 4,  // 待签署：显示到第四步
                6: 5   // 已签署：显示到第五步
            };
            return statusStepMap[status] || 0;
        }
    },
    created() {
        this.contractId = this.$route.query.id
        this.getContractDetail()
    },
    methods: {
        navigatorBack() {
            this.$router.replace({
                path: '/contract/list',
                query: {
                    current_page: this.$route.query.list_page
                }
            });
        },
        getContractDetail() {
            getContractDetail(this.contractId).then(res => {
                this.contractDetail = res.data.data
            })
        },
        downloadAttachment(url) {
            if(url == null || url == '') {
                this.$message.warning('暂无附件')
                return
            }
            if(url.startsWith('http')) {
                window.open(url)
            } else {
                if(url.charAt(0) === '/') {
                    url = url.substring(1)
                }
                window.open(cdnHost + url)
            }
        },
        async handleMakeTemplate() {
            try {
                const res = await getTemplateMakingUrl(this.contractDetail.templateNo);
                console.log(res)
                if (res.data.code === 0) {
                    window.open(res.data.data, '_blank');
                } else {
                    this.$message.error(res.data.data.msg || '获取模板制作地址失败');
                }
            } catch (error) {
                this.$message.error('获取模板制作地址失败');
            }
        },
        async handleConfirmTemplate() {
            this.$confirm('确认已完成模板制作？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const res = await confirmTemplateMakingResult(this.contractId);
                if (res.data.code === 0) {
                    this.$message.success('模板制作成功');
                    this.getContractDetail();
                } else {
                    this.$message.error(res.data.data.msg || '模板制作失败');
                }
            });
        },
        async handleInitiateAuth() {
            this.$confirm('确认发起认证邀请？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const res = await initiatePartyAuthentication(this.contractId);
                if (res.data.code === 0) {
                    this.$message.success('认证邀请发送成功');
                    this.getContractDetail();
                } else {
                    this.$message.error(res.data.data.msg || '认证邀请发送失败');
                }
            });
        },
        async handleInitiateSigning() {
            this.$confirm('确认发起签约？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const res = await initiateContractSigning(this.contractId);
                if (res.data.code === 0) {
                    this.$message.success('发起签约成功');
                    this.getContractDetail();
                } else {
                    this.$message.error(res.data.data.msg || '发起签约失败');
                }
            });
        },
        async handlePreviewTemplate() {
            try {
                const res = await getTemplatePreviewUrl(this.contractDetail.templateNo);
                if (res.data.code === 0 && res.data.data) {
                    window.open(res.data.data, '_blank');
                } else {
                    this.$message.error(res.data.data.msg || '获取模板预览地址失败');
                }
            } catch (error) {
                this.$message.error('获取模板预览地址失败');
            }
        },
        handleEditPhone(type) {
            this.phoneForm.type = type;
            this.phoneForm.phone = type === 'first' ? this.contractDetail.firstSignPhone : this.contractDetail.secondSignPhone;
            this.phoneDialogVisible = true;
        },
        async submitEditPhone() {
            try {
                await this.$refs.phoneForm.validate();
                const res = await updateSignPhone({
                    contractId: this.contractId,
                    firstSignPhone: this.phoneForm.type === 'first' ? this.phoneForm.phone : null,
                    secondSignPhone: this.phoneForm.type === 'second' ? this.phoneForm.phone : null
                });
                if (res.data.code === 0) {
                    this.$message.success('手机号更新成功');
                    this.phoneDialogVisible = false;
                    this.getContractDetail();
                } else {
                    this.$message.error(res.data.data.msg || '手机号更新失败');
                }
            } catch (error) {
                // ignore
            }
        },
        async handleCancelContract() {
            this.$confirm('确认取消当前合同？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const res = await cancelContract(this.contractId);
                if (res.data.code === 0) {
                    this.$message.success('合同取消成功');
                    this.getContractDetail();
                } else {
                    this.$message.error(res.data.data.msg || '合同取消失败');
                }
            });
        }
    }
}
</script>

<style lang="scss" scoped>
.el-steps {
    padding: 10px 0;
}

::v-deep .el-step__title {
    font-size: 15px;
    font-weight: 500;
    
    &.is-success {
        color: #67c23a;
    }
    &.is-process {
        color: #409eff;
        font-weight: 600;
    }
    &.is-wait {
        color: #909399;
    }
}

::v-deep .el-step__description {
    font-size: 13px;
    
    &.is-success {
        color: #67c23a;
    }
    &.is-process {
        color: #409eff;
    }
    &.is-wait {
        color: #909399;
    }
}

::v-deep .el-step__icon {
    &.is-success {
        background-color: #f0f9eb;
        border-color: #67c23a;
        
        i {
            color: #67c23a;
        }
    }
    &.is-process {
        background-color: #ecf5ff;
        
        i {
            color: #409eff;
        }
    }
}

::v-deep .el-step__line {
    background-color: #ebeef5;
}

::v-deep .el-step.is-success .el-step__line {
    background-color: #67c23a;
}
</style>