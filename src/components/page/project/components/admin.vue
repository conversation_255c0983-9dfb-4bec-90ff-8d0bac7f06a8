<template>
    <div>
        <el-dialog title="选择管理员" :visible.sync="toshowDia" @close="closeDia">
            <el-table :data="adminList" style="color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                <el-table-column property="id" label="账号ID"></el-table-column>
                <el-table-column property="account" label="账号"></el-table-column>
                <el-table-column property="wechatNickName" label="绑定微信"></el-table-column>
                <el-table-column label="角色" property="roleName"> </el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-button @click="handleClick(scope.row)" type="text">选择</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>
    </div>
</template>

<script>
import auth from '@/api/auth.js';
export default {
    data() {
        return {
            gridData: [
        
            ],
            toshowDia: false,
            headers: {
                Authorization: 'Bearer ' + auth.curUser().access_token
            }
        };
    },
    props: {
        dialogVisible: {
            type: Boolean,
            require: true
        },
        adminList: {
            type: Array,
            require: true
        }
    },
    watch: {
        dialogVisible: {
            handler(newVal) {
                this.toshowDia = newVal;
            }
        }
    },
    methods: {
        handleClick(row) {
            let adminIds = [];
            this.$confirm('确认添加该管理员?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    adminIds.push(row.id);
                    this.$emit('toChooseAdmin', adminIds);
                })
                .catch(() => {});
        },
        closeDia() {
            this.$emit('closeDia');
        },
        onChangeFile(file) {
            this.fileName = file.raw.name;
            if (file.raw.type.indexOf('image/') == -1) {
                this.$message.error('文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。');
            }
            // 本地图片转成base64，用于截图框显示本地图片
            this.imageToBase64(file.raw);
        },
        //成功上传头像
        handleAvatarSuccess(res, file) {
            this.previews.url = URL.createObjectURL(file.raw);
        }
    }
};
</script>

<style scoped></style>
