<template>
    <div>
        <el-dialog title="扫码加入小组" :visible.sync="toshowDia" @close="closeDia">
            <div class="qrcode_box">
                <div class="top_title">
                    微信扫一扫，加入<span style="color: #2697ff">工作组</span>
                    <img :src="qrcodeCom.url" alt="" />
                    <div class="time_or_name">有效期至{{ qrcodeCom.overTime }}</div>
                    <div class="time_or_name">{{ qrcodeCom.projectName }} 「{{ qrcodeCom.groupName }}」</div>
                    <i class="el-icon-refresh-right" @click="toNewQrcode"></i>
                </div>
                <div style="flex: 1">
                    <div class="demo-input-suffix">
                        <el-input placeholder="请输入姓名/手机号/身份证号" v-model="input4" @blur="getPeopleList">
                            <i slot="prefix" class="el-input__icon el-icon-search"></i>
                        </el-input>
                        <el-button type="primary" style="margin-left: 20px">搜索</el-button>
                    </div>
                    <div class="people_box" v-for="item in peopleList" :key="item.workerId">
                        <div class="people_item">
                            <img :src="item.avatar" alt="" v-if="item.avatar" />
                            <el-avatar v-if="!item.avatar"> {{ item.name }} </el-avatar>
                            <div class="name">{{ item.name }}</div>
                            <el-button type="primary" icon="el-icon-check" circle @click="toSureInGroup(item.workerId)"></el-button>
                            <el-button type="danger" icon="el-icon-close" circle @click="toCancelInGroup(item.workerId)"></el-button>
                        </div>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { listWaitWorker, groupQrcode, updateWorkerStatus } from '@/api/project/project.js';
import { Message } from 'element-ui';

export default {
    data() {
        return {
            toshowDia: false,
            input4: '',
            groupId: '',
            peopleList: [],
            qrcodeCom: {}
        };
    },
    props: {
        dialogVisible: {
            type: Boolean,
            require: true
        },
        qrcode: {
            type: Object,
            require: true
        },
        nowGroupId: {
            type: String,
            require: true
        }
    },
    watch: {
        dialogVisible: {
            handler(newVal) {
                this.toshowDia = newVal;
            }
        },
        nowGroupId: {
            handler(val) {
                this.groupId = val;
                this.getPeopleList();
            }
        },
        qrcode: {
            handler(val) {
                this.qrcodeCom = val;
            }
        }
    },
    methods: {
        closeDia() {
            this.$emit('closeDia');
        },
        getPeopleList() {
            listWaitWorker({
                groupId: this.groupId,
                search: this.input4
            }).then((res) => {
                if (res.data.data) {
                    this.peopleList = res.data.data;
                }
            });
        },
        toNewQrcode() {
            groupQrcode({
                sceneId: this.groupId
            }).then((res) => {
                console.log(res);
                if (res.data.code === 0) {
                    Message.success('更新成功');
                    this.qrcodeCom = res.data.data;
                } else {
                    Message({
                        message: res.data.msg,
                        type: 'error'
                    });
                }
            });
        },
        toSureInGroup(id) {
            this.$confirm(`确定要同意进组？`, '提示', {
                type: 'warning'
            })
                .then(() => {
                    updateWorkerStatus({
                        auditStatus: 3,
                        groupId: this.groupId,
                        workerId: id
                    }).then((res) => {
                        if (res.data.code == 0) {
                            Message.success(res.data.msg);
                            this.getPeopleList();
                            this.$emit('sureInGroup');
                        }
                    });
                })
                .catch(() => {});
        },
        toCancelInGroup(id) {
            this.$confirm(`确定要拒绝进组？`, '提示', {
                type: 'warning'
            })
                .then(() => {
                    updateWorkerStatus({
                        auditStatus: 2,
                        groupId: this.groupId,
                        workerId: id
                    }).then((res) => {
                        if (res.data.code == 0) {
                            Message.success(res.data.msg);
                            this.getPeopleList();
                        }
                    });
                })
                .catch(() => {});
        }
    }
};
</script>

<style scoped>
.qrcode_box {
    display: flex;
}
.top_title img {
    width: 200px;
    height: 200px;
    display: block;
    margin: 0 auto;
    margin-top: 10px;
}
.top_title {
    text-align: center;
    margin-right: 30px;
}
.time_or_name {
    margin: 5px 0;
}
.demo-input-suffix {
    display: flex;
    margin-bottom: 20px;
}
.people_item {
    display: flex;
    align-items: center;
}
.people_item img {
    width: 40px;
    height: 40px;
    margin-right: 15px;
    border-radius: 50%;
}
.people_item .name {
    flex: 1;
}
</style>
