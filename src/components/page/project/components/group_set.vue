<template>
    <div>
        <el-dialog :title="addGroup ? '工作组设置' : '工作组添加'" :visible.sync="toshowDia" @close="closeDia">
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
                <el-form-item label="小组名称" prop="name">
                    <el-input v-model="ruleForm.name" @input="toUpdataInfo"></el-input>
                </el-form-item>
                <el-form-item label="税源地" prop="name">
                    <el-select v-model="ruleForm.taxesId" filterable clearable :disabled="addGroup ? true : false">
                        <el-option v-for="item in taxesList" :key="item.taxesId" :label="item.taxesName" :value="item.taxesId"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="自动审核">
                    <el-switch v-model="ruleForm.isAutomaticAudit" :active-value="1" :inactive-value="0" active-text="开启"></el-switch>
                </el-form-item>
            </el-form>
            <div class="button_box">
                <el-button type="danger" v-if="addGroup" @click="toRemoveGroup">解散小组</el-button>
                <div>
                    <el-button @click="closeDia">取消</el-button>
                    <el-button type="primary" @click="toSureGroup">确定</el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { getTaxesList } from '@/api/project/project.js';
export default {
    data() {
        return {
            toshowDia: false,
            ruleForm: {
                name: ''
            },
            addGroup: undefined,
            rules: {
                name: [
                    { required: true, message: '请输入小组名称', trigger: 'blur' },
                    { min: 1, max: 40, message: '小组名称最多40个字', trigger: 'blur' }
                ]
            },
            taxesList: []
        };
    },
    props: {
        dialogVisible: {
            type: Boolean,
            require: true
        },
        groupInfo: {
            type: Object,
            require: true
        }
    },
    watch: {
        dialogVisible: {
            handler(newVal) {
                this.toshowDia = newVal;
            }
        },
        groupInfo: {
            handler(newVal) {
                if (newVal) {
                    this.ruleForm = JSON.parse(JSON.stringify(newVal));
                    this.ruleForm.name = this.ruleForm.groupName;
                } else {
                    this.ruleForm = {};
                }
                this.addGroup = newVal;
            }
        }
    },
    methods: {
        closeDia() {
            this.$emit('closeDia');
        },
        toSureGroup() {
            let data = {
                groupName: this.ruleForm.name,
                taxesId: this.ruleForm.taxesId,
                isAutomaticAudit:this.ruleForm.isAutomaticAudit
            };
            if (this.addGroup) {
                this.$emit('editGroup', data);
            } else {
                this.$emit('addGroup', data);
            }
            this.ruleForm.name = '';
            this.ruleForm.taxesId = '';
        },
        toUpdataInfo() {
            this.$forceUpdate();
        },
        toRemoveGroup() {
            this.$emit('toShowCode');
        }
    },
    created() {
        getTaxesList({
            enterpriseId: this.$route.query.enterpriseId
        }).then((res) => {
            this.taxesList = res.data.data;
        });
    }
};
</script>

<style scoped>
.button_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
</style>
