<template>
    <div>
        <el-dialog
            :visible.sync="dialogVisible"
            @close="handleClose"
            width="60%"
            :close-on-click-modal="false"
            :show-close="true"
        >
            <div v-if="!hasValidContent" class="no-content">
                <el-alert
                    title="暂无变更内容"
                    type="info"
                    :closable="false"
                    show-icon>
                </el-alert>
            </div>

            <div v-else>
                <!-- 项目基本信息 -->
                <div v-if="parsedChangedContent.project" class="section">
                    <h3 class="section-title">项目信息</h3>
                    <el-form label-width="120px" class="diff-form">
                        <el-row :gutter="16">
                            <el-col :span="12">
                                <el-form-item label="项目名称：">
                                    <div class="field-content" :class="getFieldClass('project.name')">
                                        {{ parsedChangedContent.project.name || '-' }}
                                        <span v-if="isFieldChanged('project.name')" class="change-indicator">
                                            (原: {{ getOriginalValue('project.name') }})
                                        </span>
                                    </div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="项目编码：">
                                    <div class="field-content" :class="getFieldClass('project.code')">
                                        {{ parsedChangedContent.project.code || '-' }}
                                        <span v-if="isFieldChanged('project.code')" class="change-indicator">
                                            (原: {{ getOriginalValue('project.code') }})
                                        </span>
                                    </div>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-form-item label="项目描述：">
                            <div class="field-content" :class="getFieldClass('project.description')">
                                {{ parsedChangedContent.project.description || '-' }}
                                <span v-if="isFieldChanged('project.description')" class="change-indicator">
                                    (原: {{ getOriginalValue('project.description') }})
                                </span>
                            </div>
                        </el-form-item>

                        <el-row :gutter="16">
                            <el-col :span="8">
                                <el-form-item label="项目人数：">
                                    <div class="field-content" :class="getFieldClass('project.workerNumber')">
                                        {{ parsedChangedContent.project.workerNumber || '-' }}
                                        <span v-if="isFieldChanged('project.workerNumber')" class="change-indicator">
                                            (原: {{ getOriginalValue('project.workerNumber') }})
                                        </span>
                                    </div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="项目报酬：">
                                    <div class="field-content" :class="getFieldClass('project.reward')">
                                        {{ parsedChangedContent.project.reward || '-' }}元
                                        <span v-if="isFieldChanged('project.reward')" class="change-indicator">
                                            (原: {{ getOriginalValue('project.reward') }}元)
                                        </span>
                                    </div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="结算方式：">
                                    <div class="field-content" :class="getFieldClass('project.rewardRule')">
                                        {{ getRewardRuleText(parsedChangedContent.project.rewardRule) }}
                                        <span v-if="isFieldChanged('project.rewardRule')" class="change-indicator">
                                            (原: {{ getRewardRuleText(getOriginalValue('project.rewardRule')) }})
                                        </span>
                                    </div>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row :gutter="16">
                            <el-col :span="12">
                                <el-form-item label="开始日期：">
                                    <div class="field-content" :class="getFieldClass('project.startDate')">
                                        {{ parsedChangedContent.project.startDate || '-' }}
                                        <span v-if="isFieldChanged('project.startDate')" class="change-indicator">
                                            (原: {{ getOriginalValue('project.startDate') }})
                                        </span>
                                    </div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="结束日期：">
                                    <div class="field-content" :class="getFieldClass('project.endDate')">
                                        {{ parsedChangedContent.project.endDate || '-' }}
                                        <span v-if="isFieldChanged('project.endDate')" class="change-indicator">
                                            (原: {{ getOriginalValue('project.endDate') }})
                                        </span>
                                    </div>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row :gutter="16">
                            <el-col :span="12">
                                <el-form-item label="众包大厅展示：">
                                    <div class="field-content" :class="getFieldClass('project.openOption')">
                                        {{ getOpenOptionText(parsedChangedContent.project.openOption) }}
                                        <span v-if="isFieldChanged('project.openOption')" class="change-indicator">
                                            (原: {{ getOpenOptionText(getOriginalValue('project.openOption')) }})
                                        </span>
                                    </div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="允许申请：">
                                    <div class="field-content" :class="getFieldClass('project.canApply')">
                                        {{ parsedChangedContent.project.canApply ? '是' : '否' }}
                                        <span v-if="isFieldChanged('project.canApply')" class="change-indicator">
                                            (原: {{ getOriginalValue('project.canApply') ? '是' : '否' }})
                                        </span>
                                    </div>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-form-item label="详细地址：">
                            <div class="field-content" :class="getFieldClass('project.address')">
                                {{ parsedChangedContent.project.address || '-' }}
                                <span v-if="isFieldChanged('project.address')" class="change-indicator">
                                    (原: {{ getOriginalValue('project.address') }})
                                </span>
                            </div>
                        </el-form-item>

                    </el-form>
                </div>

                <!-- 开票类目信息 -->
                <div v-if="allInvoiceCategories.length > 0" class="section">
                    <h3 class="section-title">开票类目</h3>
                    <div class="invoice-categories">
                        <div
                            v-for="(category, index) in allInvoiceCategories"
                            :key="category.id || index"
                            class="category-item"
                            :class="getCategoryDiffClass(category)"
                        >
                            <div class="category-name">{{ category.name }}</div>
                            <div class="category-taxes">
                                ({{ category.taxesName }})
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 项目图片 -->
                <div v-if="parsedChangedContent.images && parsedChangedContent.images.length > 0" class="section">
                    <h3 class="section-title">项目图片</h3>
                    <div class="images-grid">
                        <div
                            v-for="(image, index) in parsedChangedContent.images"
                            :key="image.url || index"
                            class="image-item"
                            :class="getImageClass(image, index)"
                        >
                            <el-image
                                style="width: 100px; height: 100px; border-radius: 4px;"
                                :src="getImageUrl(image.url)"
                                :preview-src-list="[getImageUrl(image.url)]"
                                fit="cover"
                            >
                                <div slot="error" class="image-slot">
                                    <i class="el-icon-picture-outline"></i>
                                </div>
                            </el-image>
                            <div class="image-name">{{ image.name }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <div slot="footer" class="dialog-footer">
                <el-button @click="handleClose">关闭</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { OSS_URL } from '@/api/config';

export default {
    name: 'ProjectDiff',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        originalContent: {
            type: String,
            default: ''
        },
        changedContent: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            dialogVisible: false,
            parsedOriginalContent: {},
            parsedChangedContent: {},
            ossUrl: OSS_URL
        };
    },
    computed: {
        hasValidContent() {
            return this.changedContent && Object.keys(this.parsedChangedContent).length > 0;
        },

        // 合并所有开票类目（包括新增、保留、删除的）
        allInvoiceCategories() {
            try {
                const changedCategories = (this.parsedChangedContent && this.parsedChangedContent.invoiceCategories) || [];
                const originalCategories = (this.parsedOriginalContent && this.parsedOriginalContent.invoiceCategories) || [];

                // 如果没有原始内容，直接返回变更后的类目（标记为新增）
                if (!this.originalContent) {
                    return changedCategories.map(category => ({
                        ...category,
                        status: ''
                    }));
                }

                const result = [];

                // 添加变更后的类目（新增或保留）
                if (Array.isArray(changedCategories)) {
                    changedCategories.forEach(category => {
                        if (category && category.id) {
                            const existsInOriginal = Array.isArray(originalCategories) &&
                                originalCategories.find(orig => orig && orig.id === category.id);
                            result.push({
                                ...category,
                                status: existsInOriginal ? null : '新增' // 如果原来没有则标记为新增
                            });
                        }
                    });
                }

                // 添加被删除的类目
                if (Array.isArray(originalCategories)) {
                    originalCategories.forEach(originalCategory => {
                        if (originalCategory && originalCategory.id) {
                            const existsInChanged = Array.isArray(changedCategories) &&
                                changedCategories.find(changed => changed && changed.id === originalCategory.id);
                            if (!existsInChanged) {
                                result.push({
                                    ...originalCategory,
                                    status: '已删除'
                                });
                            }
                        }
                    });
                }

                return result;
            } catch (error) {
                console.error('计算 allInvoiceCategories 时出错:', error);
                return [];
            }
        }
    },
    watch: {
        visible: {
            handler(newVal) {
                this.dialogVisible = newVal;
                if (newVal) {
                    this.parseContent();
                    // 暂时注释掉，避免错误
                    // this.loadInvoiceCategories();
                }
            },
            immediate: true
        }
    },
    methods: {
        // 解析JSON内容
        parseContent() {
            try {
                this.parsedChangedContent = this.changedContent ? JSON.parse(this.changedContent) : {};
            } catch (e) {
                console.error('解析changedContent失败:', e);
                this.parsedChangedContent = {};
            }

            try {
                this.parsedOriginalContent = this.originalContent ? JSON.parse(this.originalContent) : {};
            } catch (e) {
                console.error('解析originalContent失败:', e);
                this.parsedOriginalContent = {};
            }
        },

        // 获取嵌套对象的值
        getNestedValue(obj, path) {
            return path.split('.').reduce((current, key) => {
                return current && current[key] !== undefined ? current[key] : undefined;
            }, obj);
        },

        // 检查字段是否有变更
        isFieldChanged(fieldPath) {
            if (!this.originalContent) return false;

            const originalValue = this.getNestedValue(this.parsedOriginalContent, fieldPath);
            const changedValue = this.getNestedValue(this.parsedChangedContent, fieldPath);

            return originalValue !== changedValue;
        },

        // 获取原始值
        getOriginalValue(fieldPath) {
            return this.getNestedValue(this.parsedOriginalContent, fieldPath) || '--';
        },

        // 获取字段样式类
        getFieldClass(fieldPath) {
            return this.isFieldChanged(fieldPath) ? 'field-changed' : '';
        },

        // 获取结算方式文本
        getRewardRuleText(value) {
            const rules = {
                1: '人/项目',
                2: '人/小时',
                3: '人/天',
                4: '人/周',
                5: '人/月',
                6: '人/季',
                7: '人/年'
            };
            return rules[value] || '-';
        },

        // 获取众包大厅展示选项文本
        getOpenOptionText(value) {
            const options = {
                0: '不可展示和搜索',
                1: '可展示和搜索',
                2: '不展示，仅可搜索'
            };
            return options[value] || '-';
        },

        // 获取开票类目样式类（新方法）
        getCategoryDiffClass(category) {
            if (category.status === '新增') {
                return 'category-new';
            } else if (category.status === '已删除') {
                return 'category-deleted';
            }
            return '';
        },

        // 获取图片样式类
        getImageClass(image, index) {
            if (!this.originalContent) return '';

            const originalImages = this.parsedOriginalContent.images || [];
            const isNew = !originalImages.find(orig => orig.url === image.url);

            return isNew ? 'image-new' : '';
        },

        // 获取图片完整URL
        getImageUrl(url) {
            if (!url) return '';
            return url.startsWith('http') ? url : this.ossUrl + url;
        },
        // 关闭弹窗
        handleClose() {
            this.$emit('close');
        }
    }
};
</script>

<style scoped lang="scss">
.no-content {
    text-align: center;
    padding: 40px 0;
}

.section {
    margin-bottom: 20px;

    .section-title {
        font-size: 14px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 12px;
        padding-bottom: 6px;
        border-bottom: 1px solid #409EFF;
    }
}

.diff-form {
    // 调整表单项间距
    .el-form-item {
        margin-bottom: 12px;
    }

    // 确保标签和内容对齐
    .el-form-item__label {
        line-height: 32px;
        padding: 0 8px 0 0;
    }

    .el-form-item__content {
        line-height: 32px;
    }

    .field-content {
        padding: 6px 10px;
        border-radius: 4px;
        min-height: 20px;
        line-height: 20px;
        display: flex;
        align-items: center;

        &.field-changed {
            background-color: #fff2e8;
            border: 1px solid #ffb366;

            .change-indicator {
                color: #e6a23c;
                font-size: 12px;
                margin-left: 8px;
                flex-shrink: 0;
            }
        }
    }
}

.invoice-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;

    .category-item {
        padding: 10px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        background-color: #f5f7fa;
        min-width: 180px;

        &.category-new {
            background-color: #fff2e8;
            border-color: #ffb366;
        }

        &.category-deleted {
            background-color: #fef0f0;
            border-color: #f56c6c;
            opacity: 0.8;

            .category-name {
                text-decoration: line-through;
                color: #909399;
            }
        }

        .category-name {
            font-weight: bold;
            font-size: 13px;
            margin-bottom: 4px;
        }

        .category-taxes {
            font-size: 11px;
            color: #909399;
        }

        .category-status {
            font-size: 10px;
            margin-top: 4px;
            padding: 2px 6px;
            border-radius: 3px;
            display: inline-block;

            &.status-new {
                background-color: #e1f3d8;
                color: #67c23a;
            }

            &.status-deleted {
                background-color: #fde2e2;
                color: #f56c6c;
            }
        }
    }
}

.images-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;

    .image-item {
        text-align: center;

        &.image-new {
            padding: 6px;
            background-color: #fff2e8;
            border: 1px solid #ffb366;
            border-radius: 4px;
        }

        .image-name {
            margin-top: 6px;
            font-size: 11px;
            color: #606266;
            max-width: 100px;
            word-break: break-all;
        }
    }
}

.image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
    font-size: 30px;
}

.dialog-footer {
    text-align: right;
}
</style>