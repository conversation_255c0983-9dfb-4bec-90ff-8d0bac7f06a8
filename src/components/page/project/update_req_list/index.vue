<template>
    <div>
        <el-card>
            <el-form :inline="true" :model="requestForm" class="demo-form-inline button_box">
                <el-form-item label="企业名称">
                    <el-select v-model="requestForm.enterpriseId" filterable clearable placeholder="请选择" style='width: 230px;'>
                        <el-option v-for="item in enterpriseList" :key="item.enterpriseId" :label="item.enterpriseName" :value="item.enterpriseId">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="状态">
                    <el-select v-model="requestForm.auditStatus" placeholder="请选择状态" clearable>
                        <el-option v-for="status in statusList" :label="status.label" :value="status.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="medium" @click="search">查 询</el-button>
                </el-form-item>
                <div style="flex: 1"></div>
            </el-form>
            <el-table :data="tableData.records" style="width: 100%; color: rgba(0, 0, 0, 0.65)" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                <el-table-column label="序号" type="index" width="80" fixed="left" align="center"></el-table-column>
                <el-table-column label="企业名称" prop="enterpriseName" min-width="200"></el-table-column>
                <el-table-column label="项目名称" prop="projectName" min-width="200"></el-table-column>
                <el-table-column label="类型" prop="typeName" width="100" align="center"></el-table-column>
                <el-table-column label="变更内容" width="100" align="center">
                    <template slot-scope="scope">
                        <el-button size='mini' plain @click="viewDiff(scope.row)">查看</el-button>
                    </template>
                </el-table-column>
                <el-table-column label="审核状态" prop='auditStatusName' width="150" align="center"> </el-table-column>
                <el-table-column label="创建时间" prop='createTime' width="180" align="center"> </el-table-column>
                <el-table-column label="初审时间" prop='firstAuditTime' width="180" align="center"> </el-table-column>
                <el-table-column label="初审意见" prop='firstAuditOpinion' width="180" align="center"> </el-table-column>
                <el-table-column label="复审时间" prop='secondAuditTime'  width="180" align="center"> </el-table-column>
                <el-table-column label="复审意见" prop='secondAuditOpinion' width="180" align="center"> </el-table-column>
                <el-table-column width="130" label="操作" fixed="right" align="center">
                    <template slot-scope="scope">
                       <el-button v-if='scope.row.auditStatus === 0 || scope.row.auditStatus === 1'
                                  @click="auditUpdateReq(scope.row.id)" type="text" style="font-size: 15px">
                           {{scope.row.auditStatus === 0 ? '初审' : '复审'}}
                       </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page="tableData.current"
                    layout="prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
        </el-card>

        <!-- 变更内容差异对比弹窗 -->
        <project-diff
            :visible="showDiffDialog"
            :original-content="currentOriginalContent"
            :changed-content="currentChangedContent"
            @close="closeDiffDialog"
        />

        <!-- 审核对话框 -->
        <el-dialog title="项目变更审核" :visible.sync="showAuditDialog" width="500px">
            <el-form>
                <el-form-item label="审核意见">
                    <el-input
                        type="textarea"
                        rows="5"
                        v-model="auditComment"
                        placeholder="请填写审核意见">
                    </el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="closeAuditDialog">取 消</el-button>
                <el-button type="danger" @click="handleAudit(false)">拒 绝</el-button>
                <el-button type="primary" @click="handleAudit(true)">通 过</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { Message } from 'element-ui';
import {
    pageQueryProjectUpdateRequests,
    auditUpdateRequest,
    proEnterpriseList
} from '@/api/project/project.js';
import projectDiff from '../components/project_diff.vue';
export default {
    components: {
        projectDiff
    },
    computed: {},
    data() {
        return {
            enterpriseList: [],
            requestForm: {
                enterpriseId: "",
                auditStatus: ""
            },
            tableData: {
                records: []
            },
            current: 1,
            size: 10,
            statusList: [
                {
                    label: '待初审',
                    value: 0
                },
                {
                    label: '待复审',
                    value: 1
                },
                {
                    label: '审核通过',
                    value: 2
                },
                {
                    label: '已拒绝',
                    value: 3
                },
                {
                    label: '已取消',
                    value: 4
                }
            ],
            // 差异对比弹窗相关
            showDiffDialog: false,
            currentOriginalContent: '',
            currentChangedContent: '',
            // 审核对话框相关
            showAuditDialog: false,
            auditComment: '',
            currentAuditRequestId: ''
        };
    },
    mounted() {
        this.getProjectUpdateReqList();
        proEnterpriseList().then((res) => {
            if (res.data.code == 0) {
                this.enterpriseList = res.data.data;
            }
        });
    },
    methods: {
        // 审核变更请求
        auditUpdateReq(updateRequestId) {
            this.currentAuditRequestId = updateRequestId;
            this.auditComment = '';
            this.showAuditDialog = true;
        },
        // 查询项目变更请求列表
        getProjectUpdateReqList() {
            let data = {
                current: this.current,
                size: this.size,
                ...this.requestForm
            };
            pageQueryProjectUpdateRequests(data).then((res) => {
                if (res.data.data) {
                    if (res.data.code === 0) {
                        this.tableData = res.data.data;
                    } else {
                        Message({
                            message: res.data.msg,
                            type: 'error'
                        });
                    }
                } else {
                    this.tableData = [];
                }
            });
        },
        //分页-每页条数改变
        handleSizeChange(size) {
            this.size = size;
            this.current = 1;
            this.getProjectUpdateReqList();
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.current = current;
            this.getProjectUpdateReqList(current);
        },
        search() {
            this.current = 1;
            this.getProjectUpdateReqList();
        },
        // 查看变更内容差异
        viewDiff(row) {
            this.currentOriginalContent = row.originalContent || '';
            this.currentChangedContent = row.changedContent || '';
            this.showDiffDialog = true;
        },
        // 关闭差异对比弹窗
        closeDiffDialog() {
            this.showDiffDialog = false;
            this.currentOriginalContent = '';
            this.currentChangedContent = '';
        },
        // 关闭审核对话框
        closeAuditDialog() {
            this.showAuditDialog = false;
            this.auditComment = '';
            this.currentAuditRequestId = '';
        },
        // 处理审核操作
        handleAudit(passed) {
            if (!passed && !this.auditComment.trim()) {
                Message({
                    message: '拒绝时必须填写审核意见',
                    type: 'warning'
                });
                return;
            }

            // 添加确认对话框
            const confirmMessage = passed
                ? '确定要通过此项目变更申请吗？'
                : '确定要拒绝此项目变更申请吗？';

            this.$confirm(confirmMessage, '确认操作', {
                confirmButtonText: passed ? '确定通过' : '确定拒绝',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.submitAudit(passed);
            }).catch(() => {
                // 用户取消操作，不做任何处理
            });
        },
        // 提交审核结果
        submitAudit(passed) {
            const auditData = {
                projectUpdateRequestId: this.currentAuditRequestId,
                passed: passed
            };

            // 如果有审核意见，添加到请求参数中
            if (this.auditComment.trim()) {
                auditData.auditOpinion = this.auditComment.trim();
            }

            auditUpdateRequest(auditData).then((res) => {
                if (res.data.code === 0) {
                    Message({
                        message: passed ? '审核通过成功' : '审核拒绝成功',
                        type: 'success'
                    });
                    this.closeAuditDialog();
                    this.getProjectUpdateReqList(); // 刷新列表
                } else {
                    Message({
                        message: res.data.msg || '审核失败，请重试',
                        type: 'error'
                    });
                }
            }).catch((error) => {
                Message({
                    message: '审核失败，请重试',
                    type: 'error'
                });
            });
        }
    }
};
</script>

<style scoped lang="scss">
.btn {
    color: #ffffff;
    padding: 5px 10px;
    border-radius: 5px;
}
.button_box {
    display: flex;
    align-items: center;
}
.button_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.BMap_cpyCtrl {
    display: none !important;
}
.anchorBL {
    display: none !important;
}
.amap-logo img {
    display: none !important;
}
.amap-copyright {
    opacity: 0 !important;
}
</style>