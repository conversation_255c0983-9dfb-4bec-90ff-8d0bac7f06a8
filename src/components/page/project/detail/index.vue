<template>
    <div>
        <el-card>
            <div slot="header" class="clearfix">
                <el-button type="text" @click="navigatorBack" style="font-size: 19px;"><i class="el-icon-d-arrow-left"></i> 返回</el-button>
                <el-divider direction="vertical"></el-divider>
                <span>{{ projectDetail.projectName }}</span>
            </div>
            <div class="detail_box">
                <div class="left" shadow="never">
                    <div slot="header" class="clearfix">
                        <span>管理员</span>
                        <el-button
                            style="float: right; padding: 3px 0; font-size: 16px"
                            type="text"
                            @click="toShowAdminDia"
                            v-has="'platform_project_admin_edi'"
                            >添加</el-button
                        >
                    </div>
                    <div class="text item">
                        <div class="people_item" v-for="(item, index) in projectDetail.admins" :key="index">
                            <img :src="item.avatar" alt="" v-if="item.avatar" />
                            <el-avatar v-if="!item.avatar"> {{ item.userName }} </el-avatar>
                            {{ item.userName }}
                            <div style="flex: 1"></div>
                            <el-button
                                type="danger"
                                icon="el-icon-delete"
                                circle
                                class="toShowButton"
                                v-if="!item.sysPreset"
                                @click="toDeleteAdmin(item.userId)"
                                v-has="'platform_project_admin_edi'"
                            ></el-button>
                        </div>
                    </div>
                </div>
                <div class="right" shadow="never">
                    <div v-if="pendingList.total > 0">
                        <div slot="header" class="clearfix">
                            <span style="margin-right: 10px">待审核</span>
                            <el-tag>共：{{ pendingList.total }}人</el-tag>
                        </div>
                        <div class="clearfix" style="margin-bottom: 20px">
                            <div class="people_box" v-for="item in pendingList.records" :key="item.workerId">
                                <div class="people_item">
                                    <img :src="item.avatar" alt="" v-if="item.avatar" />
                                    <el-avatar class="avatar" v-if="!item.avatar"> {{ item.name }} </el-avatar>
                                    <div class="name">
                                        {{ item.name }}
                                        <span style="font-size: 12px; color: gray" v-if="item.groupId"> (工作组: {{ item.groupName }}) </span>
                                        <span style="font-size: 12px; color: red" v-if="!item.groupId"> (待分组) </span>
                                    </div>
                                    <el-button type="primary" icon="el-icon-check" circle @click="toSure(item.workerId, item.groupId)"></el-button>
                                    <el-button type="danger" icon="el-icon-close" circle @click="toCancel(item.workerId, item.groupId)"></el-button>
                                </div>
                            </div>
                        </div>
                        <div class="pagination">
                            <el-pagination
                                @current-change="handleCurrentChange"
                                :current-page="pendingList.current"
                                layout="prev, pager, next, jumper"
                                :total="pendingList.total"
                            ></el-pagination>
                        </div>
                    </div>
                    <div slot="header" class="clearfix">
                        <span>工作组</span>
                        <el-button
                            style="float: right; padding: 3px 0; font-size: 16px"
                            type="text"
                            @click="toUpdateProject"
                            v-has="'platform_project_add'"
                            >设置</el-button
                        >
                    </div>
                    <div class="clearfix" style="margin-bottom: 20px">
                        <el-button type="primary" style="font-size: 16px" @click="toAddGroup" v-has="'platform_project_edi'"
                            >+ 创建工作组
                        </el-button>
                        <el-button style="float: right; font-size: 16px" @click="toExcel" v-has="'platform_project_edi'">导出</el-button>
                    </div>
                    <!-- 小组列表 -->
                    <div class="group_list" v-for="(item, index) in projectDetail.groupDetails" :key="index">
                        <div class="group_top">
                            <el-tag>共：{{ item.workerNumber ? item.workerNumber : 0 }}人</el-tag>
                            <div class="name">{{ item.groupName }}</div>
                            <el-badge :value="item.pendingReview" class="item" :hidden="item.pendingReview == 0 ? true : false">
                                <el-button style="font-size: 16px" @click="toShowQrcode(item.groupId)">扫码加入</el-button>
                            </el-badge>
                            <div style="margin: 0 10px">
                                <!-- <el-badge :value="number" :max="99" class="item_number"> -->
                                <!-- <el-button style="font-size: 16px" @click="toPayGroup(item, projectDetail.projectName)">发起付款</el-button> -->
                                <!-- </el-badge> -->
                            </div>
                            <el-button style="font-size: 16px" @click="downExcel(item.groupId, item.groupName)" v-has="'platform_project_edi'"
                                >名单下载</el-button
                            >
                            <el-button style="font-size: 16px" @click="toShowGroupSet(item)" v-has="'platform_project_edi'">设置</el-button>
                        </div>
                        <div class="group_bottom">
                            <div class="item" v-for="items in item.workers" :key="items.workerId">
                                <img :src="items.avatar" alt="" v-if="items.avatar" />
                                <el-avatar v-if="!items.avatar"> {{ items.workerName }} </el-avatar>
                                <div style="margin: 0 20px 0 10px">{{ items.workerName }}</div>
                                <i
                                    class="el-icon-error group_error"
                                    @click="deleteWorkers(item.groupId, items.workerId)"
                                    v-has="'platform_project_edi'"
                                ></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </el-card>
        <admin :dialog-visible="dialogVisible" @closeDia="closeDia" :admin-list="adminList" @toChooseAdmin="toChooseAdmin"></admin>
        <qrcodes
            :dialog-visible="dialogQrcode"
            @closeDia="closeDia"
            :qrcode="qrcode"
            :nowGroupId="nowGroupId"
            @sureInGroup="sureInGroup"
        ></qrcodes>
        <groupSet
            :dialog-visible="dialogGroup"
            @closeDia="closeDia"
            :group-info="groupInfo"
            @addGroup="addGroup"
            @editGroup="editGroup"
            @toShowCode="toShowCode"
        ></groupSet>
        <projectSet
            :dialog-visible="dialogProject"
            @closeDia="closeDia"
            :projectInfo="projectInfo"
            :enterprise-id="projectInfo.enterpriseId"
            @toAddProject="toAddProject"
            :show-crowd-switch="showCrowdSwitch"
        ></projectSet>
        <el-dialog title="分配工作组" width="30%" :visible.sync="showGroupSelectDig" :before-close="closeGroupSelectDig">
            请为该人员分配一个工作组
            <el-form>
                <el-input v-model="form.workerId" type='hidden'></el-input>
                <el-form-item>
                    <el-select v-model="form.groupId" clearable filterable>
                        <el-option :label="i.groupName" :value="i.groupId" v-for="i in projectDetail.groupDetails" :key="i.groupId"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            
            <div slot="footer" class="dialog-footer">
                    <el-button @click="closeGroupSelectDig">取 消</el-button>
                    <el-button type="primary" @click="confirmGroupSelect">确 定</el-button>
                </div>
        </el-dialog>
    </div>
</template>

<script>
import admin from '../components/admin.vue';
import qrcodes from '../components/qrcode.vue';
import groupSet from '../components/group_set.vue';
import projectSet from '../components/project_set.vue';
import saveCode from '../../../common/Savecode.vue';
import {
    projectDetail,
    projectGroupAdd,
    projectGroupEdit,
    projectAdmin,
    choseAdmin,
    deleteGroup,
    deleteAdmin,
    groupQrcode,
    projectInfoDetail,
    projectUpdata,
    deleteWorker,
    excelProject,
    downloadGroupWorkers,
    pageWaitWorkersByProjectId,
    updateWorkerStatus,
    isShowCrowd
} from '@/api/project/project.js';
import { Message } from 'element-ui';

export default {
    data() {
        return {
            number: 199,
            dialogVisible: false, //选择管理员弹窗
            dialogQrcode: false, //扫码加入小组
            dialogGroup: false, //设置小组
            dialogProject: false, //设置项目
            projectDetail: {},
            groupInfo: undefined,
            adminList: [],
            dialogSaveCode: false,
            qrcode: {},
            projectInfo: {},
            nowGroupId: '',
            pendingList: {
                size: 10,
                current: 1,
                total: 0,
                records: []
            },
            showGroupSelectDig: false,
            form: {
                workerId: '',
                groupId: ''
            },
            showCrowdSwitch: false
        };
    },
    components: {
        admin,
        groupSet,
        qrcodes,
        projectSet,
        saveCode
    },
    methods: {
        toShowAdminDia() {
            this.dialogVisible = true;
        },
        toShowQrcode(id) {
            this.nowGroupId = id;
            groupQrcode({
                sceneId: id
            }).then((res) => {
                console.log(res);
                if (res.data.code === 0) {
                    this.qrcode = res.data.data;
                    this.dialogQrcode = true;
                } else {
                    Message({
                        message: res.data.msg,
                        type: 'error'
                    });
                }
            });
        },
        showCrowd() {
            isShowCrowd({
                enterpriseId: this.$route.query.enterpriseId,
                projectId: this.$route.query.projectId
            }).then((res) => {
                if (res.data.code == 0) {
                    this.showCrowdSwitch = res.data.data;
                }
            });
        },
        closeDia() {
            this.dialogVisible = false;
            this.dialogQrcode = false;
            this.dialogGroup = false;
            this.dialogProject = false;
            this.nowGroupId = '';
            this.qrcode = {};
        },
        closeCodeDia() {
            this.dialogSaveCode = false;
        },
        toShowGroupSet(item) {
            this.groupInfo = item;
            this.dialogGroup = true;
        },
        toAddGroup() {
            this.groupInfo = undefined;
            this.dialogGroup = true;
        },
        sureInGroup() {
            projectDetail(this.$route.query.projectId).then((res) => {
                this.projectDetail = res.data.data;
            });
        },
        //添加小组
        addGroup(e) {
            let data = {
                groupName: e.groupName,
                projectId: this.$route.query.projectId,
                taxesId: e.taxesId,
                isAutomaticAudit:e.isAutomaticAudit
            };
            projectGroupAdd(data).then((res) => {
                if (res.data.code === 0) {
                    Message({
                        message: '添加成功！',
                        type: 'success'
                    });
                    projectDetail(this.$route.query.projectId).then((res) => {
                        this.projectDetail = res.data.data;
                    });
                    this.dialogGroup = false;
                } else {
                    this.dialogGroup = false;
                    Message({
                        message: res.data.msg,
                        type: 'error'
                    });
                }
            });
        },
        //修改小组
        editGroup(e) {
            let data = {
                name: e.groupName,
                id: this.groupInfo.groupId,
                isAutomaticAudit:e.isAutomaticAudit

            };
            projectGroupEdit(data).then((res) => {
                if (res.data.code === 0) {
                    Message({
                        message: '修改成功！',
                        type: 'success'
                    });
                    projectDetail(this.$route.query.projectId).then((res) => {
                        this.projectDetail = res.data.data;
                    });
                    this.dialogGroup = false;
                } else {
                    this.dialogGroup = false;
                    Message({
                        message: res.data.msg,
                        type: 'error'
                    });
                }
            });
        },
        //选择管理员
        toChooseAdmin(e) {
            choseAdmin({
                adminIds: e,
                projectId: this.$route.query.projectId
            }).then((res) => {
                if (res.data.code == 0) {
                    Message({
                        message: '添加成功！',
                        type: 'success'
                    });
                    projectDetail(this.$route.query.projectId).then((res) => {
                        this.projectDetail = res.data.data;
                    });
                    this.dialogVisible = false;
                } else {
                    this.dialogVisible = false;
                    Message({
                        message: res.data.msg,
                        type: 'error'
                    });
                }
            });
        },
        //安全操作密码
        async toShowCode() {
            let res = await this.$saveCode();
            if (res) {
                deleteGroup({
                    id: this.groupInfo.groupId,
                    password: res.password
                }).then((res) => {
                    if (res.data.code == 0) {
                        Message({
                            message: '操作成功！',
                            type: 'success'
                        });
                        projectDetail(this.$route.query.projectId).then((res) => {
                            this.projectDetail = res.data.data;
                        });
                        this.dialogSaveCode = false;

                        this.dialogGroup = false;
                    } else {
                        this.dialogGroup = false;
                        this.dialogSaveCode = false;
                        Message({
                            message: res.data.msg,
                            type: 'error'
                        });
                    }
                });
            }
        },
        //删除管理员
        toDeleteAdmin(id) {
            this.$confirm('确认删除该管理员?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                let data = {
                    projectId: this.$route.query.projectId,
                    userId: id
                };
                deleteAdmin(data).then((res) => {
                    if (res.data.code == 0) {
                        Message({
                            message: '删除成功！',
                            type: 'success'
                        });
                        projectDetail(this.$route.query.projectId).then((res) => {
                            this.projectDetail = res.data.data;
                        });
                    } else {
                        Message({
                            message: res.data.msg,
                            type: 'error'
                        });
                    }
                });
            });
        },
        //显示项目组信息
        toUpdateProject() {
            projectInfoDetail({
                projectId: this.$route.query.projectId
            }).then((res) => {
                if (res.data.code == 0) {
                    this.dialogProject = true;
                    this.projectInfo = {
                        projectId: this.$route.query.projectId,
                        ...res.data.data
                    };
                } else {
                    Message({
                        message: res.data.msg,
                        type: 'error'
                    });
                }
            });
        },
        toAddProject(e) {
            projectUpdata({
                projectId: this.$route.query.projectId,
                ...e
            }).then((res) => {
                if (res.data.code == 0) {
                    Message({
                        message: '修改成功！',
                        type: 'success'
                    });
                    projectDetail(this.$route.query.projectId).then((res) => {
                        this.projectDetail = res.data.data;
                    });
                    this.dialogProject = false;
                } else {
                    this.dialogProject = false;
                    Message({
                        message: res.data.msg,
                        type: 'error'
                    });
                }
            });
        },
        deleteWorkers(id, ids) {
            this.$confirm('确认删除该员工?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                let data = {
                    groupId: id,
                    workerId: ids
                };
                deleteWorker(data).then((res) => {
                    if (res.data.code == 0) {
                        projectDetail(this.$route.query.projectId).then((res) => {
                            this.projectDetail = res.data.data;
                        });
                    }
                });
            });
        },
        toExcel() {
            excelProject({
                projectId: this.$route.query.projectId
            }).then((res) => {
                let excel = new Blob([res.data]);
                let url = URL.createObjectURL(excel);
                let a = document.createElement('a');
                a.href = url;
                a.download = '项目导出.xlsx';
                a.click();
            });
        },
        downExcel(id, name) {
            downloadGroupWorkers({
                groupId: id
            }).then((res) => {
                let excel = new Blob([res.data]);
                let url = URL.createObjectURL(excel);
                let a = document.createElement('a');
                a.href = url;
                a.download = name + '.xlsx';
                a.click();
            });
        },
        toPayGroup(groupInfos) {
            console.log(groupInfos);
            this.$router.push({
                name: 'initiate',
                params: {
                    projectId: this.$route.query.projectId,
                    groupId: groupInfos.groupId,
                    enterpriseId: this.$route.query.enterpriseId
                }
            });
        },
        navigatorBack() {
            this.$router.replace({
                path: '/project/list',
                query: {
                    current_page: this.$route.query.list_page,
                    enterprise_id: this.$route.query.enterpriseId
                }
            });
        },
        pageWaitWorkers() {
            pageWaitWorkersByProjectId({
                projectId: this.$route.query.projectId,
                current: this.pendingList.current,
                size: this.pendingList.size
            }).then((res) => {
                if (res.data.data) {
                    this.pendingList = res.data.data;
                }
            });
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.pendingList.current = current;
            this.pageWaitWorkers();
        },
        toSure(workerId, groupId) {
            if(groupId) {
                this.toSureInGroup(workerId, groupId);
            } else {
                this.toShowGroupSelectDig(workerId);
            }
        },
        toShowGroupSelectDig(workerId)  {
            this.showGroupSelectDig = true;
            this.form.workerId = workerId;
        },
        closeGroupSelectDig() {
            this.form = {
                workerId: '',
                groupId: ''
            }
            this.showGroupSelectDig = false;
        },
        confirmGroupSelect() {
            this.toSureInGroup(this.form.workerId, this.form.groupId);
        },
        toSureInGroup(workerId, groupId) {
            this.$confirm(`确定要同意进组？`, '提示', {
                type: 'warning'
            })
                .then(() => {
                    updateWorkerStatus({
                        auditStatus: 3,
                        groupId: groupId,
                        workerId: workerId
                    }).then((res) => {
                        if (res.data.code == 0) {
                            Message.success(res.data.msg);
                            this.pageWaitWorkers();
                            this.sureInGroup();
                        }
                    });
                })
                .catch(() => {})
                .finally(() => {
                    this.closeGroupSelectDig();
                });
        },
        toCancel(workerId, groupId) {
            this.$confirm(`确定要拒绝？`, '提示', {
                type: 'warning'
            }).then(() => {
                updateWorkerStatus({
                    auditStatus: 2,
                    groupId: groupId,
                    workerId: workerId
                }).then((res) => {
                    if (res.data.code == 0) {
                        Message.success(res.data.msg);
                        this.pageWaitWorkers();
                    }
                });
            })
            .catch(() => {});
        }
    },
    created() {
        this.showCrowd();
        projectDetail(this.$route.query.projectId).then((res) => {
            this.projectDetail = res.data.data;
        });
        projectAdmin({
            enterpriseId: this.$route.query.enterpriseId
        }).then((res) => {
            if (res.data.code === 0) {
                this.adminList = res.data.data;
            } else {
                Message({
                    message: res.data.msg,
                    type: 'error'
                });
            }
        });
        this.pageWaitWorkers();
    }
};
</script>

<style scoped>
.detail_box {
    display: flex;
}
.detail_box .left {
    width: 300px;
    padding-right: 30px;
    max-height: 400px;
    overflow-y: auto;
    border-right: 1px solid rgba(0,0,0,0.2);
}
.detail_box .right {
    flex: 1;
    margin-left: 30px;
}
.people_item {
    display: flex;
    align-items: center;
    padding: 5px 0;
    cursor: pointer;
}
.people_item:hover {
    background-color: #e6f7ff;
}
.people_item:hover .toShowButton {
    display: block;
}
.people_item:last-child {
    margin-bottom: 0px;
}
.toShowButton {
    display: none;
}
.people_item img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 20px;
}
.group_top {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #333;
    padding-bottom: 20px;
}
.group_list .name {
    margin-left: 10px;
    flex: 1;
}
.group_bottom {
    display: flex;
    flex-wrap: wrap;
    padding-top: 20px;
    margin-bottom: 20px;
}
.group_bottom .item {
    display: flex;
    align-items: center;
    padding: 4px 20px;
    border-radius: 25px;
    margin-bottom: 20px;
    background-color: #fff;
}
.group_bottom .item:hover {
    background-color: #e6f7ff;
}
.group_bottom .item img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}
.group_bottom .delete {
    width: 10px;
    height: 10px;
}
.group_error {
    color: #fa5555;
    visibility: hidden;
}
.group_bottom .item:hover .group_error {
    visibility: visible;
}
.people_item {
    display: flex;
    align-items: center;
}
.people_box {
    font-size: 16px;
}
.people_item img {
    width: 40px;
    height: 40px;
    margin-right: 15px;
    border-radius: 50%;
}
.people_item .avatar {
    margin-right: 15px;
}

.people_item .name {
    flex: 1;
}
</style>
