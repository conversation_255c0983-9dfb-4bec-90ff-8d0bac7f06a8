<template>
    <div>
        <el-card shadow="never">
            <el-form :inline="true" :model="Form" class="demo-form-inline button_box">
                <el-form-item label="企业名称">
                    <el-select v-model="enterpriseId" filterable placeholder="请选择" @change="handleChange">
                        <el-option v-for="item in options" :key="item.enterpriseId" :label="item.enterpriseName" :value="item.enterpriseId">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="项目名称">
                    <el-input v-model="Form.searchStr" placeholder="请输入项目名称" clearable></el-input>
                </el-form-item>
                <el-form-item label="众包大厅展示" v-if="showCrowdSwitch">
                    <el-select v-model="Form.openOption" placeholder="请选择众包大厅展示选项">
                        <el-option v-for="openOption in openOptions" :label="openOption.label" :value="openOption.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="medium" @click="toSeachList">查 询</el-button>
                </el-form-item>
                <div style="flex: 1"></div>
                <el-form-item>
                    <el-button type="primary" size="medium" @click="toAddProject" v-has="'platform_project_add'">添加项目</el-button>
                </el-form-item>
            </el-form>
            <el-table :data="tableData.resultDTOS" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                <el-table-column type="index" label="序号" width="150" fixed="left" header-align="center" align="center"></el-table-column>
                <el-table-column prop="name" label="项目名称" min-width="200" show-overflow-tooltip></el-table-column>
                <el-table-column prop="groupNumber" label="小组数量" width="150" header-align="center" align="center"></el-table-column>
                <el-table-column label="项目总人数" prop="projectTotalNumber" width="180" header-align="center" align="center"> </el-table-column>
                <el-table-column label="待审核人数" prop="waitAuditNumber" width="180" header-align="center" align="center"> </el-table-column>
                <el-table-column label="众包大厅" prop="openOption" width="200" align="center" v-if="showCrowdSwitch">
                    <template slot-scope="scope">
                        <span v-if="scope.row.openOption == 0">不展示和搜索</span>
                        <span v-if="scope.row.openOption == 1">可展示和搜索</span>
                        <span v-if="scope.row.openOption == 2">仅搜索, 不展示</span>
                    </template>
                </el-table-column>
                <el-table-column prop="createTime" label="创建时间" width="260" align="center"></el-table-column>
                <el-table-column width="200" label="操作" fixed="right" align="center">
                    <template slot-scope="scope">
                        <el-button @click="handleEdit(scope.row)" v-has="'platform_project_details'" type="text" icon="el-icon-edit" size="medium"
                            >查看详情</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page="tableData.current"
                    layout="prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
        </el-card>
        <project-set :dialog-visible="dialogProject" :show-crowd-switch="showCrowdSwitch" @closeDia="closeDia" @toAddProject="toAddProjects" :options="options"></project-set>
    </div>
</template>

<script>
import { Message } from 'element-ui';
import { currentPage } from '@/utils/common';
import { projectList, projectAdd, proEnterpriseList, isShowCrowd } from '@/api/project/project.js';
import projectSet from '../components/project_set.vue';
import enterprise from '@/components/page/branch/enterprise/enterprise.vue';
export default {
    components: {
        projectSet
    },
    computed: {},
    data() {
        return {
            Form: {
           
            },
            password: '',
            tableData: {
                resultDTOS: []
            },
            current: 1,
            size: 10,
            visible: false,
            resetVisible: false,
            distributeVisible: false,
            form: {},
            dialogType: 0,
            manageVisible: false,
            userId: '',
            dialogProject: false,
            enterpriseId: '',
            options: [],
            openOptions: [
                {
                    label: '全部',
                    value: '-1'
                },
                {
                    label: '不可展示和搜索',
                    value: '0'
                },
                {
                    label: '可展示和搜索',
                    value: '1'
                },
                {
                    label: '不展示，仅可搜索',
                    value: '2'
                }
            ],
            showCrowdSwitch: false
        };
    },
    mounted() {
        proEnterpriseList().then((res) => {
            if (res.data.code == 0) {
                this.options = res.data.data;
                this.enterpriseId = res.data.data[0].enterpriseId;
                // this.enterpriseId="7e654cf1ace9c78204b8d08d5ffb6307"; // 临时需求
            }
            this.getprojectList();
            this.showCrowd();
        });
    },
    methods: {
        // 查看详情
        handleEdit(row) {
            this.$router.push({
                path: '/project/detail',
                query: {
                    projectId: row.id,
                    enterpriseId: row.enterpriseId,
                    list_page: this.current
                }
            });
        },
        toSeachList() {
            this.current = 1;
            this.$router.replace({query: {enterprise_id: this.enterpriseId}})
            this.getprojectList();
        },
        handleChange(enterpriseId) {
            this.enterpriseId = enterpriseId;
            this.toSeachList();
            this.showCrowd();
        },
        showCrowd() {
            isShowCrowd({
                enterpriseId: this.enterpriseId,
                projectId: ''
            }).then((res) => {
                if (res.data.code == 0) {
                    this.showCrowdSwitch = res.data.data;
                }
            });
        },
        // 项目列表
        getprojectList() {
            if(this.$route.query.current_page) {
                this.current = this.$route.query.current_page;
            }
            if(this.$route.query.enterprise_id) {
                this.enterpriseId = this.$route.query.enterprise_id;
            }
            let data = {
                ...this.Form,
                current: this.current,
                size: this.size,
                enterpriseId: this.enterpriseId,
                type:1
            };
            projectList(data).then((res) => {
                if (res.data.data) {
                    if (res.data.code === 0) {
                        this.tableData = res.data.data;
                    } else {
                        Message({
                            message: res.data.msg,
                            type: 'error'
                        });
                    }
                } else {
                    this.tableData = [];
                }
            });
        },
        //分页-每页条数改变
        handleSizeChange(size) {
            this.size = size;
            this.current = 1;
            this.getprojectList();
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.current = current;
            this.$router.replace({query: {current_page: current}})
            this.getprojectList(current);
        },
        //添加项目
        toAddProject() {
            proEnterpriseList().then((res) => {
                if (res.data.code == 0) {
                    this.options = res.data.data;
                }
                this.dialogProject = true;
            });
        },
        closeDia() {
            this.dialogProject = false;
        },
        //新增项目
        toAddProjects(e) {
            projectAdd(e).then((res) => {
                if (res.data.code == 0) {
                    Message({
                        message: '添加成功！',
                        type: 'success'
                    });
                    this.current = 1;
                    this.getprojectList();
                    this.dialogProject = false;
                } else {
                    this.dialogProject = false;
                    Message({
                        message: res.data.msg,
                        type: 'error'
                    });
                }
            });
        }
    }
};
</script>

<style scoped lang="scss">
.btn {
    color: #ffffff;
    padding: 5px 10px;
    border-radius: 5px;
}
.button_box {
    display: flex;
    align-items: center;
}
</style>
