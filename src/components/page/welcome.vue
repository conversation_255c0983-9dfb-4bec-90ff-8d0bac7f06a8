<template>
    <div class="welcome_page">
        <div class="welcome_title">欢迎登录后台系统</div>
    </div>

</template>
<script>
import { ImageRotatorJS } from '@/utils/ImageRotator-min.js';
export default {
    name: 'tabs',
    data() {
        return {
            id: 'QrCode',
            qrcode: '',
            imageRotater: null,

            nowId: -1
        };
    },
    methods: {

    },
    mounted() {
        this.$nextTick(() => {
            this.InitScripts();
        });
    }
};
</script>

<style lang="scss" scoped>
.welcome_page {
    height: var(--welcome-page-height);
    background-color: #fff;
    background-image: var(--welcome-bg-image);
    background-repeat: var(--welcome-bg-repeat);
    background-position: var(--welcome-bg-position);
    background-size: var(--welcome-bg-size);
    overflow: hidden;
    color: #303133;
    -webkit-transition: 0.3s;
    transition: 0.3s;
    position: relative;
}
.welcome_title {
    font-size: 38px;
    font-family: Inter-Regular, Inter,serif;
    font-weight: 500;
    letter-spacing: 10px;
    color: var(--welcome-title-color);
    padding: 40px 58px;
    text-align: var(--welcome-title-align);
    text-shadow: 2px 2px 4px rgba(255, 255, 255, 0.8);  /* 添加文字阴影提高可读性 */
}
.img_size {
    width: 60%;
    position: absolute;
    object-fit: cover;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

//轮播图
body {
    font-size: 1em;
    color: #ffffff;
    background-color: #000000;
}
#Ellipse {
    position: absolute;
    visibility: visible;
    z-index: 999;
    width: 700px;
    height: 80px;
    border: none;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);
}
.RotatingIcon {
    border: none;
    width: 100px;
    height: 150px;
}
</style>
