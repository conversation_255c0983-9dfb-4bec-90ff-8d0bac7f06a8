<template>
    <div>
        <el-card class="left" shadow="never">
            <div slot="header" class="clearfix">
                <span>渠道提成</span>
            </div>
            <el-form label-width="80px" :inline="true">
                <el-form-item label="选择月份">
                    <el-date-picker
                        v-model="form.value1"
                        type="monthrange"
                        range-separator="~"
                        start-placeholder="开始月份"
                        end-placeholder="结束月份"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="选择渠道">
                    <el-select v-model="form.channelId" clearable placeholder="请选择" filterable>
                        <el-option v-for="item in selectList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                    </el-select>
                </el-form-item>
                <el-button type="primary" @click="toSearchList" size="medium">搜 索</el-button>
            </el-form>
            <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                <el-table-column prop="month" label="月份" show-overflow-tooltip></el-table-column>
                <el-table-column prop="channelName" label="渠道名称"></el-table-column>
                <el-table-column label="状态" prop="stateStr"></el-table-column>
                <el-table-column label="提成比例" prop="rate"> </el-table-column>
                <el-table-column label="提成金额" prop="money">
                    <template slot-scope="scope">
                        <span>{{ scope.row.money.toLocaleString('en-US') }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" prop="createTime"> </el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-button @click="prodBuild(scope.row.id)" type="text" v-if="scope.row.state == 2">生成</el-button>
                        <el-button @click="lookDetail(scope.row.id)" type="text">查看明细</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page="tableData.current"
                    layout="prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
        </el-card>
    </div>
</template>

<script>
import { sysChannelRoyaltyRecord, channelSelect, channelGenerate } from '@/api/account/account.js';
export default {
    data() {
        return {
            tableData: {},
            current: 1,
            size: 10,
            dialogFormVisible: false,
            messageInfo: {},
            showDia: false,
            form: {},
            options: [],
            selectList: []
        };
    },

    methods: {
        //分页-每页条数改变
        handleSizeChange(size) {
            this.size = size;
            this.current = 1;
            this.getMessageList();
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.current = current;
            this.getMessageList(current);
        },
        getMessageList() {
            sysChannelRoyaltyRecord({
                ...this.form,
                current: this.current,
                size: this.size
            }).then((res) => {
                this.tableData = res.data.data;
            });
        },
        lookDetail(id) {
            this.$router.push('/account/channelDetail?id=' + id);
        },
        toAddNewMessage() {
            this.showDia = true;
        },
        closeDia() {
            this.showDia = false;
        },
        toAddMessage(e) {
            addMsg(e).then((res) => {
                if (res.data.code == 0) {
                    this.current = 1;
                    this.getMessageList();
                    this.showDia = false;
                }
            });
        },
        toSearchList() {
            this.current = 1;
            this.getMessageList();
        },
        prodBuild(id) {
            this.$confirm(`确定要生成吗？`, '提示', {
                type: 'warning'
            })
                .then(() => {
                    channelGenerate({
                        id
                    }).then((res) => {
                        if (res.data.code == 0) {
                            this.getMessageList();
                        }
                    });
                })
                .catch(() => {});
        }
    },
    created() {
        channelSelect().then((res) => {
            if (res.data.code == 0) {
                this.selectList = res.data.data;
            }
        });
        this.getMessageList();
    }
};
</script>

<style>
</style>