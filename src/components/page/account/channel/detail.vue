<template>
    <div>
        <el-card class="left" shadow="never">
            <div slot="header" class="clearfix">
                <span>渠道提成详情</span>
            </div>
            <el-form label-width="80px" :inline="true">
                <el-form-item label="选择机构">
                    <el-select v-model="form.enterpriseId" clearable placeholder="请选择" filterable>
                        <el-option v-for="item in listEnterprise" :key="item.enterpriseId" :label="item.name" :value="item.enterpriseId">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="选择钱包">
                    <el-select v-model="form.walletId" clearable placeholder="请选择" filterable>
                        <el-option v-for="item in listWalletSelect" :key="item.walletId" :label="item.walletStr" :value="item.walletId">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="模糊搜索">
                    <el-input placeholder="请输入关键词" v-model="form.text" clearable></el-input>
                </el-form-item>
                <el-form-item label="金额范围">
                    <el-input placeholder="最小金额" style="width: 100px" v-model="form.minMoney" clearable></el-input>
                    -
                    <el-input placeholder="最大金额" style="width: 100px" v-model="form.maxMoney" clearable></el-input>
                </el-form-item>
                <el-form-item label="创建时间">
                    <el-date-picker
                        v-model="value1"
                        type="daterange"
                        range-separator="~"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        @change="toChoseTime"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-button @click="toExportExel">导出</el-button>
                <el-button type="primary" @click="toSearchList">搜索</el-button>
            </el-form>
            <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                <el-table-column prop="channelName" label="渠道名称" show-overflow-tooltip></el-table-column>
                <el-table-column label="提成比例">
                    <template slot-scope="scope">
                        <div>{{ scope.row.rate }}%</div>
                    </template>
                </el-table-column>
                <el-table-column label="提成金额">
                    <template slot-scope="scope">
                        <div>￥{{ scope.row.money }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="付款流水号" prop="instanceNumber"> </el-table-column>
                <el-table-column label="商户钱包" prop="walletStr"> </el-table-column>
                <el-table-column label="机构-项目">
                    <template slot-scope="scope">
                        <div>{{ scope.row.enterpriseName }}-{{ scope.row.projectName }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="收款人">
                    <template slot-scope="scope">
                        <div>{{ scope.row.workerName }}</div>
                        <div>{{ scope.row.idCardNumber }}</div>
                        <div>{{ scope.row.phone }}</div>
                        <div>{{ scope.row.bankNumber }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="发放金额" prop="content">
                    <template slot-scope="scope">
                        <div>￥{{ scope.row.payMoney }}</div>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page="tableData.current"
                    layout="prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
        </el-card>
    </div>
</template>

<script>
import { channelDetailList, listWalletByUser, exportDetailList, listEnterpriseByUser } from '@/api/account/account.js';
export default {
    data() {
        return {
            tableData: {},
            current: 1,
            size: 10,
            dialogFormVisible: false,
            messageInfo: {},
            showDia: false,
            form: {
                enterpriseId: '',
                walletId: '',
                minMoney: '',
                maxMoney: '',
                startTime: '',
                endTime: ''
            },
            options: [],
            listWalletSelect: [],
            value1: '',
            listEnterprise: []
        };
    },

    methods: {
        //分页-每页条数改变
        handleSizeChange(size) {
            this.size = size;
            this.current = 1;
            this.getMessageList();
        },
        //导出excel
        toExportExel() {
            exportDetailList({ ...this.form, id: this.$route.query.id }).then((res) => {
                let excel = new Blob([res.data]);
                let url = URL.createObjectURL(excel);
                let a = document.createElement('a');
                a.href = url;
                a.download = '渠道提成详情列表.xls';
                a.click();
            });
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.current = current;
            this.getMessageList(current);
        },
        getMessageList() {
            channelDetailList({
                current: this.current,
                size: this.size,
                id: this.$route.query.id,
                ...this.form
            }).then((res) => {
                res.data.data.records.forEach(s=>{
                    s.walletStr = s.merchantName + '-' + s.companyName + '-' +s.bankName
                })
                this.tableData = res.data.data;
            });
        },
        lookDetail(id) {
            this.$router.push('/account/channelDetail');
        },
        toAddNewMessage() {
            this.showDia = true;
        },
        closeDia() {
            this.showDia = false;
        },
        toSearchList() {
            this.current = 1;
            this.getMessageList();
        },
        toChoseTime() {
            if (this.value1) {
                this.form.startTime = this.value1[0];
                this.form.endTime = this.value1[0];
            } else {
                this.form.startTime = '';
                this.form.endTime = '';
            }
        }
    },
    created() {
        listWalletByUser().then((res) => {
            if (res.data.code == 0) {
                res.data.data.forEach((s) => {
                    s.walletStr = s.merchantName + '-' + s.companyName + '-' + s.bankName;
                });
                this.listWalletSelect = res.data.data;
            }
        });
        listEnterpriseByUser().then((res) => {
            if (res.data.code == 0) {
                this.listEnterprise = res.data.data;
            }
        });
        this.getMessageList();
    }
};
</script>

<style>
</style>