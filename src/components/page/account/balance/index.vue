<template>
    <div class="detail_box">
        <el-card class="left" shadow="never">
            <div slot="header" class="clearfix">
                <span>商户余额</span>
            </div>
            <el-form label-width="90px" :inline="true">
                <el-form-item label="商户名称：">
                    <el-input v-model="form.merchantName" style="width: 250px" placeholder="请输入商户查询"></el-input>
                </el-form-item>
                <el-form-item label="对接银行：">
                    <el-select v-model="form.bankId" clearable filterable>
                        <el-option :label="i.bankName" :value="i.id" v-for="i in bankList" :key="i.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-button type="primary" style="margin-left: 20px" @click="toSearch()" size="medium">搜 索</el-button>
                
            </el-form>
            <el-row>
                <div style="float: right">
                    <el-button type="primary" @click="toAddMerchant" v-has="'merchant_insert'" size="medium">新增商户</el-button>
                    <el-button type="primary" @click="toAddMerchantAli" v-has="'merchant_insert'" size="medium">新增支付宝商户</el-button>
                    <el-button type="primary" @click="toAddHfMerchant" v-has="'merchant_insert'" size="medium">新增汇付商户</el-button>
                </div>
            </el-row>
            <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                <el-table-column prop="merchantName" label="商户名称" min-width="100" show-overflow-tooltip></el-table-column>
                <el-table-column label="对接银行" min-width="100">
                    <template slot-scope="scope">
                        <div style="display:flex; align-items: center; ">
                            <span>{{ scope.row.bankName }}</span>
                            <img width="16px" src="@/assets/img/zsyh.png" v-show="scope.row.bankName.indexOf('招商') > -1 || scope.row.bankName.indexOf('招行') > -1" style="margin-left: 3px"/>
                            <img width="16px" src="@/assets/img/alipay.png" v-show="scope.row.bankName.indexOf('支付宝') > -1" style="margin-left: 3px"/>
                            <img width="16px" src="@/assets/img/pabank.png" v-show="scope.row.bankName.indexOf('平安') > -1" style="margin-left: 3px"/>
                            <img width="16px" src="@/assets/img/wpay.png" v-show="scope.row.bankName.indexOf('微信') > -1" style="margin-left: 3px" />
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="company" label="收款公司" min-width="200" show-overflow-tooltip></el-table-column>
                <el-table-column label="年龄限制" width="100">
                    <template slot-scope="scope">
                        <span>{{ scope.row.ageStart }}-{{ scope.row.ageEnd }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="账户余额" prop="money" min-width="150">
                    <template slot-scope="scope">
                        <span class="money">{{scope.row.money ? '￥' + scope.row.money.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) : '---' }}</span>
                        <div class="money" style="color: #ff0000; font-size: 12px;" v-if="scope.row.frozenMoney > 0">(含冻结：{{ scope.row.frozenMoney.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) }})</div>
                    </template>
                </el-table-column>
                <el-table-column label="绑定钱包" prop="walletNumber" min-width="100"></el-table-column>
                <el-table-column label="记账本余额" prop="aliBookMoney" min-width="100">
                    <template slot-scope="scope">
                        <span>{{ scope.row.aliBookMoney.toLocaleString('en-US') }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="账户状态" width="100">
                    <template slot-scope="scope">
                        <span v-if="scope.row.accountStatus == 1" style="color: #0099ff">正常</span>
                        <span v-if="scope.row.accountStatus == 2" style="color: #ff0000">已锁定</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="200">
                    <template slot-scope="scope">
                        <el-button type="text" @click="lookModify(scope.row.id,scope.row.type)" v-has="'platform_balance_edi'" style="font-size: 15px;">修改</el-button>
                        <el-button type="text" @click="lookDetail(scope.row.id)" v-has="'platform_balance_details'" style="font-size: 15px;">查看详情</el-button>
                        <el-button
                            type="text"
                            @click="handleWithdrawal(scope.row.id, scope.row.aliBookMoney, scope.row.merchantName)"
                            v-if="scope.row.type == 2 && isService"
                            style="font-size: 15px;"
                            >提现</el-button
                        >
                        <el-button
                            type="text"
                            @click="scope.row.accountStatus == 1 ? lockMerchant(scope.row.id, 2) : lockMerchant(scope.row.id, 1)"
                            v-has="'platform_balance_disable'"
                            style="font-size: 15px;"
                            >{{ scope.row.accountStatus == 1 ? '锁定账户' : '解锁账户' }}</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page="tableData.current"
                    layout="prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
        </el-card>
        <addMerchant
            :toShowDia="toShowDia"
            @closeDia="closeDia"
            @merchantSure="merchantSure"
            :merchantId="merchantId"
            @merchantSureNew="merchantSureNew"
        ></addMerchant>
        <addMerchantAli
            :toShowDia="toShowDiaAli"
            @closeDia="closeDia"
            @merchantSure="merchantSureAli"
            :merchantId="merchantId"
            @merchantSureNew="merchantSureNew"
        ></addMerchantAli>
        <addWxMerchant
            :toShowDia="toShowDiaWx"
            @closeDia="closeDia"
            :merchantId="merchantId"
            @merchantSure="merchantWxSure"
            @merchantSureNew="merchantSureNew"
        >
        </addWxMerchant>
        <addHfMerchant
            :toShowDia="toShowDiaHf"
            @closeDia="closeDia"
            :merchantId="merchantId"
            @merchantSure="merchantHfSure"
            @merchantSureNew="merchantSureNew"
        >
        </addHfMerchant>
        <withdrawalDia
            :to-show-dia="toShowWithdrawalDia"
            @closeDia="closeDia"
            :merchantId="merchantId"
            :merchantName="merchantName"
            :merchantMoney="merchantMoney"
            @withdrawalSure="withdrawalSure"
        ></withdrawalDia>
        <el-dialog title="扫码签约" :visible.sync="dialogVisible" width="40%" top="5vh">
            <iframe :src="qcodePlace" style="height: 600px; width: 100%; border: none" class="iframe-size"></iframe>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { Message } from 'element-ui';
import { merchantPage, lockStatus, merchantAliCode, bankListAll } from '@/api/account/account.js';
import addMerchant from '../components/addMerchant.vue';
import addMerchantAli from '../components/addAliMerchant.vue';
import addHfMerchant from '../components/addHfMerchant.vue';
import addWxMerchant from '../components/addWxMerchant.vue';
import withdrawalDia from '../components/withdrawalDia.vue';
import { getUserInfo } from '@/api/system/login';

export default {
    data() {
        return {
            walletList: [],
            form: {
                current: 1,
                size: 10
            },
            tableData: {},
            toShowDia: false,
            merchantId: undefined,
            toShowDiaAli: false,
            qcodePlace: '',
            dialogVisible: false,
            bankList: [],
            toShowWithdrawalDia: false,
            merchantName: '',
            merchantMoney: 0,
            isService: false,
            toShowDiaWx: false,
            toShowDiaHf: false,
        };
    },
    components: {
        addMerchant,
        addMerchantAli,
        withdrawalDia,
        addWxMerchant,
        addHfMerchant
    },
    methods: {
        getData() {
            merchantPage({
                ...this.form
            }).then((res) => {
                if (res.data.code == 0) {
                    this.tableData = res.data.data;
                }
            });
            bankListAll({}).then((res) => {
                this.bankList = res.data.data;
            });
        },
        toSearch() {
            this.form.current = 1;
            this.getData();
        },
        handleSizeChange(size) {
            this.form.size = size;
            this.form.current = 1;
            this.getData();
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.form.current = current;
            this.getData();
        },
        lookModify(id, type) {
            this.merchantId = id;
            if (type == 3) {
                this.toShowDiaWx = true;
            } else if (type == 6) {
                this.toShowDiaHf = true;
            } else {
                this.toShowDia = true;
            }
        },
        toAddMerchant() {
            this.merchantId = undefined;
            this.toShowDia = true;
        },
        toAddMerchantAli() {
            this.merchantId = undefined;
            this.toShowDiaAli = true;
        },
        closeDia() {
            this.toShowDia = false;
            this.toShowDiaAli = false;
            this.toShowWithdrawalDia = false;
            this.toShowDiaWx = false;
            this.toShowDiaHf = false;
            this.merchantId = undefined;
        },
        merchantSure() {
            this.form.current = 1;
            this.toShowDia = false;
            this.getData();
        },
        merchantWxSure(){
            this.form.current = 1;
            this.toShowDiaWx = false;
            this.getData();
        },
        merchantHfSure() {
            this.form.current = 1;
            this.toShowDiaHf = false;
            this.getData();
        },
        merchantSureNew() {
            this.toShowDia = false;
            this.toShowDiaWx = false;
            this.toShowDiaHf = false;
            this.merchantId = undefined;
            this.getData();
        },
        lockMerchant(id, accountStatus) {
            let word = '';
            if (accountStatus == 2) {
                word = '锁定商户';
            } else {
                word = '解锁商户';
            }
            this.$confirm(`确定要${word}吗？`, '提示', {
                type: 'warning'
            })
                .then(() => {
                    lockStatus({
                        id,
                        accountStatus
                    }).then((res) => {
                        if (res.data.code == 0) {
                            Message.success(res.data.msg);
                            this.getData();
                        } else {
                            Message.error(res.data.msg);
                        }
                    });
                })
                .catch(() => {});
        },
        lookDetail(id) {
            this.$router.push('/account/balance/detail?id=' + id);
        },
        toAddWxMerchant() {
            this.merchantId = undefined;
            this.toShowDiaWx = true;
        },
        toAddHfMerchant() {
            this.merchantId = undefined;
            this.toShowDiaHf = true;
        },
        withdrawalSure() {
            this.$message.success('提现成功！');
            this.toShowWithdrawalDia = false;
            this.getData();
        },
        handleWithdrawal(id, money, name) {
            this.merchantId = id;
            this.merchantName = name;
            this.merchantMoney = money;
            this.toShowWithdrawalDia = true;
        },
        async merchantSureAli(e) {
            this.toShowDiaAli = false;
            let res = await merchantAliCode(e);
            if (res.data.code != 0) return false;
            this.qcodePlace = res.data.data.body;
            this.dialogVisible = true;
        }
    },
    async created() {
        let res = await getUserInfo();
        this.isService = res.data.isService;
        this.getData();
    }
};
</script>

<style scoped>
.company_name {
    display: inline-block;
    flex: 1;
}
.button_box {
    display: flex;
    align-items: center;
}
.topinfo {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.bank_name {
    font-size: 14px;
    border: 1px solid #999;
    padding: 5px;
    border-radius: 5px;
    color: #999;
}
::-webkit-scrollbar {
    display: none; /* Chrome Safari */
}
.num_money {
    font-size: 40px;
    text-align: center;
    margin-top: 40px;
}
.word_info {
    text-align: center;
    margin-top: 10px;
    margin-bottom: 60px;
}
</style>
