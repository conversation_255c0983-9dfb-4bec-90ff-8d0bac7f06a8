<template>
    <el-dialog :title="merchantId ? '修改商户' : '新增商户'" :visible.sync="dialogVisible" width="60%" @open="openDia" @close="closeDia">
        <el-form label-width="200px">
            <el-form-item label="商户名称：">
                <el-input placeholder="请输入商户" v-model="form.merchantName" style="width: 250px"></el-input>
                <span>如：上海易鑫-招行</span>
            </el-form-item>
            <el-form-item label="对接银行：">
                <el-select v-model="form.bankId" placeholder="请选择" clearable style="width: 250px" filterable :disabled="merchantId">
                    <el-option v-for="item in bankAllList" :key="item.id" :label="item.bankName" :value="item.id" > </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="收款公司：">
                <el-input placeholder="请输入收款公司" v-model="form.company" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="收款银行全称：">
                <el-input placeholder="请输入收款银行全称" v-model="form.bankNameFull" style="width: 250px" :disabled="merchantId"></el-input>
            </el-form-item>
            <el-form-item label="收款银行简称（4-6字）：">
                <el-input placeholder="请输入收款银行简称" v-model="form.bankName" style="width: 250px"></el-input>
                <span>如：招行上海周浦</span>
            </el-form-item>
            <el-form-item label="银行账户：">
                <el-input placeholder="请输入银行账户" v-model="form.accountNumber" style="width: 250px" :disabled="merchantId"></el-input>
            </el-form-item>
            <el-form-item label="年龄限制：">
                <el-input placeholder="最小年龄" v-model="form.ageStart" style="width: 100px"></el-input>
                至
                <el-input placeholder="最大年龄" v-model="form.ageEnd" style="width: 100px"></el-input>
            </el-form-item>
            <el-form-item label="税源地选择：">
                <el-select v-model="form.taxesId" placeholder="请选择" clearable style="width: 250px" filterable>
                    <el-option v-for="item in taxesList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                </el-select>
            </el-form-item>
            <!-- <el-form-item label="网银用户UID：">
                <el-input placeholder="请输入网银用户UID" v-model="form.bankUid" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="银行公钥：">
                <el-input placeholder="请输入银行公钥" v-model="form.bankPubKey" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="银行用户私钥：">
                <el-input placeholder="请输入银行用户私钥" v-model="form.bankPrivKey" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="银行用户对称密钥：">
                <el-input placeholder="请输入银行用户对称密钥" v-model="form.bankSmKey" style="width: 250px"></el-input>
            </el-form-item> -->
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="closeDia">取 消</el-button>
            <el-button type="primary" @click="merchantId ? toSubimitUpdata() : toSubmitInfo()">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>
import { bankListAll, addMerchant, getTaxesList, getMerchantDetail, merchantUpdata, bankList } from '@/api/account/account.js';
import { Message } from 'element-ui';
export default {
    data() {
        return {
            dialogVisible: false,
            form: {
                // bankUid: 'U004866757',
                // bankPubKey: 'BEynMEZOjNpwZIiD9jXtZSGr3Ecpwn7r+m+wtafXHb6VIZTnugfuxhcKASq3hX+KX9JlHODDl9/RDKQv4XLOFak=',
                // bankPrivKey: 'jDRmWHnPbqRDTXnxXPxV/Q/FyCaGgCo1nJsDNqX+On0=',
                // bankSmKey: '3QoqG9EEnzbWwzmb'
            },
            channelPtions: [
                {
                    channelId: 0,
                    channelName: '无上级'
                }
            ],
            options: [],
            bankAllList: [],
            rateDetails: [
                {
                    minMoney: '',
                    maxMoney: '',
                    rate: ''
                }
            ],
            taxesList: []
        };
    },
    props: {
        toShowDia: {
            type: Boolean,
            require: true
        },
        merchantId: {
            type: String,
            require: false
        }
    },
    watch: {
        toShowDia(val) {
            this.dialogVisible = val;
        },
        merchantId(val) {
            if (val) {
                getMerchantDetail({
                    id: val
                }).then((res) => {
                    if (res.data.code == 0) {
                        this.form = res.data.data;
                    }
                });
            } else {
                this.form = {}
            }
        }
    },
    methods: {
        toAddRateRow() {
            this.rateDetails.push({
                minMoney: '',
                maxMoney: '',
                rate: ''
            });
        },
        toDetelRate(index) {
            this.rateDetails.splice(index, 1);
        },
        openDia() {
            if (this.merchantId) {
                bankListAll().then((res) => {
                    if (res.data.code == 0) {
                        this.bankAllList = res.data.data;
                    }
                });
            } else {
                bankList().then((res) => {
                    if (res.data.code == 0) {
                        this.bankAllList = res.data.data
                    }
                });
            }
        },
        closeDia() {
            this.form = {};
            this.$emit('closeDia');
        },
        toSubmitInfo() {
            this.$confirm('是否确认新增商户', '新增')
                .then(() => {
                    addMerchant({
                        ...this.form
                    }).then((res) => {
                        if (res.data.code == 0) {
                            this.$emit('merchantSure');
                            this.form = {};
                        } else {
                            Message.error(res.data.msg);
                        }
                    });
                })
                .catch(() => {});
        },
        toSubimitUpdata() {
            this.$saveCode()
                .then((ress) => {
                    merchantUpdata({
                        ...this.form,
                        id: this.merchantId,
                        password: ress.password
                    }).then((res) => {
                        if (res.data.code == 0) {
                            this.$emit('merchantSureNew');
                            this.form = {};
                        } else {
                            Message.error(res.data.msg);
                        }
                    });
                })
                .catch(() => {});
        }
    },
    created() {
        getTaxesList().then((res) => {
            if (res.data.code == 0) {
                this.taxesList = res.data.data;
            }
        });
    }
};
</script>

<style scoped>
.rate_box {
    align-items: center;
    display: flex;
    margin-bottom: 10px;
}
.rate_box:last-child {
    margin-bottom: 0px;
}
</style>
