<template>
    <el-dialog :title="merchantId?'修改微信商户':'新增微信商户'" :visible.sync="dialogVisible" width="60%" @open="openDia" @close="closeDia">
        <el-form label-width="150px">
            <el-form-item label="子商户号：">
                <el-input placeholder="请输入子商户号" v-model="form.subMchid" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="子商户应用ID：">
                <el-input placeholder="多个id用“，”隔开" v-model="form.appids" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="绑定公众号：">
                <el-input placeholder="多个公众号用“，”隔开" v-model="form.appnames" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="银行户名：">
                <el-input placeholder="请输入银行户名" v-model="form.accountName" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="银行账号：">
                <el-input placeholder="请输入银行账号" v-model="form.accountNumber" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="开户银行：">
                <el-input placeholder="请输入开户银行" v-model="form.bankName" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="开户行号：">
                <el-input placeholder="请输入开户行号" v-model="form.bankNumber" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="商户名称：">
                <el-input placeholder="请输入商户" v-model="form.merchantName" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="对接银行：" v-if="merchantId">
                <el-input  v-model="bankWx" style="width: 250px" disabled></el-input>
            </el-form-item>
            <el-form-item label="收款公司：">
                <el-input placeholder="请输入收款公司" v-model="form.company" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="年龄限制：">
                <el-input placeholder="最小年龄" v-model="form.ageStart" style="width: 100px"></el-input>
                至
                <el-input placeholder="最大年龄" v-model="form.ageEnd" style="width: 100px"></el-input>
            </el-form-item>
            <el-form-item label="税源地选择：">
                <el-select v-model="form.taxesId" placeholder="请选择" clearable style="width: 250px" filterable>
                    <el-option v-for="item in taxesList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="closeDia">取 消</el-button>
            <el-button type="primary" @click="merchantId ? toSubimitUpdata() : toSubmitInfo()">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>
import {addWxMerchantHttp, getTaxesList, getMerchantDetail, updateWxMerchantHttp } from '@/api/account/account.js';
import { Message } from 'element-ui';
export default {
    data() {
        return {
            dialogVisible: false,
            form: {
                authorizationType:'INFORMATION_AND_FUND_AUTHORIZATION_TYPE'
            },
        
            options: [],
            bankAllList: [],
            rateDetails: [
                {
                    minMoney: '',
                    maxMoney: '',
                    rate: ''
                }
            ],
            taxesList: [],
            bankWx:'微信银行'
        };
    },
    props: {
        toShowDia: {
            type: Boolean,
            require: true
        },
        merchantId: {
            type: String,
            require: false
        }
    },
    watch: {
        toShowDia(val) {
            this.dialogVisible = val;
        },
        merchantId(val) {
            if (val) {
                getMerchantDetail({
                    id: val
                }).then((res) => {
                    if (res.data.code == 0) {
                        this.form = res.data.data;
                    }
                });
            } else {
                this.form = {}
            }
        }
    },
    methods: {
      
        toDetelRate(index) {
            this.rateDetails.splice(index, 1);
        },
        openDia() {
        
        },
        closeDia() {
            this.form = {};
            this.$emit('closeDia');
        },
        toSubmitInfo() {
            this.$confirm('是否确认新增微信商户', '新增')
                .then(() => {
                    addWxMerchantHttp({
                        ...this.form,
                        authorizationType:"INFORMATION_AND_FUND_AUTHORIZATION_TYPE"
                    }).then((res) => {
                        if (res.data.code == 0) {
                            Message.success('新增微信商户成功！');
                            this.$emit('merchantSure');
                            this.form = {};
                        } else {
                            Message.error(res.data.msg);
                        }
                    });
                })
                .catch(() => {});
        },
        toSubimitUpdata() {
            this.$saveCode()
                .then((ress) => {
                    updateWxMerchantHttp({
                        ...this.form,
                        id: this.merchantId,
                        authorizationType:"INFORMATION_AND_FUND_AUTHORIZATION_TYPE",
                        password: ress.password
                    }).then((res) => {
                        if (res.data.code == 0) {
                            this.$emit('merchantSureNew');
                            this.form = {};
                        } else {
                            Message.error(res.data.msg);
                        }
                    });
                })
                .catch(() => {});
        }
    },
    created() {
        getTaxesList().then((res) => {
            if (res.data.code == 0) {
                this.taxesList = res.data.data;
            }
        });
    }
};
</script>

<style scoped>
.rate_box {
    align-items: center;
    display: flex;
    margin-bottom: 10px;
}
.rate_box:last-child {
    margin-bottom: 0px;
}
</style>
