<template>
    <div>
        <el-card shadow="never">
            <div slot="header" class="clearfix">
                <span>充值申请</span>
            </div>
            <el-form label-width="120px" :rules="rules" :model="form">
                <el-form-item label="钱包名称：">
                    {{ bankInfo.companyName }}
                </el-form-item>
                <el-form-item label="可开票金额："> ￥{{ bankInfo.currentTicket }} </el-form-item>
                <el-form-item label="充值金额：" prop="money">
                    <el-input v-model="form.money" @input="toWirteMoney" type="number" min="1" style="width: 200px"></el-input>
                </el-form-item>
                <el-form-item label="开票类目：">
                    <el-select v-model="form.invoiceCategory" style="width: 200px" filterable>
                        <el-option v-for="(item, index) in category" :key="index" :label="item.name" :value="item.id"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="发票类型：">
                    <el-select v-model="form.invoiceType" style="width: 200px" filterable>
                        <el-option v-for="(item, index) in type" :key="index" :label="item.name" :value="item.id"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="审批人员：" prop="auditUserId">
                    <el-select v-model="form.auditUserId" style="width: 200px">
                        <el-option v-for="(item, index) in peopleList" :key="index" :label="item.name" :value="item.id"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="充值备注：">
                    <el-input v-model="form.remark" style="width: 200px"> </el-input>
                </el-form-item>
                <el-form-item label="单据打包上传：">
                    <el-upload list-type="text"
                        :http-request="uploadURL" 
                        :multiple="true"
                        :on-success="uploadSuccess"
                        :on-error="uploadError"
                        :limit="1"
                     >
                        <i class="el-icon-upload"></i>
                    </el-upload>
                </el-form-item>
                <el-button type="primary" @click="toSubmitInfo">提交申请</el-button>
            </el-form>
        </el-card>
    </div>
</template>

<script>
import { digitUppercase } from '@/utils/moneyChange.js';
import { auditPersonList, ticketRecordInsert, getTypeInfoByWalletId } from '@/api/account/account.js';
import { client, getFileNameUUID,getTimeNow } from '../../../../utils/oss.js';
import { Message } from 'element-ui';
export default {
    data() {
        return {
            form: {
                money: ''
            },
            bankInfo: {},
            moneyStr: '零元整',
            rules: {
                money: [{ required: true, message: '请输入充值金额', trigger: 'blur' }],
                auditUserId: [{ required: true, message: '请选择审批人员', trigger: 'change' }]
            },
            peopleList: [],
            fileList: {},
            category: [],
            type: [],
            uploadSuccessed: false,
        };
    },
    created() {
        this.bankInfo = JSON.parse(this.$route.query.info);
        auditPersonList().then((res) => {
            if (res.data.code === 0) {
                this.peopleList = res.data.data;
            }
        });
        getTypeInfoByWalletId({
            walletId: this.bankInfo.id
        }).then((res) => {
            this.category = res.data.data.category;
            this.type = res.data.data.type;
        });
    },
    methods: {
        toWirteMoney() {
            this.moneyStr = digitUppercase(this.form.money);
        },
        handleBeforeUpload(file) {
            const isJPEG = file.name.split('.')[1] === 'zip';
            const isJPG = file.name.split('.')[1] === 'rar';
            const isPNG = file.name.split('.')[1] === 'png';
            if (!isJPG && !isJPEG && !isPNG && !isWEBP && !isGIF) {
                this.$message.error('上传图片只能是 zip/rar 格式!');
            }
            if (!isLt500K) {
                this.$message.error('单张图片大小不能超过 4mb!');
            }
            return (isJPEG || isJPG || isPNG || isWEBP || isGIF) && isLt500K;
        },
        uploadSuccess(res) {
            this.uploadSuccessed = true;
        },
        uploadError(err) {
            this.uploadSuccessed = false;
            console.log('error: ', err);
            this.$message.error('上传失败！请重试');
        },
        uploadURL(option) {
            this.uploadSuccessed = false;
            var suffix = option.file.name.substring(option.file.name.lastIndexOf('.'));
            var fileName = '/account/' + getTimeNow() + '/' + getFileNameUUID() + suffix;
            //定义唯一的文件名，打印出来的uid其实就是时间戳
            client()
                .multipartUpload(fileName, option.file, {
                    progress: function (percentage, cpt) {
                        option.onProgress({ percent: Math.floor(percentage * 100)});
                    }
                })
                .then((res) => {
                    if(res.res.status === 200) {
                        this.fileList = {
                            name: option.file.name,
                            url: fileName   
                        };
                        option.onSuccess(res);
                    }else {
                        option.onError(res);
                    }
                }).catch((err) => {
                    option.onError(err);
                });
        },
        toSubmitInfo() {
            var confirmText = uploadSuccessed? '未提供单据，确认提交申请?' : '确认提交申请?'; 
            this.$confirm(confirmText, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then((res) => {
                ticketRecordInsert({
                    file: this.fileList,
                    walletId: this.bankInfo.id,
                    ...this.form
                }).then((res) => {
                    if (res.data.code === 0) {
                        Message({
                            message: '提交成功',
                            type: 'success'
                        });
                        setTimeout(() => {
                            this.$router.replace('/account/invoicing');
                        }, 1300);
                    } else {
                        Message.error(res.data.msg);
                    }
                });
            });
        }
    }
};
</script>

<style scoped>
.upload_box {
    width: 80%;
    display: flex;
    flex-wrap: wrap;
}
.icon_size {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    color: #fff;
    font-size: 18px;
    opacity: 0;
    transition: 0.4s;
}
.pic_list {
    position: relative;
    width: 146px;
    height: 146px;
    margin-right: 15px;
    margin-bottom: 15px;
}
.pic_list:hover .icon_size {
    opacity: 1;
}
</style>