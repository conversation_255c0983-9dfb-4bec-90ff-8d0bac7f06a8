<template>
    <div>
        <el-card shadow="never">
            <div slot="header" class="clearfix">
                <span>充值申请</span>
            </div>
            <el-form label-width="120px" :rules="rules" :model="form">
                <el-form-item label="钱包名称：">
                    {{ bankInfo.companyName }}
                </el-form-item>
                <el-form-item label="收款公司：">
                    {{ bankInfo.company }}
                </el-form-item>
                <el-form-item label="收款账号：">
                    {{ bankInfo.accountNumber }}
                </el-form-item>
                <el-form-item label="收款银行：">
                    {{ bankInfo.bankName }}
                </el-form-item>
                <el-form-item label="钱包余额：">
                    {{ bankInfo.currentMoney }}
                </el-form-item>
                <el-form-item label="钱包费率：">
                    {{ bankInfo.rate }}
                </el-form-item>
                <el-form-item label="充值金额：" prop="money">
                    <el-input v-model="form.money" @input="toWirteMoney" type="number" min="1" style="width: 200px"></el-input>
                </el-form-item>
                <el-form-item label="大写金额：">
                    {{ moneyStr }}
                </el-form-item>
                <el-form-item label="审批人员：" prop="auditUserId">
                    <el-select v-model="form.auditUserId" style="width: 200px" filterable>
                        <el-option v-for="(item, index) in peopleList" :key="index" :label="item.name" :value="item.id"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="充值备注：">
                    <el-input v-model="form.remark" style="width: 200px"> </el-input>
                </el-form-item>
                <el-form-item label="图片凭证：">
                    <div class="upload_box">
                        <div class="pic_list" v-for="item in fileList" :key="item.url">
                            <el-image
                                style="width: 146px; height: 146px; margin-right: 15px; border-radius: 5px; display: block"
                                :src="ossUrl+item.url"
                            >
                            </el-image>
                            <i class="el-icon-delete icon_size" @click="toRemovePic(item.url)"></i>
                        </div>
                        <el-upload
                            action=""
                            list-type="picture-card"
                            :http-request="uploadURL"
                            :on-success="uploadSuccess"
                            :on-error="uploadError"
                            :multiple="true"
                            :show-file-list="false"
                            :before-upload="handleBeforeUpload"
                        >
                            <i class="el-icon-plus"></i>
                        </el-upload>
                    </div>
                </el-form-item>
                <el-button type="primary" @click="toSubmitInfo">提交申请</el-button>
            </el-form>
        </el-card>
    </div>
</template>

<script>
import { digitUppercase } from '@/utils/moneyChange.js';
import { auditPersonList, recordApplyInfo } from '@/api/account/account.js';
import { client, getFileNameUUID, getTimeNow } from '../../../../utils/oss.js';
import { Message } from 'element-ui';
import { OSS_URL } from '@/api/config';
export default {
    data() {
        return {
            form: {
                money: ''
            },
            bankInfo: {},
            moneyStr: '零元整',
            rules: {
                money: [{ required: true, message: '请输入充值金额', trigger: 'blur' }],
                auditUserId: [{ required: true, message: '请选择审批人员', trigger: 'change' }]
            },
            peopleList: [],
            fileList: [],
            ossUrl:OSS_URL
        };
    },
    created() {
        this.bankInfo = JSON.parse(this.$route.query.info);
        auditPersonList().then((res) => {
            if (res.data.code === 0) {
                this.peopleList = res.data.data;
            }
        });
    },
    methods: {
        toWirteMoney() {
            this.moneyStr = digitUppercase(this.form.money);
        },
        handleBeforeUpload(file) {
            const isJPEG = file.name.split('.')[1] === 'jpeg';
            const isJPG = file.name.split('.')[1] === 'jpg';
            const isPNG = file.name.split('.')[1] === 'png';
            const isWEBP = file.name.split('.')[1] === 'webp';
            const isGIF = file.name.split('.')[1] === 'gif';
            const isLt500K = file.size / 1024 / 1024 / 1024 / 1024 < 4;
            if (!isJPG && !isJPEG && !isPNG && !isWEBP && !isGIF) {
                this.$message.error('上传图片只能是 JPEG/JPG/PNG 格式!');
            }
            if (!isLt500K) {
                this.$message.error('单张图片大小不能超过 4mb!');
            }
            return (isJPEG || isJPG || isPNG || isWEBP || isGIF) && isLt500K;
        },
        uploadSuccess(res) {
            console.log('success: ', res);
        },
        uploadError(err) {
            console.log('error: ', err);
            this.$message.error('上传失败！请重试');
        },
        uploadURL(option) {
            var suffix = option.file.name.substring(option.file.name.lastIndexOf('.'));
            //注意哦，这里指定文件夹'image/'，尝试过写在配置文件，但是各种不行，写在这里就可以
            var fileName = '/account/' + getTimeNow() + '/' + getFileNameUUID() + suffix;
            //定义唯一的文件名，打印出来的uid其实就是时间戳
            client()
                .multipartUpload(fileName, option.file, {
                    headers: {
                        'Content-Type': 'image/jpg'
                    },
                    progress: function (percentage, cpt) {
                        option.onProgress({ percent: Math.floor(percentage * 100)});
                    }
                })
                .then((res) => {
                    if(res.res.status === 200) {
                        this.fileList.push({
                            name: option.file.name,
                            url: fileName
                        });
                        option.onSuccess(res);
                    }else {
                        option.onError(res);
                    }
                }).catch((err) => {
                    option.onError(err);
                });
        },
        toRemovePic(url) {
            this.fileLists.forEach((v, index) => {
                if (url == v.url) {
                    this.fileLists.splice(index, 1);
                }
            });
        },
        toSubmitInfo() {
            this.$confirm('确认提交申请?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then((res) => {
                recordApplyInfo({
                    fileList: this.fileList,
                    walletId: this.bankInfo.id,
                    ...this.form
                }).then((res) => {
                    if (res.data.code === 0) {
                        Message({
                            message: '提交成功',
                            type: 'success'
                        });
                        setTimeout(() => {
                            this.$router.replace('/account/recharge');
                        }, 1300);
                    }
                });
            });
        }
    }
};
</script>

<style scoped>
.upload_box {
    width: 80%;
    display: flex;
    flex-wrap: wrap;
}
.icon_size {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    color: #fff;
    font-size: 18px;
    opacity: 0;
    transition: 0.4s;
}
.pic_list {
    position: relative;
    width: 146px;
    height: 146px;
    margin-right: 15px;
    margin-bottom: 15px;
}
.pic_list:hover .icon_size {
    opacity: 1;
}
</style>