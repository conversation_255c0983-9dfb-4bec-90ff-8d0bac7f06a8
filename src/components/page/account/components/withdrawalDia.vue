<template>
    <el-dialog :title="`商户提现`" :visible.sync="dialogVisible" width="60%" @open="openDia" @close="closeDia">
        <el-form label-width="150px">
            <!-- <el-form-item label="银行账户名：">
                <el-input placeholder="银行账户名" v-model="form.accountName" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="银行账号：">
                <el-input placeholder="银行账号" v-model="form.accountNumber" style="width: 250px"></el-input>
            </el-form-item> -->
            <el-form-item label="当前商户：">{{ merchantName }}</el-form-item>
            <el-form-item label="商户余额：">{{ merchantMoney.toLocaleString('en-US') }}</el-form-item>
            <el-form-item label="提现金额：">
                <el-input placeholder="提现金额" v-model="form.money" type="number" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="支付宝名称：">
                <el-input placeholder="支付宝名称" v-model="form.alipayName" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="支付宝账号：">
                <el-input placeholder="支付宝账号" v-model="form.alipayAccount" style="width: 250px"></el-input>
            </el-form-item>

            <!-- <el-form-item label="类型：">
                <el-select v-model="form.taxesId" placeholder="请选择" clearable style="width: 250px" filterable>
                    <el-option v-for="item in taxesList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                </el-select>
            </el-form-item> -->
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="closeDia">取 消</el-button>
            <el-button type="primary" @click="toSubmitInfo()">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>
import {
    bankListAll,
    addMerchant,
    getTaxesList,
    getMerchantDetail,
    merchantUpdata,
    bankList,
    initiateWithdrawal
} from '@/api/account/account.js';
import { Message } from 'element-ui';
export default {
    data() {
        return {
            dialogVisible: false,
            form: {
                // bankUid: 'U004866757',
                // bankPubKey: 'BEynMEZOjNpwZIiD9jXtZSGr3Ecpwn7r+m+wtafXHb6VIZTnugfuxhcKASq3hX+KX9JlHODDl9/RDKQv4XLOFak=',
                // bankPrivKey: 'jDRmWHnPbqRDTXnxXPxV/Q/FyCaGgCo1nJsDNqX+On0=',
                // bankSmKey: '3QoqG9EEnzbWwzmb'
            },
            channelPtions: [
                {
                    channelId: 0,
                    channelName: '无上级'
                }
            ],
            options: [],
            bankAllList: [],
            rateDetails: [
                {
                    minMoney: '',
                    maxMoney: '',
                    rate: ''
                }
            ],
            taxesList: []
        };
    },
    props: {
        toShowDia: {
            type: Boolean,
            require: true
        },
        merchantId: {
            type: String,
            require: false
        },
        merchantMoney: {
            type: String | Number
        },
        merchantName: {
            type: String
        }
    },
    watch: {
        toShowDia(val) {
            this.dialogVisible = val;
        },
        merchantId(val) {}
    },
    methods: {
        openDia() {},
        closeDia() {
            this.$emit('closeDia');
        },
        toSubmitInfo() {
            this.$confirm('是否确认提现商户余额', '提现')
                .then(() => {
                    initiateWithdrawal({
                        ...this.form,
                        merchantId: this.merchantId,
                        type: 1
                    }).then((res) => {
                        if (res.data.code == 0) {
                            this.$emit('withdrawalSure');
                            this.form = {};
                        } else {
                            Message.error(res.data.msg);
                        }
                    });
                })
                .catch(() => {});
        },
        toSubimitUpdata() {
            this.$saveCode()
                .then((ress) => {
                    merchantUpdata({
                        ...this.form,
                        id: this.merchantId,
                        password: ress.password
                    }).then((res) => {
                        if (res.data.code == 0) {
                            this.$emit('merchantSureNew');
                            this.form = {};
                        } else {
                            Message.error(res.data.msg);
                        }
                    });
                })
                .catch(() => {});
        }
    },
    created() {
        getTaxesList().then((res) => {
            if (res.data.code == 0) {
                this.taxesList = res.data.data;
            }
        });
    }
};
</script>

<style scoped>
.rate_box {
    align-items: center;
    display: flex;
    margin-bottom: 10px;
}
.rate_box:last-child {
    margin-bottom: 0px;
}
</style>
