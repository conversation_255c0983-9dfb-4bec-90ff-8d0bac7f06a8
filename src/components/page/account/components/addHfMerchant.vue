<template>
    <el-dialog :title="merchantId?'修改汇付商户':'新增汇付商户'" :visible.sync="dialogVisible" width="60%" @open="openDia" @close="closeDia">
        <el-form label-width="150px" :model='form' ref='huifuForm' :rules='rules'>
            <el-form-item label="汇付号：" prop='huifuId'>
                <el-input placeholder="请输入汇付号" v-model="form.huifuId" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="商户名称：" prop='merchantName'>
                <el-input placeholder="请输入商户" v-model="form.merchantName" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="收款公司：" prop='company'>
                <el-input placeholder="请输入收款公司" v-model="form.company" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="年龄限制：" required>
                <el-col :span="3">
                    <el-form-item prop='ageStart' style='margin-bottom: 0;'>
                        <el-input placeholder="最小年龄" v-model.number="form.ageStart" style="width: 100px;"></el-input>
                    </el-form-item>
                </el-col>
                <el-col class="line" :span="1" style='text-align: center'>-</el-col>
                <el-col :span="20">
                    <el-form-item prop='ageEnd' style='margin-bottom: 0;'>
                        <el-input placeholder="最大年龄" v-model.number="form.ageEnd" style="width: 100px;"></el-input>
                    </el-form-item>
                </el-col>
            </el-form-item>
            <el-form-item label="税源地选择：" prop='taxesId'>
                <el-select v-model="form.taxesId" placeholder="请选择" clearable style="width: 250px" filterable>
                    <el-option v-for="item in taxesList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="closeDia">取 消</el-button>
            <el-button type="primary" @click="merchantId ? toSubimitUpdata() : toSubmitInfo()">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>
import { addHuifuMerchant, updateHuifuMerchant, getTaxesList, getMerchantDetail } from '@/api/account/account.js';
import { Message } from 'element-ui';
export default {
    data() {
        return {
            dialogVisible: false,
            form: {
                huifuId: undefined,
                merchantName: undefined,
                company: undefined,
                ageStart: undefined,
                ageEnd: undefined,
                taxesId: undefined
            },
            taxesList: [],
            rules: {
                huifuId: [{ required: true, message: '汇付号不能为空', trigger: 'blur' }],
                merchantName: [{ required: true, message: '商户名称不能为空', trigger: 'blur' }],
                company: [{ required: true, message: '公司名称不能为空', trigger: 'blur' }],
                ageStart: [{ required: true, message: '最小年龄不能为空', trigger: 'blur' }, { type: 'number', message: '年龄必须为数字', trigger: 'blur' }],
                ageEnd: [{ required: true, message: '最大年龄不能为空', trigger: 'blur' }, { type: 'number', message: '年龄必须为数字', trigger: 'blur' }],
                taxesId: [{ required: true, message: '税源地不能为空', trigger: 'change' }],
            },
        };
    },
    props: {
        toShowDia: {
            type: Boolean,
            require: true
        },
        merchantId: {
            type: String,
            require: false
        }
    },
    watch: {
        toShowDia(val) {
            this.dialogVisible = val;
        },
        merchantId(val) {
            if (val) {
                getMerchantDetail({
                    id: val
                }).then((res) => {
                    if (res.data.code == 0) {
                        this.form = res.data.data;
                    }
                });
            } else {
                this.form = {}
            }
        }
    },
    methods: {
      
        openDia() {
        
        },
        closeDia() {
            this.form = {};
            this.$emit('closeDia');
        },
        toSubmitInfo() {
            this.$refs.huifuForm.validate((valid) => {
                if (valid) {
                    this.$confirm('是否确认新增汇付商户', '新增')
                        .then(() => {
                            addHuifuMerchant({
                                ...this.form
                            }).then((res) => {
                                if (res.data.code == 0) {
                                    Message.success('新增汇付商户成功！');
                                    this.$emit('merchantSure');
                                    this.form = {};
                                } else {
                                    Message.error(res.data.msg);
                                }
                            });
                        })
                        .catch(() => {});
                }
            });
        },
        toSubimitUpdata() {
            this.$saveCode()
                .then(() => {
                    updateHuifuMerchant({
                        ...this.form,
                        id: this.merchantId,
                    }).then((res) => {
                        if (res.data.code == 0) {
                            this.$emit('merchantSureNew');
                            this.form = {};
                        } else {
                            Message.error(res.data.msg);
                        }
                    });
                })
                .catch(() => {});
        }
    },
    created() {
        console.log('hf merchantId after created(): ', this.merchantId);
        getTaxesList().then((res) => {
            if (res.data.code == 0) {
                this.taxesList = res.data.data;
            }
        });
    }
};
</script>
