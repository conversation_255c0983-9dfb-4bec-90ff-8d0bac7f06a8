<template>
    <el-dialog
        :title="merchantId ? '修改商户' : '新增支付宝商户'"
        :visible.sync="dialogVisible"
        width="60%"
        @open="openDia"
        @close="closeDia"
    >
        <el-form label-width="150px">
            <el-form-item label="支付宝账号：">
                <el-input placeholder="请输入支付宝账号" v-model="form.external_logon_id" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="商户名称：">
                <el-input placeholder="请输入商户" v-model="form.sub_merchant_name" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="收款公司：">
                <el-input placeholder="请输入收款公司" v-model="form.company" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="年龄限制：">
                <el-input placeholder="最小年龄" v-model="form.user_age_range.min" style="width: 100px"></el-input>
                至
                <el-input placeholder="最大年龄" v-model="form.user_age_range.max" style="width: 100px"></el-input>
            </el-form-item>
            <el-form-item label="税源地选择：">
                <el-select v-model="form.taxesId" placeholder="请选择" clearable style="width: 250px" filterable>
                    <el-option v-for="item in taxesList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <div style="margin: 30px 50px 0">
            <el-alert title="新增支付宝商户需要支付宝确认签约，信息填写后请打开支付宝进行签约确认~" type="warning" :closable="false">
            </el-alert>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="closeDia">取 消</el-button>
            <el-button type="primary" @click="merchantId ? toSubimitUpdata() : toSubmitInfo()">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>
import { bankList, addMerchant, getTaxesList, getMerchantDetail, merchantUpdata } from '@/api/account/account.js';
import { Message } from 'element-ui';
export default {
    data() {
        return {
            dialogVisible: false,
            form: {
                // bankUid: 'U004866757',
                // bankPubKey: 'BEynMEZOjNpwZIiD9jXtZSGr3Ecpwn7r+m+wtafXHb6VIZTnugfuxhcKASq3hX+KX9JlHODDl9/RDKQv4XLOFak=',
                // bankPrivKey: 'jDRmWHnPbqRDTXnxXPxV/Q/FyCaGgCo1nJsDNqX+On0=',
                // bankSmKey: '3QoqG9EEnzbWwzmb'
                user_age_range: {
                    min: '',
                    max: ''
                }
            },
            channelPtions: [
                {
                    channelId: 0,
                    channelName: '无上级'
                }
            ],
            options: [],
            bankAllList: [],
            rateDetails: [
                {
                    minMoney: '',
                    maxMoney: '',
                    rate: ''
                }
            ],
            taxesList: []
        };
    },
    props: {
        toShowDia: {
            type: Boolean,
            require: true
        },
        merchantId: {
            type: String,
            require: false
        }
    },
    watch: {
        toShowDia(val) {
            this.dialogVisible = val;
        },
        merchantId(val) {
            if (val) {
                getMerchantDetail({
                    id: val
                }).then((res) => {
                    if (res.data.code == 0) {
                        this.form = res.data.data;
                    }
                });
            } else {
                this.form = {
                    user_age_range: {
                        min: '',
                        max: ''
                    }
                };
            }
        }
    },
    methods: {
        toAddRateRow() {
            this.rateDetails.push({
                minMoney: '',
                maxMoney: '',
                rate: ''
            });
        },
        toDetelRate(index) {
            this.rateDetails.splice(index, 1);
        },
        openDia() {},
        closeDia() {
            this.form = {
                user_age_range: {
                    min: '',
                    max: ''
                }
            };
            this.$emit('closeDia');
        },
        toSubmitInfo() {
            this.$confirm('是否确认新增支付宝商户', '新增')
                .then(() => {
                    this.$emit('merchantSure', this.form);
                })
                .catch(() => {});
        },
        toSubimitUpdata() {
            this.$saveCode()
                .then((ress) => {
                    merchantUpdata({
                        ...this.form,
                        id: this.merchantId,
                        password: ress.password
                    }).then((res) => {
                        if (res.data.code == 0) {
                            this.$emit('merchantSureNew');
                            this.form = {};
                        } else {
                            Message.error(res.data.msg);
                        }
                    });
                })
                .catch(() => {});
        }
    },
    created() {
        bankList().then((res) => {
            if (res.data.code == 0) {
                this.bankAllList = res.data.data;
            }
        });
        getTaxesList().then((res) => {
            if (res.data.code == 0) {
                this.taxesList = res.data.data;
            }
        });
    }
};
</script>

<style scoped>
.rate_box {
    align-items: center;
    display: flex;
    margin-bottom: 10px;
}
.rate_box:last-child {
    margin-bottom: 0px;
}
</style>
