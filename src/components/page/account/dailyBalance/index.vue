<template>
    <div class="detail_box">
        <el-card class="left" shadow="never">
            <div slot="header" class="clearfix">
                <span>每日对账</span>
            </div>
            <el-form label-width="90px" :inline="true">
                <el-form-item label="机构选择：">
                    <el-select v-model="form.enterpriseId" filterable placeholder="请选择" clearable @change="toChangeEnterprise">
                        <el-option
                            :label="i.enterpriseName"
                            :value="i.enterpriseId"
                            v-for="i in listEnterprise"
                            :key="i.enterpriseId"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="钱包选择：">
                    <el-select v-model="form.walletId" filterable placeholder="请选择" clearable>
                        <el-option :label="i.walletStr" :value="i.walletId" v-for="i in walletBox" :key="i.walletId"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="日期选择：">
                    <el-date-picker
                        v-model="dateList"
                        type="daterange"
                        range-separator="~"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        @change="toChangeDate"
                        value-format="yyyy-MM-dd"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-button type="primary" @click="toSearch()" size="medium">搜 索</el-button>
                <el-button @click="toExport()" size="medium">导 出</el-button>
            </el-form>
            <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                <el-table-column prop="walletName" label="钱包名称" min-width="80" show-overflow-tooltip></el-table-column>
                <el-table-column label="所属机构" prop="enterpriseName"></el-table-column>
                <el-table-column prop="currentBalance" label="当日余额" min-width="140" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span class="money">￥{{ scope.row.currentBalance.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="当日日期" prop="date"></el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page="tableData.current"
                    layout="prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
        </el-card>
    </div>
</template>

<script>
import { Message } from 'element-ui';
import { merchantPage, lockStatus, companyList, walletList, getDailyList, walletDailyExport } from '@/api/account/account.js';
export default {
    data() {
        return {
            walletList: [],
            current: 1,
            size: 10,
            form: {
                enterpriseId: '',
                walletId: '',
                startTime: null,
                endDate: null
            },
            tableData: {},
            toShowDia: false,
            merchantId: undefined,
            listEnterprise: [],
            walletBox: [],
            dateList: []
        };
    },
    methods: {
        getData() {
            getDailyList({
                ...this.form,
                current: this.current,
                size: this.size
            }).then((res) => {
                if (res.data.code == 0) {
                    this.tableData = res.data.data;
                }
            });
        },
        toSearch() {
            this.current = 1;
            this.getData();
        },
        async toExport() {
            let res = await walletDailyExport(this.form);
            let excel = new Blob([res.data]);
            let url = URL.createObjectURL(excel);
            let a = document.createElement('a');
            a.href = url;
            a.download = '每日对账记录.xlsx';
            a.click();
        },
        handleSizeChange(size) {
            this.size = size;
            this.current = 1;
            this.getData();
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.current = current;
            this.getData();
        },
        toChangeEnterprise(val) {
            this.form.walletId = undefined;
            if (val) {
                walletList(val).then((res) => {
                    if (res.data.code === 0) {
                        this.walletBox = res.data.data.map((s) => {
                            s.walletStr = s.merchantsName + '-' + s.companyName + '-' + s.bankName;
                            return s;
                        });
                    }
                });
            } else {
                walletList('').then((res) => {
                    if (res.data.code === 0) {
                        this.walletBox = res.data.data.map((s) => {
                            s.walletStr = s.merchantsName + '-' + s.companyName + '-' + s.bankName;
                            return s;
                        });
                    }
                });
            }
        },
        toChangeDate(val) {
            if (val) {
                this.form.startTime = `${val[0]} 00:00:00`;
                this.form.endDate = `${val[1]} 23:59:59`;
            } else {
                this.form.startTime = null;
                this.form.endDate = null;
            }
        },
        lookModify(id) {
            this.merchantId = id;
            this.toShowDia = true;
        },
        toAddMerchant() {
            this.merchantId = undefined;
            this.toShowDia = true;
        }
    },
    created() {
        companyList().then((res) => {
            if (res.data.code === 0) {
                this.listEnterprise = res.data.data;
            }
        });
        walletList(this.form.enterpriseId).then((res) => {
            if (res.data.code === 0) {
                this.walletBox = res.data.data.map((s) => {
                    s.walletStr = s.merchantsName + '-' + s.companyName + '-' + s.bankName;
                    return s;
                });
            }
        });
        this.getData();
    }
};
</script>

<style scoped>
.company_name {
    display: inline-block;
    flex: 1;
}
.button_box {
    display: flex;
    align-items: center;
}
.topinfo {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.bank_name {
    font-size: 14px;
    border: 1px solid #999;
    padding: 5px;
    border-radius: 5px;
    color: #999;
}
.num_money {
    font-size: 40px;
    text-align: center;
    margin-top: 40px;
}
.word_info {
    text-align: center;
    margin-top: 10px;
    margin-bottom: 60px;
}
</style>
