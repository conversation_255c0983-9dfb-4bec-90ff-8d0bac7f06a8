<template>
    <div>
        <el-card shadow="never">
            <div slot="header" class="clearfix">
                <div class="tabbar-choose">
                    <span
                        :class="[item.id == tabbarId ? 'choose-item active' : 'choose-item']"
                        @click="toChangeTabbar(item.id)"
                        v-for="item in tabbarList"
                        :key="item.id"
                        >{{ item.name }}</span
                    >
                </div>
            </div>
            <template v-if="tabbarId == 1">
                <el-form :inline="true" ref="form" :model="form" class="demo-form-inline">
                    <el-form-item label="商户选择：">
                        <el-select v-model="form.merchantId" placeholder="全部商户" clearable filterable>
                            <el-option :label="i.merchantName" :value="i.id" v-for="i in merchantList" :key="i.id"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="打款方：">
                        <el-input v-model="form.drawingParty" placeholder="请输入打款方" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="收款方：">
                        <el-input v-model="form.payee" placeholder="请输入收款方" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="是否到账：">
                        <el-select v-model="form.isTransferAccount" placeholder="请选择" clearable>
                            <el-option label="否" :value="0"></el-option>
                            <el-option label="是" :value="1"></el-option>
                            <el-option label="钱包锁定，未到账" :value="2"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <!-- <el-button size="medium" @click="excelList">导出 </el-button> -->
                        <el-button type="primary" icon="el-icon-search" size="medium" @click="getDataSearch">查 询 </el-button>
                    </el-form-item>
                </el-form>
                <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                    <el-table-column type="index" width="60" label="序号" fixed></el-table-column>
                    <el-table-column prop="merchantName" label="商户名称" min-width="80" fixed show-overflow-tooltip></el-table-column>
                    <el-table-column prop="company" label="打款方" min-width="120" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <div>{{ scope.row.drawingPartyName.replace('有限公司', '') }}</div>
                            <div>{{ scope.row.drawingPartyAccount }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="收款方" min-width="120" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <div>{{ scope.row.payeeName.replace('有限公司', '') }}</div>
                            <div>{{ scope.row.payeeAccount }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="流水单号" min-width="100" prop="orderNo"></el-table-column>
                    <el-table-column prop="money" label="打款金额">
                        <template slot-scope="scope">
                            <span class="money" :style="{ color: scope.row.money > 0 ? '#ff6600' : '' }">{{ scope.row.moneyStr }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="商户钱包" min-width="120"  show-overflow-tooltip>
                        <template slot-scope="scope">
                            <div v-if="scope.row.isTransferAccount != 2">
                                <div v-if="!scope.row.walletId" style="color: red">未到账</div>
                                <div v-else>
                                    <div>{{ scope.row.walletStr.replace('有限公司', '') }}</div>
                                    <div style="display:flex; align-items: center; ">
                                        <span>{{ scope.row.bankName }}</span>
                                        <img width="16px" src="@/assets/img/zsyh.png" v-show="scope.row.bankName.indexOf('招商') > -1 || scope.row.bankName.indexOf('招行') > -1" style="margin-left: 3px"/>
                                        <img width="16px" src="@/assets/img/alipay.png" v-show="scope.row.bankName.indexOf('支付宝') > -1" style="margin-left: 3px"/>
                                        <img width="16px" src="@/assets/img/pabank.png" v-show="scope.row.bankName.indexOf('平安') > -1" style="margin-left: 3px"/>
                                        <img width="16px" src="@/assets/img/wpay.png" v-show="scope.row.bankName.indexOf('微信') > -1" style="margin-left: 3px" />
                                    </div>
                                </div>
                            </div>
                            <div v-else>
                                <div>{{ scope.row.walletStr.replace('有限公司', '') }}</div>
                                <div style="color: red">[钱包已锁定，无法到账]</div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="银行到账/系统拉取日期" prop="" width="200" align="center">
                        <template slot-scope="scope">
                            <div>{{ scope.row.bankArrivalTime }}</div>
                            <div>{{ scope.row.systemArrivalTime }}</div>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="pagination">
                    <el-pagination
                        @current-change="handleCurrentChange"
                        :current-page="current"
                        layout="prev, pager, next, jumper"
                        :total="tableData.total"
                    ></el-pagination>
                </div>
            </template>
            <template v-if="tabbarId == 2">
                <el-form :inline="true" ref="forms" :model="forms" class="demo-form-inline">
                    <el-form-item label="类型：">
                        <el-select v-model="forms.type" placeholder="请选择" clearable filterable>
                            <el-option label="充值" :value="1"></el-option>
                            <el-option label="划拨" :value="2"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="付款方：">
                        <el-input v-model="forms.payerInfo" placeholder="请输入打款方" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="打款方：">
                        <el-input v-model="forms.payeeInfo" placeholder="请输入打款方" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="是否到账：">
                        <el-select v-model="forms.resType" placeholder="请选择" clearable>
                            <el-option label="否" value="error"></el-option>
                            <el-option label="是" value="success"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <!-- <el-button size="small" @click="excelList">导出 </el-button> -->
                        <el-button type="primary" icon="el-icon-search" size="medium" @click="getDataSearch">查 询 </el-button>
                    </el-form-item>
                </el-form>
                <el-table :data="tableDatas.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                    <el-table-column type="index" width="60" label="序号" fixed></el-table-column>
                    <el-table-column label="类型" width="60" fixed>
                        <template slot-scope="scope">
                            <div>{{ scope.row.type == 1 ? '充值' : '划拨' }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="company" label="付款方" min-width="120" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <div>{{ scope.row.payerType }}</div>
                            <div>{{ scope.row.payerIdentity.replace('有限公司', '') }}</div>
                        </template>
                    </el-table-column>

                    <el-table-column label="收款方" min-width="100" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <div>{{ scope.row.payeeType }}</div>
                            <div>{{ scope.row.payeeIdentity.replace('有限公司', '') }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="流水单号" min-width="120">
                        <template slot-scope="scope">
                            <div>外部订单号：{{ scope.row.orderNO.split('/')[0] }}</div>
                            <div>{{ scope.row.type == 1 ? '充值' : '划拨' }}：{{ scope.row.orderNO.split('/')[1] }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="money" label="打款金额" width="120">
                        <template slot-scope="scope">
                            <span class="money" :style="{ color: scope.row.amount > 0 ? '#ff6600' : '' }">{{ scope.row.moneyStr }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="resMsg" label="处理结果" min-width="150"> </el-table-column>
                    <el-table-column label="操作时间" prop="createDate" width="200"></el-table-column>
                </el-table>
                <div class="pagination">
                    <el-pagination
                        @current-change="handleCurrentChange"
                        :current-page="current"
                        layout="prev, pager, next, jumper"
                        :total="tableDatas.total"
                    ></el-pagination>
                </div>
            </template>
            <template v-if="tabbarId == 3">
                <el-form :inline="true" ref="formss" :model="forms" class="demo-form-inline">
                    <!-- <el-form-item label="类型：">
                        <el-select v-model="formss.type" placeholder="请选择" clearable filterable>
                            <el-option label="充值" :value="1"></el-option>
                            <el-option label="划拨" :value="2"></el-option>
                        </el-select>
                    </el-form-item> -->

                    <el-form-item label="收款方：">
                        <el-input v-model="formss.payerInfo" placeholder="请输入收款方" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="付款方：">
                        <el-input v-model="formss.payeeInfo" placeholder="请输入付款方" clearable></el-input>
                    </el-form-item>
                    <!-- <el-form-item label="是否到账：">
                        <el-select v-model="formss.resType" placeholder="请选择" clearable>
                            <el-option label="否" value="error"></el-option>
                            <el-option label="是" value="success"></el-option>
                        </el-select>
                    </el-form-item> -->
                    <el-form-item>
                        <!-- <el-button size="medium" @click="excelList">导出 </el-button> -->
                        <el-button type="primary" icon="el-icon-search" size="medium" @click="getDataSearch">查 询 </el-button>
                    </el-form-item>
                </el-form>
                <el-table :data="tableDatass.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                    <el-table-column type="index" width="60" label="序号"></el-table-column>
                    <el-table-column label="商户名称" min-width="80" prop="mearchName"></el-table-column>
                    <el-table-column prop="company" label="付款方" min-width="120">
                        <template slot-scope="scope">
                            <div>{{ scope.row.bankAccountName.replace('有限公司', '') }}</div>
                            <div>{{ scope.row.bankAccountNumber }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="收款方" min-width="120">
                        <template slot-scope="scope">
                            <div>{{ scope.row.mearchName }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="流水单号" min-width="200" prop="incomeRecordId"></el-table-column>
                    <el-table-column prop="money" label="打款金额" width="120">
                        <template slot-scope="scope">
                            <span class="money" :style="{ color: scope.row.amount > 0 ? '#ff6600' : '' }">{{ scope.row.moneyStr }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作时间" prop="createTime" width="200"></el-table-column>
                </el-table>
                <div class="pagination">
                    <el-pagination
                        @current-change="handleCurrentChange"
                        :current-page="current"
                        layout="prev, pager, next, jumper"
                        :total="tableDatass.total"
                    ></el-pagination>
                </div>
            </template>
        </el-card>
    </div>
</template>

<script>
import { getTransferAccountPage, excelList, listRcMerchant, getAliPayRecord, getWXRecord } from '@/api/account/account.js';
import { getFileNameUUID } from '@/utils/oss.js';
export default {
    data() {
        return {
            form: {},
            tableData: {},
            current: 1,
            size: 10,
            listEnterprise: [],
            walletBox: [],
            merchantList: [],
            tabbarId: 1,
            forms: {},
            tabbarList: [
                {
                    name: '银企直连到账记录',
                    id: 1
                },
                {
                    name: '支付宝到账记录',
                    id: 2
                },
                {
                    name: '微信到账记录',
                    id: 3
                }
            ],
            tableDatas: {},
            tableDatass:{},
            formss:{

            }
        };
    },
    created() {
        listRcMerchant().then((res) => {
            this.merchantList = res.data.data;
        });
        this.getData();
    },
    methods: {
        getInoutStr(id) {
            let str = '';
            this.inOutList.forEach((s) => {
                if (s.id == id) {
                    str = s.name;
                }
            });
            return str;
        },
        toChangeTime(date) {
            if (date) {
                this.form.startTime = date[0] + ' ' + '00:00:00';
                this.form.endTime = date[1] + ' ' + '23:59:59';
            } else {
                this.form.startTime = '';
                this.form.endTime = '';
            }
        },
        toChangeTabbar(id) {
            this.tabbarId = id;
            this.tableDatas.current = 1;
            this.tableData.current = 1;
            this.form = {};
            this.forms = {};
            this.current = 1;
            this.$forceUpdate();
            this.getData();
        },
        handleSizeChange(size) {
            this.size = size;
            this.current = 1;
            this.getData();
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.current = current;
            this.getData(current);
        },
        getData() {
            getTransferAccountPage({
                ...this.form,
                current: this.current,
                size: this.size
            }).then((res) => {
                if (res.data.code === 0) {
                    res.data.data.records.forEach((v) => {
                        v.walletStr = v.merchantName + '-' + v.walletName;
                        v.moneyStr = '';
                        v.moneyStr = '￥' + v.money.toLocaleString('en-US');
                    });
                    this.tableData = res.data.data;
                }
            });
            getAliPayRecord({
                ...this.forms,
                current: this.current,
                size: this.size
            }).then((res) => {
                if (res.data.data.code === 0) {
                    res.data.data.data.records.forEach((v) => {
                        v.moneyStr = '';
                        v.moneyStr = '￥' + v.amount.toLocaleString('en-US');
                    });
                    this.tableDatas = res.data.data.data;
                }
            });
            getWXRecord({
                ...this.formss,
                current: this.current,
                size: this.size
            }).then((res) => {
                console.log(res.data.data,'到底有啥')
                if (res.data.code === 0) {
                    res.data.data.records.forEach((v) => {
                        v.moneyStr = '';
                        v.moneyStr = '￥' + v.amount.toLocaleString('en-US');
                    });
                    this.tableDatass = res.data.data;
                }
            });
        },
        getDataSearch() {
            this.current = 1;
            this.getData();
        },
        excelList() {
            excelList({
                ...this.form
            }).then((res) => {
                let excel = new Blob([res.data]);
                let url = URL.createObjectURL(excel);
                let a = document.createElement('a');
                a.href = url;
                a.download = '导出账单' + getFileNameUUID() + '.zip';
                a.click();
            });
        }
    }
};
</script>

<style scoped>
.money_box {
    display: flex;
    justify-content: space-around;
    font-size: 14px;
    color: #989898;
    margin-bottom: 20px;
}
.money_box span {
    font-weight: 550;
    font-size: 18px;
}
.tabbar-choose {
    display: flex;
    align-items: center;
}
.choose-item {
    color: #c0c0c0;
    font-size: 16px;
    margin-right: 20px;
    user-select: none;
    cursor: pointer;
    padding-bottom: 4px;
    border-bottom: 3px solid transparent;
}
.choose-item.active {
    color: #409eff;
    font-size: 18px;
    margin-right: 20px;
    padding-bottom: 4px;
    border-bottom: 3px solid #409eff;
}
</style>
