<template>
    <div>
        <el-card>
            <div slot="header" class="clearfix">
                <span>商户提现记录</span>
            </div>
            <el-form :inline="true" ref="form" :model="form" class="demo-form-inline">
                <el-form-item label="选择商户">
                    <el-select v-model="form.merchantId" placeholder="全部商户" clearable filterable @change="toChangeValue">
                        <el-option :label="i.merchantName" :value="i.id" v-for="i in listRcMerchant" :key="i.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="账户名称">
                    <el-input placeholder="输入账户名称" v-model="form.accountName" clearable></el-input>
                </el-form-item>
                <el-form-item label="提现账号">
                    <el-input placeholder="输入提现账号" v-model="form.accountNumber" clearable></el-input>
                </el-form-item>
                <el-form-item label="订单号">
                    <el-input placeholder="输入订单号" v-model="form.orderNumber" clearable></el-input>
                </el-form-item>
                <el-form-item label="选择结果">
                    <el-select v-model="form.resultType" placeholder="选择结果" clearable filterable>
                        <el-option label="成功" :value="1"></el-option>
                        <el-option label="失败" :value="2"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="创建时间">
                    <el-date-picker
                        v-model="form.date"
                        type="daterange"
                        range-separator="~"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd"
                        clearable
                        @change="toChangeTime"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="medium" @click="getDataNeed">查 询 </el-button>
                    <el-button size="medium" @click="getExcel">导 出</el-button>
                </el-form-item>
            </el-form>
            <el-table :data="tableData.records" stripe style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                <el-table-column prop="mearchatName" label="商户名称" min-width="120" fixed="left"></el-table-column>
                <el-table-column prop="accountName" label="账户名称"></el-table-column>
                <el-table-column prop="accountNumber" label="账户" min-width="80" show-overflow-tooltip></el-table-column>
                <el-table-column label="账户订单号" prop="orderNumber" min-width="120">
                    <template slot-scope="scope">
                        <div v-if="scope.row.orderNumber">{{ scope.row.orderNumber }}</div>
                        <div v-else>-</div>
                    </template>
                </el-table-column>
                <el-table-column label="总金额" prop="money">
                    <template slot-scope="scope">
                        <div class="money" v-if="scope.row.money > 0" style="color: #ff6600">+￥{{ scope.row.money.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</div>
                        <div class="money" v-else>￥{{ scope.row.money.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="状态" min-width="60" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span v-if="scope.row.resultType == 1">成功</span>
                        <span v-if="scope.row.resultType == 2">失败</span>
                    </template>
                </el-table-column>
                <el-table-column min-width="120" label="备注" prop="remark"></el-table-column>
                <el-table-column label="创建时间/交割时间" min-width="100" prop="createTime">
                    <template slot-scope="scope">
                        <div>{{ scope.row.startTime }}</div>
                        <div>{{ scope.row.endTime }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="图片凭证">
                    <template slot-scope="scope">
                        <el-button v-if="scope.row.picFileUrl" @click="toLookUrl(scope.row.picFileUrl)">查看凭证</el-button>
                        <span v-if="!scope.row.picFileUrl">-</span>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page="tableData.current"
                    layout="prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
        </el-card>
        <el-dialog title="驳回" :visible.sync="dialogVisible" width="40%" @close="closeDia">
            <el-form label-width="80px">
                <el-form-item label="驳回理由">
                    <el-input type="textarea" v-model="remark"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeDia">取 消</el-button>
                <el-button type="primary" @click="sureRecall">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import {
    bussnessList,
    companyList,
    walletRecordAudit,
    listWalletByUser,
    rcpageWithdrawalRecords,
    exportWithdrawal
} from '@/api/account/account.js';
import { Message } from 'element-ui';
import { OSS_URL } from '@/api/config.js';
export default {
    data() {
        return {
            statusBox: [
                {
                    name: '全部',
                    id: undefined
                },
                {
                    name: '待审核',
                    id: 1
                },
                {
                    name: '已完成',
                    id: 2
                },
                {
                    name: '已取消',
                    id: 3
                },
                {
                    name: '操作中',
                    id: 4
                },
                {
                    name: '已完成（有退票）',
                    id: 5
                }
            ],
            form: {
                current: 1,
                size: 10,
                merchantId: undefined,
                enterpriseId: undefined,
                status: undefined,
                startTime: '',
                endTime: ''
            },
            tableData: {},
            tableDatas: [],
            listRcMerchant: [],
            listEnterprise: [],
            remark: '',
            dialogVisible: false,
            rowId: '',
            walletBox: []
        };
    },
    methods: {
        toChangeValue(val) {
            this.form.enterpriseId = '';
            if (val) {
                companyList(val).then((res) => {
                    if (res.data.code === 0) {
                        this.listEnterprise = res.data.data;
                    }
                });
                listWalletByUser(val).then((res) => {
                    this.walletBox = res.data.data.map((s) => {
                        s.walletStr = s.merchantsName + '-' + s.companyName + '-' + s.bankName;
                        return s;
                    });
                });
            } else {
                companyList().then((res) => {
                    if (res.data.code === 0) {
                        this.listEnterprise = res.data.data;
                    }
                });
                listWalletByUser('').then((res) => {
                    this.walletBox = res.data.data.map((s) => {
                        s.walletStr = s.merchantsName + '-' + s.companyName + '-' + s.bankName;
                        return s;
                    });
                });
            }
        },
        getData() {
            rcpageWithdrawalRecords({
                ...this.form
            }).then((res) => {
                if (res.data.code === 0) {
                    this.tableData = res.data.data;
                }
            });
        },
        getDataNeed() {
            this.form.current = 1;
            this.getData();
        },
        //分页-每页条数改变
        handleSizeChange(size) {
            this.form.size = size;
            this.form.current = 1;
            this.getData();
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.form.current = current;
            this.getData(current);
        },
        handleClick(tab, event) {
            if (tab.paneName != '0') {
                this.form.status = parseInt(tab.paneName);
                this.form.current = 1;
                this.getData();
            } else {
                this.form.status = undefined;
                this.form.current = 1;
                this.getData();
            }
        },
        toChangeTime(date) {
            if (date) {
                this.form.startTime = date[0] + ' ' + '00:00:00';
                this.form.endTime = date[1] + ' ' + '23:59:59';
            } else {
                this.form.startTime = '';
                this.form.endTime = '';
            }
        },
        toRecall(id) {
            this.$confirm(`确定要驳回吗？`, '提示', {
                type: 'warning'
            })
                .then(() => {
                    this.dialogVisible = true;
                    this.rowId = id;
                })
                .catch(() => {});
        },
        toPass(id) {
            this.$confirm(`确定要通过吗？`, '提示', {
                type: 'warning'
            })
                .then(() => {
                    this.rowId = id;
                    walletRecordAudit({
                        remark: '',
                        id: id,
                        auditStatus: 2
                    }).then((res) => {
                        if (res.data.code == 0) {
                            this.getData();
                        } else {
                            return Message.error(res.data.msg);
                        }
                    });
                })
                .catch(() => {});
        },
        sureRecall() {
            if (!this.remark) return Message.error('请输入驳回理由！');
            walletRecordAudit({
                remark: this.remark,
                id: this.rowId,
                auditStatus: 3
            }).then((res) => {
                if (res.data.code == 0) {
                    this.dialogVisible = false;
                    this.getData();
                } else {
                    return Message.error(res.data.msg);
                }
            });
        },
        toLookUrl(url){
            window.open(OSS_URL + '/' + url);
        },
        closeDia() {
            this.remark = '';
            this.dialogVisible = false;
        },
        async getExcel() {
            let res = await exportWithdrawal(this.form);
            const blob = new Blob([res.data]);
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = '提现记录.xlsx';
            link.click();
            URL.revokeObjectURL(link.href);
        }
    },
    created() {
        this.getData();
        bussnessList().then((res) => {
            if (res.data.code === 0) {
                this.listRcMerchant = res.data.data;
            }
        });
        companyList().then((res) => {
            if (res.data.code === 0) {
                this.listEnterprise = res.data.data;
            }
        });
        listWalletByUser('').then((res) => {
            this.walletBox = res.data.data.map((s) => {
                s.walletStr = s.merchantsName + '-' + s.companyName + '-' + s.bankName;
                return s;
            });
        });
    }
};
</script>

<style scoped></style>
