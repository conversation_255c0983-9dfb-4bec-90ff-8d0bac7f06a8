<template>
    <div>
        <el-card>
            <el-tabs @tab-click="handleClick" style="box-shadow: none;" class="tab-title-lg">
                <el-tab-pane :label="item.name" v-for="(item, index) in statusBox" :key="index">
                    <el-form :inline="true" ref="form" :model="form" class="demo-form-inline">
                        <el-form-item label="选择商户">
                            <el-select v-model="form.merchantId" placeholder="全部商户" clearable filterable @change="toChangeValue">
                                <el-option :label="i.merchantName" :value="i.id" v-for="i in listRcMerchant" :key="i.id"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="商户钱包">
                            <el-select v-model="form.walletId" placeholder="全部商户" clearable filterable>
                                <el-option :label="i.walletStr" :value="i.walletId" v-for="i in walletBox" :key="i.walletId"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="发起方">
                            <el-select v-model="form.enterpriseId" placeholder="全部发起方" clearable filterable>
                                <el-option
                                    :label="i.enterpriseName"
                                    :value="i.enterpriseId"
                                    v-for="i in listEnterprise"
                                    :key="i.id"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="创建时间">
                            <el-date-picker
                                v-model="form.date"
                                type="daterange"
                                range-separator="~"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                value-format="yyyy-MM-dd"
                                clearable
                                @change="toChangeTime"
                            >
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" icon="el-icon-search" size="medium" @click="getDataNeed">查 询 </el-button>
                            <el-button size="medium" @click="getExcel">导 出</el-button>
                        </el-form-item>
                    </el-form>

                    <!-- <div class="money_box">
                        <div>
                            已完成&nbsp;&nbsp;<span style="color: #318000">{{ readyMoney }}</span>
                        </div>
                        <div>
                            有退票&nbsp;&nbsp;<span style="color: #ffa500">{{ backMoney }}</span>
                        </div>
                        <div>
                            已取消&nbsp;&nbsp;<span style="color: #ff0609">{{ removeMoney }}</span>
                        </div>
                        <div>
                            未审核&nbsp;&nbsp;<span style="color: #2c3034">{{ auditMoney }}</span>
                        </div>
                    </div> -->

                    <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                        <el-table-column prop="walletStr" label="商户钱包" width="150" fixed="left" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <div>{{ scope.row.merchantName }}</div>
                                <div style="display:flex; align-items: center; ">
                                    <span>{{ scope.row.bankName }}</span>
                                    <img width="16px" src="@/assets/img/zsyh.png" v-show="scope.row.bankName.indexOf('招商') > -1 || scope.row.bankName.indexOf('招行') > -1" style="margin-left: 3px"/>
                                    <img width="16px" src="@/assets/img/alipay.png" v-show="scope.row.bankName.indexOf('支付宝') > -1" style="margin-left: 3px"/>
                                    <img width="16px" src="@/assets/img/pabank.png" v-show="scope.row.bankName.indexOf('平安') > -1" style="margin-left: 3px"/>
                                    <img width="16px" src="@/assets/img/wpay.png" v-show="scope.row.bankName.indexOf('微信') > -1" style="margin-left: 3px" />
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="enterpriseName" label="发起方" min-width="250" show-overflow-tooltip></el-table-column>
                        <el-table-column prop="createName" label="发起人" width="150" show-overflow-tooltip></el-table-column>
                        <el-table-column label="审批人" prop="auditUserName" width="150" show-overflow-tooltip> </el-table-column>
                        <el-table-column label="总金额" prop="money" width="120">
                            <template slot-scope="scope">
                                <div class="money" v-if="scope.row.money > 0" style="color: #ff6600">+{{ scope.row.money.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</div>
                                <div class="money" v-else>{{ scope.row.money.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column label="状态" width="100" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <span v-if="scope.row.auditStatus == 1">待审核</span>
                                <span v-if="scope.row.auditStatus == 2">已完成</span>
                                <span v-if="scope.row.auditStatus == 3">已取消</span>
                                <span v-if="scope.row.auditStatus == 4">系统处理中</span>
                                <span v-if="scope.row.auditStatus == 6">处理失败</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="类型" width="100" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <span v-if="scope.row.inOutType == 1">系统充值</span>
                                <span v-if="scope.row.inOutType == 2">人工调账</span>
                                <span v-if="scope.row.inOutType == 9">企业退款</span>
                            </template>
                        </el-table-column>
                        <el-table-column min-width="200" label="备注" prop="remark" show-overflow-tooltip></el-table-column>
                        <el-table-column label="流水单号" min-width="200" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <div v-if="scope.row.orderNo">
                                    <div v-if="scope.row.mearchantType == 2">
                                        <div>充值：{{ scope.row.orderNo.split('/')[0] }}</div>
                                        <div>划拨：{{ scope.row.orderNo.split('/')[1] }}</div>
                                    </div>
                                    <div v-else>
                                        {{ scope.row.orderNo.split('/')[0] }}
                                    </div>
                                </div>
                                <div v-else>-</div>
                            </template>
                        </el-table-column>
                        <el-table-column label="创建时间/交割时间" width="200" prop="createTime"  show-overflow-tooltip>
                            <template slot-scope="scope">
                                <div>{{ scope.row.createTime }}</div>
                                <div>{{ scope.row.updateTime }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column label="交易凭证" width="150">
                            <template slot-scope="scope">
                                <el-button v-if="scope.row.picFlag" @click="toLookDetail(scope.row.fileList)"> 查看凭证 </el-button><br/>
                                <el-button v-if="scope.row.picFlag" @click="toDownload(scope.row)"> 下载凭证 </el-button>
                                <span v-if="!scope.row.picFlag">-</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" fixed="right" width="100" v-has="'platform_recharge_audit'">
                            <template slot-scope="scope">
                                <div v-if="scope.row.isAudit && scope.row.auditStatus == 1">
                                    <el-button type="text" @click="toRecall(scope.row)" style="font-size: 15px;">撤回</el-button>
                                    <el-button type="text" @click="toPass(scope.row)" style="font-size: 15px;">通过</el-button>
                                </div>
                                <span v-if="!scope.row.isAudit">-</span>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
                <div class="pagination">
                    <el-pagination
                        @current-change="handleCurrentChange"
                        :current-page="tableData.current"
                        layout="prev, pager, next, jumper"
                        :total="tableData.total"
                    ></el-pagination>
                </div>
            </el-tabs>
        </el-card>
        <el-dialog title="驳回" :visible.sync="dialogVisible" width="40%" @close="closeDia">
            <el-form label-width="80px">
                <el-form-item label="驳回理由">
                    <el-input type="textarea" v-model="remark"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeDia">取 消</el-button>
                <el-button type="primary" @click="sureRecall">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { rechargeList, bussnessList, companyList, walletRecordAudit, exportExcel, listWalletByUser, auditWalletRefund } from '@/api/account/account.js';
import { Message } from 'element-ui';
import { OSS_URL } from '@/api/config.js';
export default {
    data() {
        return {
            statusBox: [
                {
                    name: '全部',
                    id: undefined
                },
                {
                    name: '待审核',
                    id: 1
                },
                {
                    name: '已完成',
                    id: 2
                },
                {
                    name: '已取消',
                    id: 3
                },
                {
                    name: '操作中',
                    id: 4
                },
                {
                    name: '已完成（有退票）',
                    id: 5
                }
            ],
            form: {
                current: 1,
                size: 10,
                merchantId: undefined,
                enterpriseId: undefined,
                status: undefined,
                startTime: '',
                endTime: ''
            },
            tableData: {},
            tableDatas: [],
            listRcMerchant: [],
            listEnterprise: [],
            remark: '',
            dialogVisible: false,
            curRow: undefined,
            walletBox: [],
            readyMoney: 0, //已完成
            backMoney: 0, //已退票
            removeMoney: 0, //已取消
            auditMoney: 0, //待审核
        };
    },
    methods: {
        toChangeValue(val) {
            this.form.enterpriseId = '';
            if (val) {
                companyList(val).then((res) => {
                    if (res.data.code === 0) {
                        this.listEnterprise = res.data.data;
                    }
                });
                listWalletByUser(val).then((res) => {
                    this.walletBox = res.data.data.map((s) => {
                        s.walletStr = s.merchantsName + '-' + s.companyName + '-' + s.bankName;
                        return s;
                    });
                });
            } else {
                companyList().then((res) => {
                    if (res.data.code === 0) {
                        this.listEnterprise = res.data.data;
                    }
                });
                listWalletByUser('').then((res) => {
                    this.walletBox = res.data.data.map((s) => {
                        s.walletStr = s.merchantsName + '-' + s.companyName + '-' + s.bankName;
                        return s;
                    });
                });
            }
        },
        getData() {
            // rechargeStatisticalMoney({
            //     ...this.form
            // }).then((res) => {
            //     if (res.data.code === 0) {
            //         res.data.data.forEach((s) => {
            //             if (s.auditStatus == 2) {
            //                 this.readyMoney = parseFloat(s.sumMoney).toLocaleString('en-US');
            //             } else if (s.auditStatus == 5) {
            //                 this.backMoney = parseFloat(s.sumMoney).toLocaleString('en-US');
            //             } else if (s.auditStatus == 3) {
            //                 this.removeMoney = parseFloat(s.sumMoney).toLocaleString('en-US');
            //             } else if (s.auditStatus == 1) {
            //                 this.auditMoney = parseFloat(s.sumMoney).toLocaleString('en-US');
            //             }
            //         });
            //     }
            // });
            rechargeList({
                ...this.form
            }).then((res) => {
                if (res.data.code === 0) {
                    res.data.data.records.forEach((v) => {
                        v.fileListBig = [];
                        v.walletStr = v.merchantName + '-' + v.bankName;
                        if (v.fileList.length > 0) {
                            v.fileList.forEach((element) => {
                                v.fileListBig.push(OSS_URL + element.url);
                            });
                        }
                    });
                    this.tableData = res.data.data;
                }
            });
        },
        getDataNeed() {
            this.form.current = 1;
            this.getData();
        },
        //分页-每页条数改变
        handleSizeChange(size) {
            this.form.size = size;
            this.form.current = 1;
            this.getData();
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.form.current = current;
            this.getData(current);
        },
        handleClick(tab, event) {
            if (tab.paneName != '0') {
                this.form.status = parseInt(tab.paneName);
                this.form.current = 1;
                this.getData();
            } else {
                this.form.status = undefined;
                this.form.current = 1;
                this.getData();
            }
        },
        toChangeTime(date) {
            if (date) {
                this.form.startTime = date[0] + ' ' + '00:00:00';
                this.form.endTime = date[1] + ' ' + '23:59:59';
            } else {
                this.form.startTime = '';
                this.form.endTime = '';
            }
        },
        toLookDetail(urlList){
            window.open(OSS_URL + urlList[0].url)
        },
        toDownload(row){
            // rename and download pdf file
            var pdfUrl = OSS_URL + '/' + row.fileList[0].url;
            // 流水号_姓名_金额_时间.pdf,
            var orderNo = row.orderNo? row.orderNo.split('/')[0] : "";
            var pdfName =
                (orderNo || "") + '_' +
                (row.name || "") + '_' +
                (row.money || "") + '_' +
                (row.createTime.replace(/[:]/g, '')) + '.pdf';
            if (window.navigator.msSaveBlob) {
                var xhr = new XMLHttpRequest();
                xhr.open('GET', pdfUrl, true);
                xhr.responseType = 'arraybuffer';
                xhr.onload = function() {
                    if (this.status === 200) {
                        var blob = new Blob([this.response], { type: 'application/pdf' });
                        window.navigator.msSaveBlob(blob, pdfName);
                    }
                };
                xhr.send();
            } else {
                fetch(pdfUrl)
                    .then(response => response.blob())
                    .then(blob => {
                        var url = window.URL.createObjectURL(blob);
                        var a = document.createElement('a');
                        a.href = url;
                        a.download = pdfName;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                    });
            }
        },
        toRecall(row) {
            this.$confirm(`确定要驳回吗？`, '提示', {
                type: 'warning'
            })
                .then(() => {
                    this.dialogVisible = true;
                    this.curRow = row;
                })
                .catch(() => {});
        },
        toPass(row) {
            this.$confirm(`确定要通过吗？`, '提示', {
                type: 'warning'
            })
                .then(() => {
                    this.curRow = row;
                    if (row.inOutType == 9) {
                        auditWalletRefund({
                            walletRecordId: row.id,
                            passed: true,
                            auditRemark: '审核通过'
                        }).then((res) => {
                            if (res.data.code == 0) {
                                this.getData();
                            } else {
                                return Message.error(res.data.msg);
                            }
                        });
                        return;
                    } else {
                        walletRecordAudit({
                            remark: '',
                            id: row.id,
                            auditStatus: 2
                        }).then((res) => {
                            if (res.data.code == 0) {
                                this.getData();
                            } else {
                                return Message.error(res.data.msg);
                            }
                        });
                    }
                })
                .catch(() => {});
        },
        sureRecall() {
            if (!this.remark) return Message.error('请输入驳回理由！');
            if (this.curRow.inOutType == 9) {
                auditWalletRefund({
                    walletRecordId: this.curRow.id,
                    passed: false,
                    auditRemark: this.remark
                }).then((res) => {
                    if (res.data.code == 0) {
                        this.dialogVisible = false;
                        this.getData();
                    } else {
                        return Message.error(res.data.msg);
                    }
                });
                return;
            } else {
                walletRecordAudit({
                    remark: this.remark,
                    id: this.curRow.id,
                    auditStatus: 3
                }).then((res) => {
                    if (res.data.code == 0) {
                        this.dialogVisible = false;
                        this.getData();
                    } else {
                        return Message.error(res.data.msg);
                    }
                });
            }
        },
        closeDia() {
            this.remark = '';
            this.dialogVisible = false;
        },
        async getExcel() {
            let res = await exportExcel(this.form);
            const blob = new Blob([res.data]);
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = '充值记录.xlsx';
            link.click();
            URL.revokeObjectURL(link.href);
        }
    },
    created() {
        this.getData();
        bussnessList().then((res) => {
            if (res.data.code === 0) {
                this.listRcMerchant = res.data.data;
            }
        });
        companyList().then((res) => {
            if (res.data.code === 0) {
                this.listEnterprise = res.data.data;
            }
        });
        listWalletByUser('').then((res) => {
            this.walletBox = res.data.data.map((s) => {
                s.walletStr = s.merchantsName + '-' + s.companyName + '-' + s.bankName;
                return s;
            });
        });
    }
};
</script>

<style>
    .tab-title-lg .el-tabs__item {
        font-size: 16px;
        line-height: 1.5;
        font-weight: 400;
    }

    .tab-title-lg .el-tabs__header {
        margin-bottom: 20px;
    }
    .money_box {
        display: flex;
        justify-content: space-around;
        font-size: 14px;
        color: #989898;
        margin-bottom: 20px;
    }
    .money_box span {
        font-weight: 550;
        font-size: 18px;
    }
</style>
