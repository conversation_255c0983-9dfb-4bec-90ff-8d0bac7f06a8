<template>
    <div>
        <el-card shadow="never">
            <div slot="header" class="clearfix">
                <span>流水账单</span>
            </div>
            <el-form :inline="true" ref="form" :model="form" class="demo-form-inline">
                <el-row type="flex" align="bottom">
                    <el-col :span="20">
                        <el-row>
                            <el-form-item label="商户选择：">
                                <el-select v-model="form.merchantId" placeholder="全部商户" clearable filterable @change="toChangeMerchant">
                                    <el-option :label="i.merchantName" :value="i.id" v-for="i in merchantList" :key="i.id"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="机构选择：">
                                <el-select v-model="form.enterpriseId" filterable placeholder="请选择" clearable @change="toChangeEnterprise">
                                    <el-option
                                        :label="i.enterpriseName"
                                        :value="i.enterpriseId"
                                        v-for="i in listEnterprise"
                                        :key="i.enterpriseId"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="钱包类型：">
                                <el-select v-model="form.walletType" placeholder="请选择" clearable filterable @change="toChangeValueType">
                                    <el-option :label="i.name" :value="i.id" v-for="i in walletBoxStatus" :key="i.id"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="钱包选择：">
                                <el-select v-model="form.walletId" filterable placeholder="请选择" clearable>
                                    <el-option :label="i.walletStr" :value="i.walletId" v-for="i in walletBox" :key="i.walletId"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="所属业务：">
                                <el-select v-model="form.inOutType" placeholder="请选择" clearable filterable>
                                    <el-option :label="i.name" :value="i.id" v-for="i in inOutList" :key="i.id"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="交易状态：">
                                <el-select v-model="form.auditStatus" placeholder="请选择" clearable>
                                    <el-option :label="i.name" :value="i.id" v-for="i in statusBox" :key="i.id"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="发放渠道：" v-show="form.inOutType == 3 || form.inOutType == 4">
                                <el-select v-model="form.dataType" placeholder="请选择" clearable>
                                    <el-option :label="i.name" :value="i.value" v-for="i in dataType" :key="i.value"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="模糊搜索：">
                                <el-input v-model="form.text" placeholder="请输入关键字" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="金额范围：">
                                <el-input v-model="form.minMoney" type="number" placeholder="最小金额" style="width: 150px" clearable></el-input>
                                -
                                <el-input v-model="form.maxMoney" type="number" placeholder="最大金额" style="width: 150px" clearable></el-input>
                            </el-form-item>
                        </el-row>
                        <el-row>
                            <el-form-item style="text-align: right;">
                                <el-select v-model="form.type" placeholder="请选择" clearable>
                                    <el-option label="创建时间" value="1"></el-option>
                                    <el-option label="交割时间" value="2"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item>
                                <el-date-picker
                                    v-model="form.date"
                                    type="daterange"
                                    range-separator="~"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    value-format="yyyy-MM-dd"
                                    clearable
                                    @change="toChangeTime"
                                >
                                </el-date-picker>
                            </el-form-item>
                        </el-row>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item>
                            <el-button size="medium" @click="excelList">导 出 </el-button>
                            <el-button type="primary" icon="el-icon-search" size="medium" @click="getDataSearch">查 询 </el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <!-- <div class="money_box">
                <div>
                    已完成&nbsp;&nbsp;<span style="color: #318000">{{ readyMoney }}</span>
                </div>
                <div>
                    有退票&nbsp;&nbsp;<span style="color: #ffa500">{{ backMoney }}</span>
                </div>
                <div>
                    已取消&nbsp;&nbsp;<span style="color: #ff0609">{{ removeMoney }}</span>
                </div>
                <div>
                    未审核&nbsp;&nbsp;<span style="color: #2c3034">{{ auditMoney }}</span>
                </div>
            </div> -->
            <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                <el-table-column prop="merchantName" label="商户名称" width="120" fixed show-overflow-tooltip></el-table-column>
                <el-table-column prop="walletStr" label="商户钱包" width="200" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <div>{{ scope.row.companyName }}</div>
                        <div style="display:flex; align-items: center; ">
                            <span>{{ scope.row.bankName }}</span>
                            <img width="16px" src="@/assets/img/zsyh.png" v-show="scope.row.bankName.indexOf('招商') > -1 || scope.row.bankName.indexOf('招行') > -1" style="margin-left: 3px"/>
                            <img width="16px" src="@/assets/img/alipay.png" v-show="scope.row.bankName.indexOf('支付宝') > -1" style="margin-left: 3px"/>
                            <img width="16px" src="@/assets/img/pabank.png" v-show="scope.row.bankName.indexOf('平安') > -1" style="margin-left: 3px"/>
                            <img width="16px" src="@/assets/img/wpay.png" v-show="scope.row.bankName.indexOf('微信') > -1" style="margin-left: 3px" />
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="company" label="交易目标" width="150" show-overflow-tooltip></el-table-column>
                <el-table-column label="所属业务" width="100" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span>{{ getInoutStr(scope.row.inOutType) }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="状态" prop="phone" width="100" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <div v-if="scope.row.auditStatus == 1">待审核</div>
                        <div v-if="scope.row.auditStatus == 2">已完成</div>
                        <div v-if="scope.row.auditStatus == 3">已取消</div>
                        <div v-if="scope.row.auditStatus == 4">操作中</div>
                        <div v-if="scope.row.auditStatus == 5">
                            已完成
                            <div style="color: #ff0000">有退票</div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="收款人" min-width="200" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <div v-if="scope.row.name">{{ scope.row.name }}</div>
                        <div v-if="scope.row.idCardNumber">
                            <i class="el-icon-postcard"></i> {{ scope.row.idCardNumber }}
                        </div>
                        <div v-if="scope.row.bankNumber">
                            <i class="el-icon-bank-card"></i> {{ scope.row.bankNumber }}
                        </div>
                        <div v-if="scope.row.phone"><i class="el-icon-mobile-phone"></i> {{ scope.row.phone }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="流水单号" min-width="250" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <div v-if="scope.row.orderNo">
                            <div v-if="scope.row.mearchantType == 2 && scope.row.inOutType == 1">
                                <div>充值：{{ scope.row.orderNo.split('/')[0] }}</div>
                                <div>划拨：{{ scope.row.orderNo.split('/')[1] }}</div>
                            </div>
                            <div v-else>
                                {{ scope.row.orderNo.split('/')[0] }}
                            </div>
                        </div>
                        <div v-else>-</div>
                    </template>
                </el-table-column>
                <!-- <el-table-column label="付款信息" prop="drawee"> </el-table-column> -->
                <el-table-column prop="money" label="金额" width="150" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span class="money" :style="{ color: scope.row.money > 0 ? '#ff6600' : '' }">{{ scope.row.moneyStr }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="发放渠道" v-if="form.inOutType == 3 || form.inOutType == 4" width="100" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span>{{ dataType.find((v) => v.value == scope.row.dataType).name }}</span>
                    </template>
                </el-table-column>
                <el-table-column min-width="200" label="备注" prop="remark" show-overflow-tooltip></el-table-column>
                <el-table-column label="创建时间/交割时间" prop="createTime" width="200" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <div>{{ scope.row.createTime }}</div>
                        <div>{{ scope.row.updateTime }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="交易凭证" width="100">
                    <template slot-scope="scope">
                        <el-button v-if="scope.row.picFlag" @click="toLookDetail(scope.row.fileList)"> 查看凭证 </el-button><br/>
                        <el-button v-if="scope.row.picFlag" @click="toDownload(scope.row)"> 下载凭证 </el-button>
                        <span v-if="!scope.row.picFlag">-</span>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page="tableData.current"
                    layout="prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
        </el-card>
    </div>
</template>

<script>
import { turnoverList, companyList, walletListV2, statisticalMoney, excelList, listRcMerchant } from '@/api/account/account.js';
import { getFileNameUUID } from '@/utils/oss.js';
import { OSS_URL } from '@/api/config';
export default {
    data() {
        return {
            statusBox: [
                {
                    name: '待审核',
                    id: 1
                },
                {
                    name: '操作中',
                    id: 4
                },
                {
                    name: '已完成',
                    id: 2
                },
                {
                    name: '已取消',
                    id: 3
                },
                {
                    name: '已完成(有退票)',
                    id: 5
                }
            ],
            inOutList: [
                {
                    name: '帐户充值',
                    id: 1
                },
                {
                    name: '人工调帐',
                    id: 2
                },
                {
                    name: '费用发放',
                    id: 3
                },
                {
                    name: '服务费',
                    id: 4
                },
                {
                    name: '退票回冲',
                    id: 5
                },
                {
                    name: '渠道提成',
                    id: 6
                },
                {
                    name: '票额调整',
                    id: 7
                }
            ],
            form: {
                enterpriseId: '',
                walletId: undefined,
                auditStatus: '',
                text: '',
                minMoney: '',
                maxMoney: '',
                type: '',
                startTime: '',
                endTime: '',
                merchantId: '',
                walletType: '',
                type: '1'
            },
            tableData: {},
            current: 1,
            size: 10,
            listEnterprise: [],
            walletBox: [],
            readyMoney: 0, //已完成
            backMoney: 0, //已退票
            removeMoney: 0, //已取消
            merchantList: [],
            auditMoney: 0, //待审核
            dataType: [
                {
                    value: 0,
                    name: '平台发放'
                },
                {
                    value: 1,
                    name: 'API发放'
                },
                {
                    value: 3,
                    name: '线下发放'
                }
            ],
            walletBoxStatus: [
                {
                    name: '招商银行',
                    id: 1
                },
                {
                    name: '支付宝',
                    id: 2
                },
                {
                    name: '微信',
                    id: 3
                },
                {
                    name: '平安银行',
                    id: 4
                }
            ]
        };
    },
    created() {
        companyList(this.form.merchantId).then((res) => {
            if (res.data.code === 0) {
                this.listEnterprise = res.data.data;
            }
        });
        walletListV2({ type: this.form.walletType, enterpriseId: this.form.enterpriseId }).then((res) => {
            if (res.data.code === 0) {
                this.walletBox = res.data.data.map((s) => {
                    s.walletStr = s.merchantsName + '-' + s.companyName + '-' + s.bankName;
                    return s;
                });
            }
        });
        listRcMerchant().then((res) => {
            this.merchantList = res.data.data;
        });
        this.getData();
    },
    methods: {
        toChangeValueType(val) {
            this.form.walletId = undefined;
            walletListV2({ type: val, enterpriseId: this.form.enterpriseId }).then((res) => {
                if (res.data.code === 0) {
                    this.walletBox = res.data.data.map((s) => {
                        s.walletStr = s.merchantsName + '-' + s.companyName + '-' + s.bankName;
                        return s;
                    });
                }
            });
        },
        toLookDetail(urlList) {
            window.open(OSS_URL + '/' + urlList[0].url);
        },
        toDownload(row){
            // rename and download pdf file
            var pdfUrl = OSS_URL + '/' + row.fileList[0].url;
            // 流水号_姓名_金额_时间.pdf,
            var orderNo = row.orderNo? row.orderNo.split('/')[0] : "";
            var pdfName =
                (orderNo || "") + '_' +
                (row.name || "") + '_' +
                (row.money || "") + '_' +
                (row.createTime.replace(/[:]/g, '')) + '.pdf';
            if (window.navigator.msSaveBlob) {
                var xhr = new XMLHttpRequest();
                xhr.open('GET', pdfUrl, true);
                xhr.responseType = 'arraybuffer';
                xhr.onload = function() {
                    if (this.status === 200) {
                        var blob = new Blob([this.response], { type: 'application/pdf' });
                        window.navigator.msSaveBlob(blob, pdfName);
                    }
                };
                xhr.send();
            } else {
                fetch(pdfUrl)
                    .then(response => response.blob())
                    .then(blob => {
                        var url = window.URL.createObjectURL(blob);
                        var a = document.createElement('a');
                        a.href = url;
                        a.download = pdfName;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                    });
            }
        },
        toChangeMerchant(val) {
            this.form.enterpriseId = '';
            this.form.walletId = undefined;
            if (val) {
                companyList(val).then((res) => {
                    if (res.data.code === 0) {
                        this.listEnterprise = res.data.data;
                    }
                });
                this.walletBox = [];
            } else {
                companyList(this.form.merchantId).then((res) => {
                    if (res.data.code === 0) {
                        this.listEnterprise = res.data.data;
                    }
                });
                walletList(this.form.enterpriseId).then((res) => {
                    if (res.data.code === 0) {
                        this.walletBox = res.data.data.map((s) => {
                            s.walletStr = s.merchantsName + '-' + s.companyName + '-' + s.bankName;
                            return s;
                        });
                    }
                });
            }
        },
        toChangeEnterprise(val) {
            this.form.walletId = undefined;
            if (val) {
                walletList(val).then((res) => {
                    if (res.data.code === 0) {
                        this.walletBox = res.data.data.map((s) => {
                            s.walletStr = s.merchantsName + '-' + s.companyName + '-' + s.bankName;
                            return s;
                        });
                    }
                });
            } else {
                this.walletBox = [];
            }
        },
        getInoutStr(id) {
            let str = '';
            this.inOutList.forEach((s) => {
                if (s.id == id) {
                    str = s.name;
                }
            });
            return str;
        },
        toChangeTime(date) {
            if (date) {
                this.form.startTime = date[0] + ' ' + '00:00:00';
                this.form.endTime = date[1] + ' ' + '23:59:59';
            } else {
                this.form.startTime = '';
                this.form.endTime = '';
            }
        },
        handleSizeChange(size) {
            this.size = size;
            this.current = 1;
            this.getData();
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.current = current;
            this.getData(current);
        },
        getData() {
            // statisticalMoney({
            //     ...this.form
            // }).then((res) => {
            //     if (res.data.code === 0) {
            //         res.data.data.forEach((s) => {
            //             if (s.auditStatus == 2) {
            //                 this.readyMoney = parseFloat(s.sumMoney).toLocaleString('en-US');
            //             } else if (s.auditStatus == 5) {
            //                 this.backMoney = parseFloat(s.sumMoney).toLocaleString('en-US');
            //             } else if (s.auditStatus == 3) {
            //                 this.removeMoney = parseFloat(s.sumMoney).toLocaleString('en-US');
            //             } else if (s.auditStatus == 1) {
            //                 this.auditMoney = parseFloat(s.sumMoney).toLocaleString('en-US');
            //             }
            //         });
            //     }
            // });
            turnoverList({
                ...this.form,
                current: this.current,
                size: this.size
            }).then((res) => {
                if (res.data.code === 0) {
                    res.data.data.records.forEach((v) => {
                        v.walletStr = v.merchantName + '-' + v.companyName + '-' + v.bankName;
                        v.moneyStr = '';
                        if (v.money > 0) {
                            v.moneyStr = '+￥' + v.money.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                        } else {
                            v.moneyStr = '-￥' + Math.abs(v.money).toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                        }
                        v.fileListBig = [];
                        if (v.fileList && v.fileList.length > 0) {
                            v.fileList.forEach((element) => {
                                v.fileListBig.push(OSS_URL + element.url);
                            });
                        }
                    });
                    this.tableData = res.data.data;
                }
            });
        },
        getDataSearch() {
            this.current = 1;
            this.getData();
        },
        async excelList() {
            const s = await this.$exportDia();
            excelList({
                ...this.form,
                isFile: s.isFile
            }).then((res) => {
                // let excel = new Blob([res.data]);
                // let url = URL.createObjectURL(excel);
                // let a = document.createElement('a');
                // let suf = '';
                // if (s.isFile == 0) {
                //     suf = '.xlsx';
                // } else {
                //     suf = '.zip';
                // }
                // a.href = url;
                // a.download = '导出账单' + getFileNameUUID() + suf;
                // a.click();
                this.$confirm('已加入下载队列，是否前往下载中心查看?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'success'
                }).then(() => {
                    this.$router.push('/system/downloadCenter');
                });
            });
        }
    }
};
</script>

<style scoped>
.money_box {
    display: flex;
    justify-content: space-around;
    font-size: 14px;
    color: #989898;
    margin-bottom: 20px;
}
.money_box span {
    font-weight: 550;
    font-size: 18px;
}
</style>
