<template>
    <div>
        <el-dialog title="详情" :visible.sync="dialogVisible" width="60%" top="4vh" @close="closeDia" @open="toOpenThisDia">
            <el-form label-width="140px">
                <el-form-item label="开票企业名称：">
                    {{ infoDetail.enterpriseName }}
                </el-form-item>
                <el-form-item label="纳税识别号：">
                    {{ infoDetail.taxIdentifyNumber }}
                </el-form-item>
                <el-form-item label="企业地址：">
                    {{ infoDetail.address }}
                </el-form-item>
                <el-form-item label="开户银行：">
                    {{ infoDetail.bankName }}
                </el-form-item>
                <el-form-item label="银行账号：">
                    {{ infoDetail.accountNumber }}
                </el-form-item>
                <el-form-item label="开票类目：">
                    <div v-if="filteredInvoiceDetails && filteredInvoiceDetails.length > 0">
                        <el-table :data="filteredInvoiceDetails" border style="width: 100%; margin-top: 10px;">
                            <el-table-column prop="invoiceCategoryName" label="类目名称" min-width="120">
                            </el-table-column>
                            <el-table-column prop="projectName" label="项目名称" min-width="150">
                            </el-table-column>
                            <el-table-column prop="ticketAmount" label="开票金额" min-width="100" align="right">
                                <template slot-scope="scope">
                                    <span :style="{ color: scope.row.ticketAmount >= 0 ? '#67C23A' : '#F56C6C', fontWeight: 'bold' }">
                                        ￥{{ scope.row.ticketAmount }}
                                    </span>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div v-if="hasNegativeAmountWithoutCategory" class="negative-amount-notice">
                            <el-alert
                                title="票额调整提醒"
                                type="warning"
                                :closable="false"
                                show-icon>
                                <template slot="default">
                                    当前开票申请涉及票额调整，调票金额为 <strong style="color: #F56C6C;">￥{{ negativeAmountTotal }}</strong>，请财务人员要根据实际情况在相应的开票类目中扣除对应金额。请以下方显示的"开票总金额"为最终开票依据。
                                </template>
                            </el-alert>
                        </div>
                    </div>
                    <div v-else>
                        {{ infoDetail.invoiceCategoryStr }}
                    </div>
                </el-form-item>
                <el-form-item label="开票总金额：">
                    <span style="font-family: 'Alibaba PuHuiTi', 'Arial', 'Tahoma', 'PingFang SC', 'Microsoft YaHei', sans-serif; font-weight: bold;">
                        ￥{{ infoDetail.money }}
                    </span>
                </el-form-item>
                <el-form-item label="开票类型：">
                    {{ infoDetail.invoiceTypeStr }}
                </el-form-item>
                <el-form-item label="提醒事项：" class="remark">
                    {{ infoDetail.tips }}
                </el-form-item>
                <el-form-item label="发票备注：" class="remark">
                    <span v-if="infoDetail.auditStatus != 1">{{ infoDetail.remark }}</span>
                    <el-input v-if="infoDetail.auditStatus == 1"
                        class="invoiceInput" type="textarea"
                        v-model="invoiceRemark"></el-input>
                </el-form-item>
                <br/>
                <el-form-item label="结算单据：" v-if="!isLoading && settlemenUrl" class="downloadButton">
                    <el-button
                        :style="{
                        'border':'solid 1px #FF0000', 'color':'#000',
                        'background-color':'#FFaaaa', 'font-size':'14px','width':'150px', 'height':'40px' }"
                        @click="downFile(OSS_URL + settlemenUrl, '结算单据')"
                    >下载企业单据
                    </el-button>
                </el-form-item>
                <el-form-item label="结算单据：" v-if="!isLoading && !settlemenUrl" class="downloadButton">
                    <el-button
                        :style="{'border':'solid 1px #FF0000', 'color':'#000','font-size':'14px','width':'150px', 'height':'40px' }"
                    >无企业单据!
                    </el-button>
                </el-form-item>
                <el-form-item label="结算单据：" v-if="isLoading" class="downloadButton">
                    数据加载中...
                </el-form-item>
                <!-- 查看扫描照片 -->
                <el-form-item label="扫描照片：" 
                              v-if="isLoading && infoDetail.auditStatus > 1" class="downloadButton">
                    数据加载中...
                </el-form-item>
                <!-- 修改扫描照片 -->
                <el-form-item label="扫描照片："
                    v-else-if="(infoDetail.auditStatus ==3 && scanningUrl.trim() == '') ||
                               (infoDetail.auditStatus ==4 && modify && scanningUrl.trim() == '') ||
                               (infoDetail.auditStatus ==2 && modify && scanningUrl.trim() == '')" class="downloadButton">
                    <el-upload 
                        action="" list-type="picture-card"
                        :http-request="uploadURLs"
                        :multiple="false"
                        :on-success="uploadSuccess"
                        :on-error="uploadError"
                        :on-remove="fileRemove"
                        :on-cancel="fileRemove"
                        :file-list="fileLists"
                        :limit="1"
                    >
                        <i class="el-icon-upload"></i>
                    </el-upload>
                </el-form-item>
                <el-form-item label="扫描照片："
                    v-else-if="(infoDetail.auditStatus ==3 && scanningUrl.trim() != '') ||
                               (infoDetail.auditStatus ==4 && !modify) ||
                               (infoDetail.auditStatus ==4 && modify && scanningUrl.trim() != '') ||
                               (infoDetail.auditStatus ==2 && !modify) ||
                               (infoDetail.auditStatus ==2 && modify && scanningUrl.trim() != '')" class="downloadButton">
                    <el-button v-if="scanningUrl.trim() != ''"
                        :style="{
                        'border':'solid 1px #00FF00', 'color':'#000',
                        'background-color':'#aaFFaa', 'font-size':'14px','width':'150px', 'height':'40px' }"
                        @click="downFile(OSS_URL + scanningUrl, '扫描照片')">下载发票扫描照片</el-button>
                    <el-button v-if="scanningUrl.trim() == ''"
                        :style="{'border':'solid 1px #00FF00', 'color':'#000','font-size':'14px','width':'150px', 'height':'40px' }"
                    >未上传发票,请"修改"!
                    </el-button>
                </el-form-item>
                <el-divider></el-divider>
                <!-- 等待填写快递单号 -->
                <template v-if="infoDetail.auditStatus == 4 || infoDetail.auditStatus == 2">
                  <div>
                    <el-form style="display: flex; flex-warp: nowrap; padding-left: 30px;">
                      <el-form-item label="收件人：" style="display: flex; padding: 0 30px;"> 
                          {{ infoDetail.express ? infoDetail.express.name : '' }} </el-form-item>
                      <el-form-item label="收件人地址：" style="display: flex; padding: 0 30px;">
                          {{ infoDetail.express ? infoDetail.express.address : '' }} </el-form-item>
                    </el-form>
                  </div>
                  <el-form-item label="快递单号：">
                        <el-input v-if="infoDetail.auditStatus == 2 && !modify"
                            v-model="courierNumber" disabled
                            class="inputCourierNumber disabled">
                            {{ infoDetail.express ? infoDetail.express.courierNumber : '' }}
                        </el-input>
                        <el-input v-else v-model="courierNumber" class="inputCourierNumber"></el-input>
                  </el-form-item>
                </template>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button 
                    @click="closeDia"
                    >{{btnCloseText}}</el-button>
                <el-button v-if="modify" v-has="'platform_invoicing_audit'"
                    type="primary" @click="toModifyInfoBtn"
                    >确认修改</el-button>
                <el-button v-if="infoDetail.auditStatus == 1" v-has="'platform_invoicing_audit'"
                    type="primary" @click="toSubmitInfo" 
                    >审核通过</el-button>
                <el-button v-if="infoDetail.auditStatus == 3" v-has="'platform_invoicing_audit'"
                    type="primary" @click="toReadlyMail"
                    >完成开票</el-button>
                <el-button v-if="infoDetail.auditStatus == 4 && !modify" v-has="'platform_invoicing_audit'"
                    type="primary" @click="toReadlyMail" 
                    :disabled="!courierNumber ? true : false"
                    >完成开票</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { ticketRecordDetail, ticketRecord, editCourierNumber, editScanning } from '@/api/account/account.js';
import { client, getFileNameUUID, getTimeNow } from '../../../../utils/oss.js';
import { Message } from 'element-ui';
import { OSS_URL } from '@/api/config.js';
import axios from 'axios';
export default {
    data() {
        return {
            isLoading: false,
            dialogVisible: false,
            btnCloseText: '取 消',
            infoDetail: {
                file: {
                    url: ''
                },
                scanning: {
                    url: ''
                }
            },
            settlemenUrl: '',
            scanningUrl: '',
            srcSettList: [],
            srcScannList: [],
            form: {
                settlement: {
                    name: '',
                    url: ''
                },
                scanning: {
                    name: '',
                    url: ''
                }
            },
            courierNumber: "",
            fileList: [],
            fileLists: [],
            OSS_URL: OSS_URL,
            srcList: [''],
            uploadSuccessed: false, 
            invoiceRemark: '',
        };
    },
    computed: {
        filteredInvoiceDetails() {
            if (!this.infoDetail.invoiceDetails) {
                return [];
            }
            return this.infoDetail.invoiceDetails.filter(item => 
                item.invoiceCategoryName && item.invoiceCategoryName.trim() !== ''
            );
        },
        hasNegativeAmountWithoutCategory() {
            if (!this.infoDetail.invoiceDetails) {
                return false;
            }
            return this.infoDetail.invoiceDetails.some(item => 
                (!item.invoiceCategoryName || item.invoiceCategoryName.trim() === '') && 
                item.ticketAmount < 0
            );
        },
        negativeAmountTotal() {
            if (!this.infoDetail.invoiceDetails) {
                return 0;
            }
            return this.infoDetail.invoiceDetails
                .filter(item => 
                    (!item.invoiceCategoryName || item.invoiceCategoryName.trim() === '') && 
                    item.ticketAmount < 0
                )
                .reduce((total, item) => total + item.ticketAmount, 0);
        }
    },
    methods: {
        closeDia() {
            this.$emit('closeDia');
            this.fileList = [];
            this.fileLists = [];
            this.fileRemove(); // remove the cache
        },
        toOpenThisDia() {
            this.form = {
                settlement: {
                    name: '',
                    url: ''
                },
                scanning: {
                    name: '',
                    url: ''
                }
            };
            this.fileRemove(); // remove the cache
        },
        uploadSuccess(res) {
            this.uploadSuccessed = true;
            this.$message.success('点击右下角蓝色按钮"完成开票"，您上传的“扫描照片”才会保存成功。');

            let parentElements = document.querySelectorAll('.el-upload-list__item');
            parentElements.forEach((parentElement) => {
                let thumbnailImg = parentElement.querySelector('.el-upload-list__item-thumbnail');
                if (res.name.split('.')[1] === "pdf" && thumbnailImg) {
                   thumbnailImg.src = require('@/assets/img/misc/thumbnail_invoice.png');
                }
            });
        },
        uploadError(err) {
            this.uploadSuccessed = false;
            console.log('error: ', err);
            this.$message.error('“扫描照片”上传失败！请重试');
        },
        fileRemove(file, uploadFiles) {
            this.uploadSuccessed = false;
            this.form.scanning.url = '';
            this.scanningUrl = '';
        },
        uploadURLs(option) {
            this.uploadSuccessed = false;
            const _this = this;
            const file = option.file;
            //注意哦，这里指定文件夹'image/'，尝试过写在配置文件，但是各种不行，写在这里就可以
            var suffix = file.name.substring(file.name.lastIndexOf('.'));
            var fileName = '/invoice/' + getTimeNow() + '/' + getFileNameUUID() + suffix;
            //定义唯一的文件名，打印出来的uid其实就是时间戳
            client()
                .multipartUpload(fileName, file, {
                    progress: function (percentage, cpt) {}
                })
                .then((res) => {
                    if(res.res.status === 200) {
                        this.form.scanning = {
                            url: fileName,
                            name: file.name
                        };
                        // _this.saveFile();
                        option.onSuccess(res);
                    }else {
                        option.onError(res);
                    }
                }).catch((err) => {
                    option.onError(err);
                });
        },
        saveFile(){
            ticketRecord({
                scanning: this.form.scanning,
                id: this.detailId,
                auditStatus: this.infoDetail.auditStatus
            }).then((res) => {
                if (res.data.code == 0) {
                    ticketRecordDetail({
                        id: this.detailId
                    }).then((ress) => {
                        if (ress.data.code == 0) {
                            Message.success(ress.data.msg);
                            let file = { url: '' };
                            if (res.data.file) {
                                file = res.data.file;
                            }
                            this.infoDetail = { ...ress.data.data, file };
                            this.$emit('newDataGet');
                        }
                    });
                }
            });
        },
        toSubmitInfo() {
            // from auditStatus 1 (待审核)
            ticketRecord({
                settlement: this.form.settlement,
                id: this.detailId,
                remark: this.invoiceRemark,
                auditStatus: 3
            }).then((res) => {
                if (res.data.code == 0) {
                    ticketRecordDetail({
                        id: this.detailId
                    }).then((ress) => {
                        if (ress.data.code == 0) {
                            Message.success(ress.data.msg);
                            let file = { url: '' };
                            if (res.data.file) {
                                file = res.data.file;
                            }
                            this.infoDetail = { ...ress.data.data, file };
                            this.uploadSuccessed = false;
                            this.$emit('newDataGet');
                        }
                    });
                }
            });
        },
        toSubmitMail() {
            // from auditStatus 3 (开票中)
            var submitFlag = this.uploadSuccessed || this.scanningUrl;
            var confirmText = submitFlag ? '发票已上传,将转交邮寄.' : '发票未上传! 请重新上传发票.';
            if(submitFlag){
                this.$confirm(confirmText, '提示!', {
                    confirmButtonText: '确定',
                    cancelButtonText: '关 闭',
                    type: 'warning'
                }).then((res) => {
                ticketRecord({
                        settlement: this.form.settlement,
                        id: this.detailId,
                        auditRemark: this.infoDetail.remark,
                        scanning: this.form.scanning,
                        auditStatus: 4
                    }).then((res) => {
                        if (res.data.code == 0) {
                            ticketRecordDetail({
                                id: this.detailId
                            }).then((ress) => {
                                if (ress.data.code == 0) {
                                    Message.success(ress.data.msg);
                                    let file = { url: '' };
                                    if (res.data.file) {
                                        file = res.data.file;
                                    }
                                    this.infoDetail = { ...ress.data.data, file };
                                    this.scanningUrl = this.infoDetail.scanning && this.infoDetail.scanning.url ? this.infoDetail.scanning.url : '';
                                    this.$emit('newDataGet');
                                }
                            });
                        }
                    });
                });
            }
            else {
                this.$confirm(confirmText, '重要提醒!', {
                    confirmButtonText: '确定',
                    cancelButtonText: '关 闭',
                    type: 'error'
                }).then((res) => {
                }).catch(()=>{});
            }
        },
        toReadlyMail() {
            // from auditStatus 4 (待邮寄)
            ticketRecord({
                id: this.detailId,
                auditRemark: this.auditRemark,
                auditStatus: 2,
                scanning: this.form.scanning,
                courierNumber: this.courierNumber
            }).then((res) => {
                if (res.data.code == 0) {
                    ticketRecordDetail({
                        id: this.detailId
                    }).then((ress) => {
                        if (ress.data.code == 0) {
                            Message.success(ress.data.msg);
                            this.btnCloseText = "完成&关闭";
                            let file = { url: '' };
                            if (res.data.file) {
                                file = res.data.file;
                            }
                            this.infoDetail = { ...ress.data.data, file };
                            this.scanningUrl = this.infoDetail.scanning && this.infoDetail.scanning.url ? this.infoDetail.scanning.url : '';
                            this.$emit('newDataReady');
                        }
                    });
                }
            });
        },
        toShowImg(img) {
            this.srcList[0] = img;
        },
        downFile(url, fileName) {
            //获取文件名后缀
            var suffix = url.substring(url.lastIndexOf('.'));
            axios
                .get(url, { responseType: 'blob' })
                .then((response) => {
                    const blob = new Blob([response.data]);
                    const link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = fileName + new Date() + suffix;
                    link.click();
                    URL.revokeObjectURL(link.href);
                })
                .catch(console.error);
        },
        toModifyInfoBtn() {
            if (this.infoDetail.auditStatus == 2) {
                this.modifyCourierNumber();
            } else {
                this.modifyScanning();
            }
        },
        // 修改快递单号
        async modifyCourierNumber() {
            let res = await this.$confirm(`确定要修改快递单号吗？`, '提示', {
                type: 'warning'
            });
            if (res.cancel) return false;
            if (!this.courierNumber) return this.$message.error('请填写快递单号！');
            let ress = await editCourierNumber({
                id: this.detailId,
                courierNumber: this.courierNumber
            });
            if (ress.data.code != 0) return false;
            this.$message.success('修改成功！');
            this.$emit('closeDia');
        },
        //修改扫描照片
        async modifyScanning() {
            let res = await this.$confirm(`确定要修改扫描照片吗？`, '提示', {
                type: 'warning'
            });
            if (res.cancel) return false;
            let url = this.form.scanning.url;
            if (!url || url.trim() == '') return this.$message.error('请上传扫描图片！');
            let ress = await editScanning({
                id: this.detailId,
                url
            });
            if (ress.data.code != 0) return false;
            this.$message.success('修改成功！');
            this.$emit('closeDia');
        }
    },
    props: {
        showDia: {
            type: Boolean,
            require: true
        },
        detailId: {
            type: String,
            require: true
        },
        modify: {
            type: Boolean,
            require: true
        }
    },
    watch: {
        showDia(val) {
            this.dialogVisible = val;
            this.fileRemove(); // remove the cache
        },
        detailId(val) {
            console.log("detailId",val);
            this.settlemenUrl = '';
            this.scanningUrl = '';
            this.srcScannList = [];
            this.invoiceRemark = '';
            ticketRecordDetail({
                id: val
            }).then((res) => {
                this.isLoading = true;

                let file = { url: '' };
                if (res.data.data.file) {
                    file = res.data.data.file;
                }
                this.infoDetail = { ...res.data.data, file };
                this.btnCloseText = (this.infoDetail.auditStatus == 2 && !this.modify)? "关 闭" :"取 消";

                this.courierNumber = res.data.data.express ? res.data.data.express.courierNumber : '';
                this.invoiceRemark = res.data.data.remark;
                this.settlemenUrl = this.infoDetail.file && this.infoDetail.file.url ? this.infoDetail.file.url : '';
                this.scanningUrl = this.infoDetail.scanning && this.infoDetail.scanning.url ? this.infoDetail.scanning.url : '';

                this.isLoading = false;
            });
        }
    }
};
</script>

<style scoped>
    .image-size {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
    .remark {
        color: red;
        font-weight: 700;
        margin: 8px 0px;
    }
    .invoiceInput {
        width:50%;
        max-width:450px;
    }
   .inputCourierNumber {
        margin-top: 12px;
        font-size: 14px;
        font-size: max(14px, 1em);
        font-family: inherit;
        width:50%;
        max-width: 450px;
        padding: 0.25em 0.5em;
        border: 1px solid ;
        border-color: #eef;
        border-radius: 4px;
   }
   .inputCourierNumber .disabled{
       color: #fff;
   }
   .el-form-item{
        margin-bottom: 2px;
   }
   .downloadButton {
        margin: 12px 0px;
   }
   .negative-amount-notice {
        margin-top: 15px;
   }
   .negative-amount-notice .el-alert {
        border-radius: 6px;
   }
   .negative-amount-notice .el-alert__content {
        color: #E6A23C;
        font-size: 14px;
        line-height: 1.5;
   }
</style>
