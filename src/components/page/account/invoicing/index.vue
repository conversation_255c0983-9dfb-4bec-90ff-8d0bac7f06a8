<template>
    <div>
        <el-card>
            <el-tabs @tab-click="toClickTab" v-model="editableTabsValue" class="tab-title-lg" style="box-shadow: none;">
                <el-tab-pane :label="item.name" :name="item.id.toString()" v-for="(item, index) in statusBox" :key="index" v-if="item.id < 5">
                    <el-form :inline="true" ref="form" :model="form" class="demo-form-inline">
                        <el-row>
                            <el-form-item label="选择税源地">
                                <el-select v-model="form.taxesIds" placeholder="全部税源地" clearable filterable multiple>
                                    <el-option :label="i.name" :value="i.id" v-for="i in taxesList" :key="i.id"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="选择渠道">
                                <el-select v-model="form.channelIds" clearable placeholder="请选择" filterable multiple>
                                    <el-option v-for="item in channelList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="选择企业">
                                <el-select v-model="form.enterpriseId" clearable placeholder="请选择" filterable>
                                    <el-option v-for="item in enterpriseList" :key="item.enterpriseId" :label="item.enterpriseName" :value="item.enterpriseId"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="钱包类型">
                                <el-select v-model="form.walletType" clearable placeholder="请选择" filterable>
                                    <el-option :key="1" label="招商银行" :value="1"></el-option>
                                    <el-option :key="2" label="支付宝" :value="2"></el-option>
<!--                                    <el-option :key="3" label="微信" :value="3"></el-option>-->
                                    <el-option :key="4" label="平安" :value="4"></el-option>
                                    <el-option :key="5" label="聚合支付" :value="5"></el-option>
                                    <el-option :key="6" label="兴业银行" :value="6"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-row>
                        <el-row>
                            <el-form-item label="开票金额">
                                <el-input v-model="form.lowerMoney" placeholder="最小金额" clearable style="width: 120px"></el-input>
                                <span style="margin: 0 10px;"> ~ </span>
                                <el-input v-model="form.maxMoney" placeholder="最大金额" clearable style="width: 120px"></el-input>
                            </el-form-item>
                            <el-form-item style="text-align: right; margin-left: 20px">
                                <el-select v-model="form.timeType" placeholder="请选择">
                                    <el-option label="创建时间" :value="1"></el-option>
                                    <el-option label="修改时间" :value="2"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item>
                                <el-date-picker
                                    v-model="form.date"
                                    type="daterange"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    value-format="yyyy-MM-dd"
                                    clearable
                                    @change="toChangeTime"
                                >
                                </el-date-picker>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" icon="el-icon-search" size="medium" @click="toSeachInfo">查 询 </el-button>
                                <el-button type="info" icon="el-icon-receiving" size="medium" @click="toExportExcel">导出</el-button>
                                <el-button type="success" icon="el-icon-upload2" size="medium" @click="showBatchUploadDialog=true">批量上传发票</el-button>
                                <el-button type="warning" icon="el-icon-view" size="medium" @click="showUploadHistoryDialog=true">查看上传记录</el-button>
                            </el-form-item>
                        </el-row>
                    </el-form>

                    <!-- <div class="money_box">
                        <div>
                            待审核&nbsp;&nbsp;<span style="color: #2c3034">{{ auditMoney }}</span>
                        </div>
                        <div>
                            已完成&nbsp;&nbsp;<span style="color: #318000">{{ readyMoney }}</span>
                        </div>
                        <div>
                            开票中&nbsp;&nbsp;<span style="color: #ffa500">{{ invoicingMoney }}</span>
                        </div>
                        <div>
                            待邮寄&nbsp;&nbsp;<span style="color: #ff0609">{{ postMoney }}</span>
                        </div>
                    </div> -->

                    <el-table :data="tableData.records" style="width: 100%; color: rgba(0, 0, 0, 0.65)" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                        <el-table-column label="商户钱包" min-width="180" prop="walletStr">
                            <template slot-scope="scope">
                                <div style="display:flex; align-items: center; ">
                                    <span>{{ scope.row.bankName }}</span>
                                    <img width="16px" src="@/assets/img/zsyh.png" v-show="scope.row.bankName.indexOf('招商') > -1 || scope.row.bankName.indexOf('招行') > -1" style="margin-left: 3px"/>
                                    <img width="16px" src="@/assets/img/alipay.png" v-show="scope.row.bankName.indexOf('支付宝') > -1" style="margin-left: 3px"/>
                                    <img width="16px" src="@/assets/img/pabank.png" v-show="scope.row.bankName.indexOf('平安') > -1" style="margin-left: 3px"/>
                                    <img width="16px" src="@/assets/img/wpay.png" v-show="scope.row.bankName.indexOf('微信') > -1" style="margin-left: 3px" />
                                </div>
                                <div>{{ scope.row.merchantName }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column label="总金额" prop="money" width="150">
                            <template slot-scope="scope">
                                <span class="money">{{scope.row.money ? '￥' + scope.row.money.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) : '---' }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="开票类目" prop="invoiceCategoryStr" width="150"></el-table-column>
                        <el-table-column label="开票状态" prop="auditStatusStr" width="150"></el-table-column>
                        <el-table-column label="发起方" width="140">
                            <template slot-scope="scope">
                                <div>{{ scope.row.enterpriseName }}</div>
                                <div>
                                    {{ scope.row.calculationType == 1 ? '内扣' : '外扣' }}:{{ scope.row.minMoney }}-{{ scope.row.maxMoney }}/{{
                                        scope.row.rate
                                    }}%
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="createName" label="申请人" width="150" show-overflow-tooltip></el-table-column>
                        <el-table-column label="操作人" prop="auditUserName" width="150"> </el-table-column>
                        <el-table-column min-width="200" label="提醒事项" prop="tips" class-name="remark" show-overflow-tooltip></el-table-column>
                        <el-table-column min-width="200" label="发票备注" prop="remark" show-overflow-tooltip class-name="remark"></el-table-column>
                        <el-table-column label="创建/修改时间" width="200">
                            <template slot-scope="scope">
                                <div>{{ scope.row.createTime ? scope.row.createTime : "---" }}</div>
                                <div>{{ scope.row.updateTime ? scope.row.updateTime : "---" }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="id" label="编号" width="160"></el-table-column>
                        <el-table-column label="操作" fixed="right" width="100">
                            <template slot-scope="scope">
                                <el-button type="text" @click="lookDetail(scope.row.id)" style="font-size: 15px;!important">查看</el-button>
                                <el-button
                                    type="text"
                                    style="font-size: 15px!important;"
                                    @click="toCancelTicket(scope.row.id)"
                                    v-show="scope.row.isAudit && scope.row.auditStatus == 1"
                                    v-has="'platform_invoicing_audit'"
                                    >撤回</el-button
                                >
                                <el-button type="text" @click="toModifyInfo(scope.row.id)" style="font-size: 15px;!important" v-if="scope.row.auditStatus === 4 || scope.row.auditStatus === 2"
                                    >修改</el-button
                                >
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="pagination">
                        <el-pagination
                            @current-change="handleCurrentChange"
                            :current-page="tableData.current"
                            layout="prev, pager, next, jumper"
                            :total="tableData.total"
                        ></el-pagination>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </el-card>
        <detailDia
            :showDia="showDia"
            @closeDia="closeDia"
            :detailId="detailId"
            @newDataGet="toSeachInfo"
            @newDataReady="newDataReady"
            :modify="modify"
        ></detailDia>
        <el-dialog title="驳回" :visible.sync="dialogVisible" width="40%" @close="closeDia">
            <el-form label-width="80px">
                <el-form-item label="驳回理由">
                    <el-input type="textarea" v-model="remark"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeDia">取 消</el-button>
                <el-button type="primary" @click="sureRecall">确 定</el-button>
            </span>
        </el-dialog>
        <el-dialog title="发票批量上传" :visible.sync="showBatchUploadDialog" width="30%" @close="closeBatchUploadDialog">
            <el-form>
                <el-form-item label="发票文件ZIP压缩包" prop="files">
                    <el-upload
                        ref="invoiceUploader"
                        class="file-upload"
                        action=""
                        :limit="1"
                        :http-request="uploadFiles"
                        :before-upload="beforeUpload"
                        :on-success="uploadSuccess"
                        :on-error="uploadError"
                        :on-remove="uploadRemove"
                        :on-cancel="uploadRemove"
                        :on-exceed="invoiceUploadExceed"
                        :file-list="invoiceFiles"
                        accept=".zip"
                        list-type="">
                        <el-button slot="trigger" size="small" type="primary">选择文件</el-button>
                        <div slot="tip" class="el-upload__tip">只支持zip文件格式，且文件大小不超过10MB，所包含的发票文件，文件名必须使用“开票申请记录编号”命名</div>
                    </el-upload>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeBatchUploadDialog">取 消</el-button>
                <el-button type="primary" @click="toBatchProcess">提交处理</el-button>
            </span>
        </el-dialog>
        <el-dialog title="发票批量上传记录" :visible.sync="showUploadHistoryDialog" width="60%" @close="closeUploadHistoryDialog">
            <el-table :data="uploadHistory.records" style="width: 100%; color: rgba(0, 0, 0, 0.65)" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
<!--                <el-table-column label="文件名" prop="fileUrl" width="350" :show-overflow-tooltip="true">-->
<!--                    <template slot-scope="scope">-->
<!--                        <a :href="scope.row.fileUrl" target="_blank">{{ scope.row.fileUrl.substring(scope.row.fileUrl.lastIndexOf("/") + 1) }}</a>-->
<!--                    </template>-->
<!--                </el-table-column>-->
                <el-table-column label="上传时间" prop="createTime" min-width="200"></el-table-column>
                <el-table-column label="处理状态" prop="processStatus" min-width="150">
                    <template slot-scope="scope">
                        <span>{{ processStatusMap[scope.row.processStatus] }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="处理开始时间" prop="processStartTime" min-width="200"></el-table-column>
                <el-table-column label="处理结束时间" prop="processEndTime" min-width="200"></el-table-column>
                <el-table-column label="操作" width="100" fixed="right">
                    <template slot-scope="scope">
                        <el-button type="text" @click="lookHistoryDetail(scope.row)" style="font-size: 15px;!important">查看详情</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    @current-change="handleHistoryCurrentChange"
                    :current-page="uploadHistory.current"
                    layout="prev, pager, next, jumper"
                    :total="uploadHistory.total"
                ></el-pagination>
            </div>
        </el-dialog>
        <el-dialog title="处理详情" :visible.sync="showHistoryDetailDialog" width="70%">
            <el-form label-width="100px">
                <el-form-item label="处理结果:">
                    <el-row v-html="currentHistoryRowData.processDetail"></el-row>
                </el-form-item>
                <el-form-item label="处理状态:">
                    <el-row>{{processStatusMap[currentHistoryRowData.processStatus]}}</el-row>
                </el-form-item>
                <el-form-item label="上传时间:">
                    <el-row>{{currentHistoryRowData.createTime}}</el-row>
                </el-form-item>
                <el-form-item label="处理开始时间:">
                    <el-row>{{currentHistoryRowData.processStartTime}}</el-row>
                </el-form-item>
                <el-form-item label="处理结束时间:">
                    <el-row>{{currentHistoryRowData.processEndTime}}</el-row>
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>

<script>
import { ticketRecordList, ticketRecord, exportTicketRecordList, listWalletByUser, toTicketBatchProcess, getBatchProcessHistory } from '@/api/account/account.js';
import detailDia from './detailDia.vue';
import { Message } from 'element-ui';
import { listEnterpriseList } from '@/api/grant/grant';
import { getTaxesList } from '@/api/personal/personal';
import { getChannelList } from '@/api/branch/enterprise';
import { fileUpload } from '@/api/system/user';
import { cdnHost } from '@/config/env';
export default {
    components: {
        detailDia
    },
    data() {
        return {
            statusBox: [
                {
                    name: '全部',
                    id: 0,
                },
                {
                    name: '待审核',
                    id: 1
                },
                {
                    name: '开票中',
                    id: 3
                },
                {
                    name: '已完成',
                    id: 2
                },
                {
                    name: '已驳回',
                    id: 5
                }
            ],
            form: {
                auditStatus: 1,
                current: 1,
                size: 10,
                taxesIds: [],
                channelIds: [],
                enterpriseId: '',
                lowerMoney: '',
                maxMoney: '',
                timeType: 1,
                startTime: '',
                endTime: '',
                walletId: undefined
            },
            //  0-待处理 1-处理中 2-处理完成 3-处理失败 4-部分处理失败
            processStatusMap: {
                0: '待处理',
                1: '处理中',
                2: '处理完成',
                3: '处理失败',
                4: '部分成功'
            },
            taxesList: [],
            enterpriseList: [],
            channelList: [],
            tableData: {},
            editableTabsValue: '1',
            merchantList: [],
            showDia: false,
            detailId: '',
            remark: '',
            dialogVisible: false,
            ticketId: '',
            listWalletSelect: [],
            modify: false,
            auditMoney: 0, //待审核
            readyMoney: 0, //已完成
            invoicingMoney: 0, //开票中
            postMoney: 0, //待邮寄,
            showBatchUploadDialog: false,
            showUploadHistoryDialog: false,
            historyCurrent: 1,
            historyPageSize: 10,
            invoiceFiles: [],
            uploadHistory: {},
            currentHistoryRowData: {},
            showHistoryDetailDialog: false
        };
    },
    watch: {
        'showUploadHistoryDialog'(val) {
            if (val) {
                this.getBatchProcessHistory();
            } else {
                this.uploadHistory = {};
                this.historyCurrent = 1;
            }
        },
        'showHistoryDetailDialog'(val) {
            if (!val) {
                this.currentHistoryRowData = {};
            }
        }
    },
    created() {
        this.getData();
        getTaxesList().then((res) => {
            if (res.data.code == 0) {
                this.taxesList = res.data.data;
            }
        });
        getChannelList().then((res) => {
            if (res.data.code === 0) {
                this.channelList = res.data.data;
            }
        });
        listEnterpriseList().then((res) => {
            if (res.data.code === 0) {
                this.enterpriseList = res.data.data;
            }
        });
    },
    methods: {
        toChangeTime(date) {
            if (date) {
                this.form.startTime = date[0] + ' ' + '00:00:00';
                this.form.endTime = date[1] + ' ' + '23:59:59';
            } else {
                this.form.startTime = '';
                this.form.endTime = '';
            }
        },
        toChangeValue(val) {
            this.form.walletId = undefined;
            listWalletByUser(val).then((res) => {
                if (res.data.code == 0) {
                    res.data.data.forEach((s) => {
                        s.walletStr = s.merchantsName + '-' + s.companyName + '-' + s.bankName;
                    });
                    this.listWalletSelect = res.data.data;
                }
            });
        },
        closeDia() {
            this.showDia = false;
            this.modify = false;
            this.detailId = '';
            this.dialogVisible = false;
        },
        getData() {
            // statisticalTicketMoney({
            //     ...this.form
            // }).then((res) => {
            //     if (res.data.code === 0) {
            //         res.data.data.forEach((s) => {
            //             if (s.auditStatus == 1) {   //已完成
            //                 this.auditMoney = parseFloat(s.sumMoney).toLocaleString('en-US');
            //             } else if (s.auditStatus == 2) {
            //                 this.readyMoney = parseFloat(s.sumMoney).toLocaleString('en-US');
            //             } else if (s.auditStatus == 3) {
            //                 this.invoicingMoney = parseFloat(s.sumMoney).toLocaleString('en-US');
            //             } else if (s.auditStatus == 4) {
            //                 this.postMoney = parseFloat(s.sumMoney).toLocaleString('en-US');
            //             }
            //         });
            //     }
            // });
            ticketRecordList({
                ...this.form
            }).then((res) => {
                res.data.data.records.forEach((element) => {
                    element.walletStr = element.merchantName + '-' + element.companyName + '-' + element.bankName;
                    this.statusBox.map((item) => {
                        if (item.id === element.auditStatus) {
                            element.auditStatusStr = item.name;
                        }
                    });
                });
                this.tableData = res.data.data;
            });
        },
        toSeachInfo() {
            this.form.current = 1;
            this.getData();
        },
        toClickTab(tab) {
            this.editableTabsValue = tab.name;
            this.form.auditStatus = parseInt(this.editableTabsValue) ? parseInt(this.editableTabsValue) : null;
            this.form.current = 1;
            this.getData();
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.form.current = current;
            this.getData();
        },
        handleHistoryCurrentChange(current) {
            this.historyCurrent = current;
            this.getBatchProcessHistory();
        },
        lookDetail(id) {
            this.detailId = id;
            this.showDia = true;
        },
        newDataReady() {
            this.showDia = true;
            this.getData();
        },
        toCancelTicket(id) {
            this.ticketId = id;
            this.$confirm('是否驳回审核', '确认')
                .then(() => {
                    this.dialogVisible = true;
                })
                .catch(() => {});
        },
        sureRecall() {
            if (!this.remark) return Message.error('请输入原因');
            ticketRecord({
                ...this.form,
                id: this.ticketId,
                auditStatus: 5,
                auditRemark: this.remark
            }).then((res) => {
                if (res.data.code == 0) {
                    this.remark = '';
                    this.dialogVisible = false;
                    this.getData();
                } else {
                    Message.error(res.data.msg);
                }
            });
        },
        async toExportExcel() {
            this.$confirm('确认按照当前查询条件导出所有记录吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                let res = await exportTicketRecordList(this.form);
                if (res.data.code !== 0) {
                    console.log(res.data.msg);
                    this.$message.error(res.data.msg);
                    return false;
                }
                this.$confirm('已加入下载队列，是否前往下载中心查看?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'success'
                }).then(() => {
                    this.$router.push('/system/downloadCenter');
                });
            }).catch(() => {
                // 取消导出
            });
        },
        toModifyInfo(id) {
            this.detailId = id;
            this.modify = true;
            this.showDia = true;
        },
        closeUploadHistoryDialog() {
            this.showUploadHistoryDialog = false;
        },
        closeBatchUploadDialog() {
            this.showBatchUploadDialog = false;
            this.invoiceFiles = [];
        },
        uploadFiles(option) {
            const _this = this;
            const file = option.file;
            let formData = new FormData();
            formData.append('file', file);
            formData.append('path', 'invoice');

            fileUpload(formData).then((res) => {
                if (res !== undefined && res.data.code === 0) {
                    let new_file = {
                        name: file.name,
                        response: res,
                        percentage: 0,
                        raw: file,
                        size: file.size,
                        status: 'success',
                        uid: file.uid,
                        url: res.data.data
                    }
                    option.onSuccess(res, new_file);
                } else {
                    option.onError(res, file);
                }
            });
        },
        beforeUpload(file) {
            if(file.size > 10 * 1024 * 1024 ) {
                Message.error('文件大小不能超过10MB');
                return false;
            }
        },
        uploadSuccess(res, file, fileList) {
            if (res.status === 200 && res.data.code === 0) {
                Message.success('上传成功');
                file.uri = res.data.data;
                file.url = cdnHost + res.data.data;
                this.invoiceFiles = [file];
            } else {
                Message.error('上传失败');
            }
        },
        uploadError() {

        },
        uploadRemove(file, fileList) {
            this.invoiceFiles = fileList;
        },
        invoiceUploadExceed(files) {
            this.$refs.invoiceUploader.clearFiles();
            this.$refs.invoiceUploader.handleStart(files[0]);
            this.uploadFiles({
                file: files[0],
                onSuccess: this.uploadSuccess,
                onError: this.uploadError
            });
        },
        toBatchProcess() {
            if (this.invoiceFiles.length === 0) {
                Message.error('请上传发票文件');
                return;
            }
            this.$confirm('确认提交处理吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                let res = await toTicketBatchProcess(this.invoiceFiles[0].url);
                if (res.data.code !== 0) {
                    console.log(res.data.msg);
                    this.$message.error(res.data.msg);
                    return false;
                }

                this.$message.success('提交成功，开始处理，请在上传记录中查看处理进度');
                this.showBatchUploadDialog = false;
                this.invoiceFiles = [];
            }).catch(() => {
                // 取消提交
            });
        },
        async getBatchProcessHistory() {
            let res = await getBatchProcessHistory({
                page: this.historyCurrent,
                size: this.historyPageSize
            });
            if (res.data.code === 0) {
                this.uploadHistory = res.data.data;
            }
        },
        lookHistoryDetail(rowData) {
            if(rowData.processDetail) {
                rowData.processDetail = rowData.processDetail.replace(/\n/g, '<br>');
            }
            this.currentHistoryRowData = rowData;
            this.showHistoryDetailDialog = true;
        }
    }
};
</script>

<style>
    .tab-title-lg .el-tabs__item {
        font-size: 16px;
        line-height: 1.5;
        font-weight: 400;
    }

    .tab-title-lg .el-tabs__header {
        margin-bottom: 20px;
    }

    .remark {
        color: red;
        font-weight: 700;
    }
    .money_box {
        display: flex;
        justify-content: space-around;
        font-size: 14px;
        color: #989898;
        margin-bottom: 20px;
    }
    .money_box span {
        font-weight: 550;
        font-size: 18px;
    }
</style>
