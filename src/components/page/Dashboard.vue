<template>
    <div>
        <el-row :gutter="20">
            <el-col :span="24" :gutter="20">
                <el-card shadow="never" class="mgb20">
                    <div class="box-info">
                        <div>
                            实时概况 更新时间：{{ timeNow }}
                            <el-button type="text" style="margin-left: 5px" @click="toRefreshInfo()">刷新</el-button>
                        </div>
                        <div>
                            <el-select
                                placeholder="请选择税源地"
                                class="select-box"
                                v-model="form.taxesId"
                                filterable
                                clearable
                                @change="toRefreshInfo"
                            >
                                <el-option v-for="item in taxesList" :key="item.taxesId" :label="item.taxesName" :value="item.taxesId"> </el-option>
                            </el-select>
                            <!-- <el-select placeholder="请选择企业" class="select-box" v-model="form.enterpriseId" filterable clearable @change="toRefreshInfo">
                                <el-option
                                    v-for="item in enterpriseList"
                                    :key="item.enterpriseId"
                                    :label="item.name"
                                    :value="item.enterpriseId"
                                >
                                </el-option>
                            </el-select> -->
                        </div>
                    </div>
                </el-card>

                <el-row shadow="hover" :gutter="20">
                    <el-col :span="16">
                        <el-card shadow="never" class="mgb20">
                            <div slot="header" class="clearfix">
                                <span>已上传发放统计</span>
                            </div>
                            <el-row>
                                <el-col :span="6">
                                    <div>
                                        <el-card
                                            shadow="never"
                                            :style="{ border: 0 }"
                                        >
                                            <div class="name_title">今日累计</div>
                                            <div class="name_money money_normal">￥{{uploadedIssueStatisticsData.todayTotalMoney}}</div>
                                        </el-card>
                                    </div>
                                </el-col>
                                <el-col :span="6">
                                    <div>
                                        <el-card
                                            shadow="never"
                                            :style="{ border: 0 }"
                                        >
                                            <div class="name_title">本月累计</div>
                                            <div class="name_money money_normal">￥{{uploadedIssueStatisticsData.currentMonthTotalMoney}}</div>
                                        </el-card>
                                    </div>
                                </el-col>
                            </el-row>
                        </el-card>

                        <el-card shadow="never" class="mgb20">
                            <div slot="header" class="clearfix">
                                <span>今日交易情况</span>
                            </div>
                            <el-row>
                                <el-col :span="6" v-for="(i, index) in toDayList" :key="index">
                                    <div @click="toChangeToday(index)">
                                        <el-card
                                            shadow="never"
                                            :class="[todayChose == index ? 'color-blue' : 'color-blue']"
                                            :body-style="{ border: 0 }"
                                        >
                                            <div class="name_title">{{ i.name }}</div>
                                            <div class="name_money money_normal" v-show="index <= 1">{{ i.money | currency('￥') }}</div>
                                            <div class="name_money" v-show="index > 1">{{ i.money }}</div>
                                        </el-card>
                                    </div>
                                </el-col>
                            </el-row>
                            <div ref="chart" style="width: 90%; height: 443px"></div>
                        </el-card>
                    </el-col>
                    <el-col :span="8">
                        <el-row style="margin-bottom: 10px;">
                            <el-card shadow="never">
                                <div slot="header" class="clearfix" style="height: 20px;">
                                    <div class="status-business">
                                        <span> 交易成功（{{ tradeInfoData.success.length }}条）</span>
                                        <!-- <el-button type="text">查看全部</el-button> -->
                                    </div>
                                </div>
                                <div class="scroll-wrap">
                                    <div class="scroll-content" :style="{ top }">
                                        <div class="scroll-row" v-for="(i, index) in tradeInfoData.success" :key="index">
                                            <div>
                                                <div class="scroll-row-bank">{{ i.merchantName }} - {{ i.bankName }}</div>
                                                <div class="scroll-row-company">{{ i.enterpriseName }}</div>
                                                <div class="scroll-row-company">收款人：{{ i.workerName }}</div>
                                            </div>
                                            <div>
                                                <div class="scroll-row-money money">{{ i.amount | currency('￥') }}</div>
                                                <div class="scroll-row-time">{{ i.time }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </el-card>
                        </el-row>
                        <el-row>
                            <el-card shadow="never">
                                <div slot="header" class="clearfix" style="height: 20px;">
                                    <div class="status-business">
                                        <span> 交易失败（{{ tradeInfoData.fail.length }}条）</span>
                                        <!-- <el-button type="text">查看全部</el-button> -->
                                    </div>
                                </div>
                                <div class="scroll-wrap">
                                    <div class="scroll-content" :style="{ top: tops }">
                                        <div class="scroll-row" v-for="(i, index) in tradeInfoData.fail" :key="index">
                                            <div>
                                                <div class="scroll-row-bank">{{ i.merchantName }} - {{ i.bankName }}</div>
                                                <div class="scroll-row-company">{{ i.enterpriseName }}</div>
                                                <div class="scroll-row-company">收款人：{{ i.workerName }}</div>
                                            </div>
                                            <div>
                                                <div class="scroll-row-money money">{{ i.amount | currency('￥') }}</div>
                                                <div class="scroll-row-time">{{ i.time }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </el-card>
                        </el-row>
                    </el-col>
                </el-row>
                
                <el-row :gutter="12" class="channel">
                    <el-col :span="16">
                        <el-card shadow="never">
                            <div slot="header" class="clearfix">
                                <div class="status-business">
                                    <span>分支信息</span>
                                </div>
                            </div>
                            <el-row>
                                <el-col :span="4">
                                    <div class="number-box">
                                        <div>税源地</div>
                                        <div class="num">{{ rowChartsInfo.taxesNumber }}</div>
                                    </div>
                                    <div class="number-box">
                                        <div>渠道总数</div>
                                        <div class="num">{{ rowChartsInfo.channelNumber }}</div>
                                    </div>
                                    <div class="number-box">
                                        <div>企业总数</div>
                                        <div class="num">{{ rowChartsInfo.enterpriseNumber }}</div>
                                    </div>
                                </el-col>
                                <el-col :span="20">
                                    <div ref="charts" style="width: 100%"></div>
                                </el-col>
                            </el-row>
                        </el-card>
                    </el-col>
                    <el-col :span="8">
                        <el-card shadow="never">
                            <div slot="header" class="clearfix">
                                <div class="status-business">
                                    <span>人员信息</span>
                                </div>
                            </div>
                            <el-row>
                                <el-col :span="12">
                                    <div class="number-box">
                                        <div>总人数</div>
                                        <div class="num">{{ workerNumber.workerTotalNumber }}</div>
                                    </div>

                                    <div class="number-box">
                                        <div>微信绑定</div>
                                        <div class="num">{{ workerNumber.wechatBindNumber }}</div>
                                    </div>
                                </el-col>
                                <el-col :span="12">
                                    <div style="height: 350px">
                                        <div style="height: 80px"></div>
                                        <dv-water-level-pond :config="config" style="width: 150px; height: 150px" />
                                    </div>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <div class="number-box">
                                        <div>总人数</div>
                                        <div class="num">{{ workerNumber.workerTotalNumber }}</div>
                                    </div>

                                    <div class="number-box">
                                        <div>微信绑定</div>
                                        <div class="num">{{ workerNumber.wechatBindNumber }}</div>
                                    </div>
                                </el-col>
                                <el-col :span="12">
                                    <div style="height: 350px">
                                        <div style="height: 80px"></div>
                                        <dv-water-level-pond :config="config" style="width: 150px; height: 150px" />
                                    </div>
                                </el-col>
                            </el-row>
                        </el-card>
                    </el-col>
                </el-row>
            </el-col>
        </el-row>
    </div>
</template>

<script>
import Schart from 'vue-schart';
import theme from '../../utils/echartsMacarons.json'
import {
    currentRecharge,
    currentBill,
    // currentSign,
    // currentSignError,
    currentChannel,
    currentEnterprise,
    tradeInfo,
    branchInfo,
    workerInfoNumber,
    listTaxes,
    listEnterprises,
    getUploadedIssueStatistics
} from '@/api/charts/charts.js';
import { currency, timeCurrent } from '@/utils/currency.js';
export default {
    name: 'dashboard',
    data() {
        return {
            name: localStorage.getItem('ms_username'),
            value2: '',
            value1: '',
            todayChose: 0,
            activeIndex: 0,
            activeIndexs: 0,
            form: {
                taxesId: '',
                enterpriseId: ''
            },
            timeNow: '',

            toDayList: [
                {
                    name: '今日充值（元）',
                    money: ''
                },
                {
                    name: '今日发放（元）',
                    money: ''
                },
                {
                    name: '今日新增渠道',
                    money: ''
                },
                {
                    name: '今日新增企业',
                    money: ''
                }
            ],
            config: {
                data: [],
                shape: 'round',
                colors: ['blue', 'blue'],
                waveOpacity: 0.75,
                waveHeight: 20
            },
            tradeInfoData: {
                success: [],
                fail: []
            },
            rowChartsInfo: {},
            workerNumber: {},
            taxesList: [],
            enterpriseList: [],
            uploadedIssueStatisticsData: {}
        };
    },
    components: {
        Schart
    },
    filters: {
        currency: currency
    },
    computed: {
        role() {
            return this.name === 'admin' ? '超级管理员' : '普通用户';
        },
        top() {
            return -this.activeIndex + 'px';
        },
        tops() {
            return -this.activeIndexs + 'px';
        }
    },
    mounted() {
        this.$echarts.registerTheme('macarons', theme)
        //获取实时刷新时间
        this.currentNowTime();
        this.uploadedIssueStatistics();
    },
    created() {
        // window.location.reload();
        listTaxes().then((res) => {
            if (res.data.code == 0) {
                this.taxesList = res.data.data;
            }
        });
        listEnterprises().then((res) => {
            if (res.data.code == 0) {
                this.enterpriseList = res.data.data;
            }
        });
    },
    methods: {
        toRefreshInfo() {
            this.activeIndex = 0;
            this.activeIndexs = 0;
            clearInterval(this.intnum);
            clearInterval(this.intnums);
            this.currentNowTime();
            this.uploadedIssueStatistics();
        },
        currentNowTime() {
            this.timeNow = timeCurrent();
            this.getBranchInfo();
            //今日充值
            this.toDayRecharge();
            //今日发放
            this.toDayCurrentBill();
            // //签约人数
            // this.toDayCurrentSign();
            // //签约异常
            // this.toDayCurrentSignError();
            //今日渠道
            this.toDayCurrentChannel();
            //今日企业
            this.toDayCurrentEntrprise();
            //交易信息
            this.toDayCurrentTradeInfo();
            this.ScrollUp();
            this.ScrollFialUp();
            this.getWorkerNum();
        },
        //环形图
        getBranchInfo() {
            branchInfo(this.form).then((res) => {
                if (res.data.code == 0) {
                    this.rowChartsInfo = res.data.data;
                    res.data.data.walletInfos.forEach((s) => {
                        s.name = `${s.merchantName} 「${s.bankName}」`;
                        s.value = s.walletNumber;
                    });
                    this.getEchartNew(res.data.data.walletInfos);
                }
            });
        },
        toChangeToday(idx) {
            this.todayChose = idx;
            //今日充值
            this.toDayRecharge();
            //今日发放
            this.toDayCurrentBill();
            // //签约人数
            // this.toDayCurrentSign();
            // //签约异常
            // this.toDayCurrentSignError();
            //今日渠道
            this.toDayCurrentChannel();
            //今日企业
            this.toDayCurrentEntrprise();
        },
        //成功滚动播报方法
        ScrollUp() {
            this.intnum = setInterval((_) => {
                if (this.activeIndex < (this.tradeInfoData.success.length - 1) * 90) {
                    this.activeIndex += 1;
                } else {
                    this.activeIndex = 0;
                    // clearInterval(this.intnum);

                    // this.scrollBox = this.scrollBox.concat(data);
                    // console.log(this.scrollBox);
                    // this.ScrollUp();
                }
            }, 40);
        },
        ScrollFialUp() {
            this.intnums = setInterval((_) => {
                if (this.activeIndexs < (this.tradeInfoData.fail.length - 1) * 90) {
                    this.activeIndexs += 1;
                } else {
                    this.activeIndexs = 0;
                    // clearInterval(this.intnum);

                    // this.scrollBox = this.scrollBox.concat(data);
                    // console.log(this.scrollBox);
                    // this.ScrollUp();
                }
            }, 40);
        },
        Stop() {
            // clearInterval(this.intnum);
        },
        Up() {
            // this.ScrollUp();
        },
        getEchartData(hourWithData) {
            let dataTableW = hourWithData.map((s) => {
                s.data = (parseFloat(s.data) / 10000).toFixed(2);
                return s;
            });
            const chart = this.$refs.chart;
            if (chart) {
                const myChart = this.$echarts.init(chart, 'macarons');
                const option = {
                    legend: {
                        orient: 'horizontal',
                        x: 'right',
                        y: ' center',
                        width: '100',
                        padding: [10, 30, 0, 0],
                        itemWidth: 30,
                        textStyle: { color: '#000' }
                    },
                    tooltip: {
                        formatter: function (params) {
                            return params.value.data + '万元';
                        }
                    },
                    dataset: {
                        source: dataTableW
                    },
                    xAxis: { type: 'category' },
                    yAxis: {
                        axisLabel: {
                            formatter: `{value}万元`
                        }
                    },
                    grid: {
                        x: '4%',
                        y: '4%',
                        x2: '4%',
                        y2: '4%',
                        left: '10%',
                        bottom:'10%',
                        borderWidth: 1
                    },
                    series: [
                        {
                            type: 'bar',
                            encode: {
                                x: 'hour',
                                // 将 "product" 列映射到 Y 轴。
                                y: 'data'
                            }
                        }
                    ]
                };
                myChart.setOption(option);
                window.addEventListener('resize', function () {
                    myChart.resize();
                });
            }
            this.$on('hook:destroyed', () => {
                window.removeEventListener('resize', function () {
                    myChart.resize();
                });
            });
        },
        getEchartNew(dataRow) {
            let legendData = []
            dataRow.forEach((s) => {
                legendData.push = s.name;
            })
            const chart = this.$refs.charts;
            if (chart) {
                const myChart = this.$echarts.init(chart, 'macarons');
                const option = {
                    tooltip: {
                        trigger: 'item'
                    },
                    title: {
                        text: '钱包总数',
                        left: 'left'
                    },
                    legend: {
                        data: legendData,
                        x: 'right', //可设定图例在左、右、居中
                        y: 'bottom', //可设定图例在上、下、居中

                    },

                    series: [
                        {
                            type: 'pie',
                            radius: ['30%', '60%'],
                            avoidLabelOverlap: false,
                            itemStyle: {
                                borderRadius: 8,
                                borderColor: '#fff',
                                borderWidth: 4
                            },
                            label: {
                                show: true
                            },
                            labelLine: {
                                show: true
                            },
                            data: dataRow
                        }
                    
                    ],
                };

                myChart.setOption(option);
                window.addEventListener('resize', function () {
                    myChart.resize();
                });
                let rowHeight = 700;
                myChart.resize({ height: rowHeight });
            }
            this.$on('hook:destroyed', () => {
                window.removeEventListener('resize', function () {
                    myChart.resize();
                });
            });
        },
        //今日充值
        toDayRecharge() {
            currentRecharge(this.form).then((res) => {
                if (res.data.code == 0) {
                    this.toDayList[0].money = res.data.data.total;
                    if (this.todayChose == 0) {
                        this.getEchartData(res.data.data.hourWithData);
                    }
                }
            });
        },
        //今日发放
        toDayCurrentBill() {
            currentBill(this.form).then((res) => {
                if (res.data.code == 0) {
                    this.toDayList[1].money = res.data.data.total;
                    if (this.todayChose == 1) {
                        this.getEchartData(res.data.data.hourWithData);
                    }
                }
            });
        },
        // //签约人数
        // toDayCurrentSign() {
        //     currentSign(this.form).then((res) => {
        //         if (res.data.code == 0) {
        //             this.toDayList[2].money = res.data.data.total;
        //             if (this.todayChose == 2) {
        //                 this.getEchartData(res.data.data.hourWithData);
        //             }
        //         }
        //     });
        // },
        // //签约异常
        // toDayCurrentSignError() {
        //     currentSignError(this.form).then((res) => {
        //         if (res.data.code == 0) {
        //             this.toDayList[3].money = res.data.data.total;
        //             if (this.todayChose == 3) {
        //                 this.getEchartData(res.data.data.hourWithData);
        //             }
        //         }
        //     });
        // },
        //今日渠道
        toDayCurrentChannel() {
            currentChannel(this.form).then((res) => {
                if (res.data.code == 0) {
                    this.toDayList[2].money = res.data.data.total;
                    if (this.todayChose == 2) {
                        this.getEchartData(res.data.data.hourWithData);
                    }
                }
            });
        },
        toDayCurrentEntrprise() {
            currentEnterprise(this.form).then((res) => {
                if (res.data.code == 0) {
                    this.toDayList[3].money = res.data.data.total;
                    if (this.todayChose == 3) {
                        this.getEchartData(res.data.data.hourWithData);
                    }
                }
            });
        },
        //交易信息
        toDayCurrentTradeInfo() {
            tradeInfo(this.form).then((res) => {
                if (res.data.code == 0) {
                    if (res.data.data.fail && res.data.data.success) {
                        this.tradeInfoData = res.data.data;
                    } else if (res.data.data.fail && !res.data.data.success) {
                        this.tradeInfoData.fail = res.data.data.fail;
                        this.tradeInfoData.success = [];
                    } else if (!res.data.data.fail && res.data.data.success) {
                        this.tradeInfoData.fail = [];
                        this.tradeInfoData.success = res.data.data.success;
                    } else {
                        this.tradeInfoData.fail = [];
                        this.tradeInfoData.success = [];
                    }
                }
            });
        },
        getWorkerNum() {
            workerInfoNumber(this.form).then((res) => {
                if (res.data.code == 0) {
                    this.workerNumber = res.data.data;
                    this.config.data = [parseFloat((res.data.data.wechatBindNumber / res.data.data.workerTotalNumber)*100).toFixed(2)];
                    this.config = { ...this.config };
                    console.log(this.config);
                }
            });
        },
        uploadedIssueStatistics() {
            getUploadedIssueStatistics({ ...this.form }).then((res) => {
                if (res.data.code == 0) {
                    this.uploadedIssueStatisticsData = res.data.data;
                }
            });
        },
    }
};
</script>


<style scoped>
.grid-content {
    display: flex;
    align-items: center;
    height: 100px;
}
.select-box {
    margin-left: 10px;
}
.scroll-wrap {
    height: 225px;
    overflow: hidden;
}
.scroll-content {
    position: relative;
    transition: top 0.5s;
}

.number-box {
    font-size: 14px;
}
.number-box .num {
    font-size: 25px;
    margin: 10px 0 20px 0;
}
.channel .number-box {
    font-size: 16px;
}

.grid-cont-right {
    flex: 1;
    text-align: center;
    font-size: 14px;
    color: #999;
}
.status-business {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.grid-num {
    font-size: 30px;
    font-weight: bold;
}
.box-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.grid-con-icon {
    font-size: 50px;
    width: 100px;
    height: 100px;
    text-align: center;
    line-height: 100px;
    color: #fff;
}

.grid-con-1 .grid-con-icon {
    background: rgb(45, 140, 240);
}

.grid-con-1 .grid-num {
    color: rgb(45, 140, 240);
}

.grid-con-2 .grid-con-icon {
    background: rgb(100, 213, 114);
}

.grid-con-2 .grid-num {
    color: rgb(45, 140, 240);
}

.grid-con-3 .grid-con-icon {
    background: rgb(242, 94, 67);
}

.grid-con-3 .grid-num {
    color: rgb(242, 94, 67);
}
.color-blue {
    border-color: transparent;
}
.user-info {
    display: flex;
    align-items: center;
    padding-bottom: 20px;
    border-bottom: 2px solid #ccc;
    margin-bottom: 20px;
}

.user-avator {
    width: 120px;
    height: 120px;
    border-radius: 50%;
}

.user-info-cont {
    padding-left: 50px;
    flex: 1;
    font-size: 14px;
    color: #999;
}

.user-info-cont div:first-child {
    font-size: 30px;
    color: #222;
}

.user-info-list {
    font-size: 14px;
    color: #999;
    line-height: 25px;
}

.user-info-list span {
    margin-left: 50px;
}

.mgb20 {
    margin-bottom: 20px;
}

.todo-item {
    font-size: 14px;
}

.todo-item-del {
    text-decoration: line-through;
    color: #999;
}

.schart {
    width: 100%;
    height: 300px;
}
.name_title {
    font-size: 15px;
    margin-bottom: 10px;
}
.name_money {
    font-size: 20px;
}
.company_name {
    display: inline-block;
    flex: 1;
}
.topinfo {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.bank_name {
    font-size: 14px;
    border: 1px solid #999;
    padding: 5px;
    border-radius: 5px;
    color: #999;
}
.num_money {
    font-size: 40px;
    text-align: center;
    margin-top: 40px;
}
.word_info {
    text-align: center;
    margin-top: 10px;
    margin-bottom: 60px;
}
.scroll-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
    border-bottom: 1px solid #f2f2f2;
}
.scroll-row-bank {
    font-size: 14px;
    border: 1px solid #cccccc;
    border-radius: 4px;
    display: inline-block;
    padding: 2px 4px;
    margin-bottom: 2px;
    background-color: #FAFAFA;
}
.scroll-row-company {
    font-size: 14px;
}
.scroll-row-money {
    font-size: 19px;
    text-align: right;
    margin-bottom: 2px;
    color: red;
}
.scroll-row-time {
    font-size: 12px;
    text-align: right;
}
</style>
