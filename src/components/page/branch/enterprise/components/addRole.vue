<template>
    <div>
        <el-dialog :title="roleId ? '修改角色' : '新增角色'" :visible.sync="dialogVisible" width="60%" @open="openDia" @close="closeDia">
            <el-form label-width="150px" :rules="rules" :model="form">
                <el-form-item label="角色名称：" prop="name">
                    <el-input v-model="form.name" clearable style="width: 250px"></el-input>
                </el-form-item>
                <el-form-item label="备注：">
                    <el-input v-model="form.remark" style="width: 250px"></el-input>
                </el-form-item>
                <el-form-item label="权限：" v-if="needShow"  prop="qx">
                    <el-tree :data="data" show-checkbox node-key="id" :default-checked-keys="checkedArr" ref="tree" :props="defaultProps">
                    </el-tree>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeDia">取 消</el-button>
                <el-button type="primary" @click="roleId ? toModify() : toSubmitInfo()">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { selectTaxesRole, listMenu, infoRole } from '@/api/branch/enterprise.js';
import { Message } from 'element-ui';

export default {
    data() {
        return {
            dialogVisible: false,
            form: {},
            channelPtions: [],
            data: [],
            defaultProps: {
                children: 'children',
                label: 'name'
            },
            checkedArr: [],
            infoObj: {},
            needShow: false,
            rules:{
                name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
                qx:[{ required: true, trigger: 'blur'}]

            }
        };
    },
    props: {
        toShowDia: {
            type: Boolean,
            require: true
        },

        roleId: {
            type: String,
            require: true
        }
    },
    watch: {
        toShowDia(val) {
            this.dialogVisible = val;
            this.needShow = val;
        },
        roleId(val) {}
    },
    mounted() {},
    methods: {
        toAddRateRow() {
            this.rateDetails.push({
                minMoney: '',
                maxMoney: '',
                rate: ''
            });
        },
        //解析出所有的选中节点id
        resolveAllEunuchNodeId(json, idArr, temp) {
            for (let i = 0; i < json.length; i++) {
                const item = json[i];
                // 存在子节点，递归遍历;不存在子节点，将json的id添加到临时数组中
                if (item.children && item.children.length !== 0) {
                    this.resolveAllEunuchNodeId(item.children, idArr, temp);
                } else {
                    temp.push(idArr.filter((id) => id === item.id));
                }
            }
            return temp;
        },
        toDetelRate(index) {
            this.rateDetails.splice(index, 1);
        },
        openDia() {
            if (!this.roleId) {
                this.form = {};
                this.checkedArr = [];
                return false;
            }
            this.checkedArr = [];
            infoRole({
                roleId: this.roleId,
                belongId: this.$route.query.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.checkedArr = this.resolveAllEunuchNodeId(this.data, res.data.data.menuIds, []);
                    this.form = JSON.parse(JSON.stringify(res.data.data));
                    this.$forceUpdate();
                    console.log(this.checkedArr);
                }
            });
        },
        closeDia() {
            this.checkedArr = [];
            this.$emit('closeDia');
        },
        toSubmitInfo() {
            if (!this.form.name) return Message.error('请输入角色名称');
            if (this.$refs.tree.getCheckedKeys().length == 0) {
                Message.error('至少勾选一项');
                return;
            }
            let values = {
                menuIds: this.$refs.tree.getCheckedKeys().concat(this.$refs.tree.getHalfCheckedKeys())
            };
            let data = {
                ...this.form,
                ...values
            };
            this.$confirm('是否确认角色', '新增')
                .then((res) => {
                    this.$emit('addRole', data);
                })
                .catch(() => {});
        },
        toModify() {
            if (!this.form.name) return Message.error('请输入角色名称');
            if (this.$refs.tree.getCheckedKeys().length == 0) {
                Message.error('至少勾选一项');
                return;
            }
            let values = {
                menuIds: this.$refs.tree.getCheckedKeys().concat(this.$refs.tree.getHalfCheckedKeys())
            };
            let data = {
                ...this.form,
                ...values,
                id: this.roleId
            };
            this.$confirm('是否修改角色', '修改')
                .then((res) => {
                    this.$emit('updateRole', data);
                })
                .catch(() => {});
        }
    },
    created() {
        listMenu().then((res) => {
            if (res.data.code == 0) {
                this.data = res.data.data;
            }
        });
    }
};
</script>

<style scoped>
.rate_box {
    align-items: center;
    display: flex;
    margin-bottom: 10px;
}
.rate_box:last-child {
    margin-bottom: 0px;
}
</style>