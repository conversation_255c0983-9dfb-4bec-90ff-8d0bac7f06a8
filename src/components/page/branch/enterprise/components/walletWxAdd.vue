<template>
    <el-dialog
        :visible.sync="dialogVisible"
        @close="toCloseDia"
        width="50%"
        :title="walletId ? '修改钱包' : '新增微信钱包'"
        @open="toOpenDia"
    >
        <el-tabs type="border-card">
            <el-tab-pane label="基本信息">
                <el-form label-width="120px" :rules="rules" ref="form" :model="form">
                    <el-form-item label="选择商户：">
                        <el-select
                            v-model="merchant"
                            placeholder="请选择"
                            :disabled="!!walletId"
                            style="width: 250px"
                            @change="handleMerchantChange"
                            value-key="id"
                            filterable
                        >
                            <el-option :label="item.merchantName" :value="item" v-for="item in merchantList" :key="item.id"></el-option>
                        </el-select>
                    </el-form-item>
                    <!-- <el-form-item label="收款公司：">
                        <el-input v-model="company" disabled style="width: 250px"></el-input>
                    </el-form-item> 
                    <el-form-item label="收款银行：">
                        <el-input v-model="bankName" disabled style="width: 250px"> </el-input>
                    </el-form-item>
                    <el-form-item label="收款账户：">
                        <el-input v-model="accountNumber" disabled style="width: 250px"></el-input>
                    </el-form-item>-->
                    <el-form-item label="绑定公司：" prop="companyName">
                        <el-input v-model="form.companyName" style="width: 250px"></el-input>
                    </el-form-item>
                    <el-form-item label="银行户名：" >
                        <el-input v-model="form.wxAccountName" style="width: 250px"></el-input>
                    </el-form-item>
                    <el-form-item label="银行账户：" >
                        <el-input v-model="form.wxAccountNumber" style="width: 250px"></el-input>
                    </el-form-item>
                </el-form>
            </el-tab-pane>
            <el-tab-pane label="钱包费率">
                <el-form label-width="120px">
                    <el-form-item label="计算方式：">
                        <el-select v-model="form.calculationType" placeholder="请选择计算方式" style="width: 250px">
                            <el-option label="内扣" :value="1"></el-option>
                            <el-option label="外扣" :value="2"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="限额检查：">
                        <el-select v-model="form.quotaType" placeholder="请选择限额检查" style="width: 250px">
                            <el-option label="本商户" :value="1"></el-option>
                            <el-option label="全局" :value="2"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="发放范围/费率：">
                        <el-input v-model="form.walletRates[0].minMoney" style="width: 80px" placeholder="最小金额" disabled></el-input>
                        -
                        <el-input v-model="form.walletRates[0].maxMoney" style="width: 80px" placeholder="最大金额"></el-input>
                        /
                        <el-input v-model="form.walletRates[0].rate" style="width: 80px" placeholder="费率"></el-input>%
                    </el-form-item>
                    <el-alert
                        title="内扣手续费在结算金额内扣除，发放总金额=发放金额+手续费"
                        type="warning"
                        v-if="form.calculationType == 1"
                        :closable="false"
                    >
                    </el-alert>
                    <el-alert
                        title="外扣手续费在结算金额外扣除，发放总金额=发放金额+发放金额X费率"
                        type="warning"
                        v-if="form.calculationType == 2"
                        :closable="false"
                    >
                    </el-alert>
                </el-form>
            </el-tab-pane>
            <el-tab-pane label="开票设置">
                <el-form label-width="120px">
                    <el-form-item label="发票类型：">
                        <el-select
                            v-model="form.invoiceTypes"
                            placeholder="请选择发票类型"
                            multiple
                            style="width: 250px"
                            clearable
                            filterable
                        >
                            <el-option :label="item.name" :value="item.id" v-for="item in type" :key="item.id"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="开票类目：">
                        <el-select
                            v-model="invoiceCategorys"
                            placeholder="请选择开票类目"
                            multiple
                            style="width: 250px"
                            filterable
                            value-key="id"
                            @change="toChangeSelectInfo"
                            clearable
                        >
                            <el-option :label="item.name" :value="item.id" v-for="item in category" :key="item.id"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="开票策略：">
                        <el-radio v-model="form.invoiceStrategy" :label="1">按充值</el-radio>
                        <el-radio v-model="form.invoiceStrategy" :label="2">减余额</el-radio>
                    </el-form-item>
                </el-form>
            </el-tab-pane>
        </el-tabs>
        <span slot="footer" class="dialog-footer">
            <el-button @click="diaClose">取 消</el-button>
            <el-button type="primary" @click="walletId ? surModifyInfo() : addInfo()">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>
import {
    listRcMerchant,
    typeListAll,
    categoryListAll,
    walletDetail,
    walletFinallyDetail,
    listRcMerchantWXPay,
    queryInvoiceCategoryByTaxesId
} from '@/api/branch/enterprise';
export default {
    data() {
        let validatePass1 = (rule, value, callback) => {
            if (!value) {
                callback(new Error('请输入企业全称'));
            } else if (this.getFullNameEnglish(value) == 1) {
                callback(new Error('包含英文括号，注意分别!'));
            } else if (this.getFullNameEnglish(value) == 2) {
                callback(new Error('包含中文括号，注意分别!'));
            } else {
                callback();
            }
        };
        return {
            rules: {
                companyName: [{ required: true, validator: validatePass1, trigger: 'blur' }]
            },
            dialogVisible: false,
            form: {
                walletRates: [
                    {
                        minMoney: 0,
                        maxMoney: '',
                        rate: '',
                        sequence: '1'
                    }
                ]
            },
            merchantList: [],
            company: '',
            bankName: '',
            accountNumber: '',
            type: [],
            category: [],
            merchant: undefined,
            invoiceCategorys: []
        };
    },
    methods: {
        async toOpenDia() {
            let res = await listRcMerchantWXPay({ enterpriseId: this.$route.query.id });
            this.merchantList = res.data.data;

            if (this.walletId) {
                walletDetail({
                    walletId: this.walletId
                }).then((res) => {
                    if (res.data.code == 0) {
                        let data = {
                            ...res.data.data,
                            walletRates: res.data.data.walletRateVos ? res.data.data.walletRateVos.map(rate => ({
                                ...rate
                            })) : [{
                                minMoney: 0,
                                maxMoney: '',
                                rate: '',
                                sequence: '1'
                            }],
                            invoiceTypes: res.data.data.invoiceType.split(',')
                        };
                        this.merchantList.forEach((s) => {
                            if (s.id == res.data.data.merchantId) {
                                this.merchant = s;
                            }
                        });
                        this.company = res.data.data.company;
                        this.bankName = res.data.data.bankName;
                        this.accountNumber = res.data.data.accountNumber;
                        if (res.data.data.invoiceCategory) {
                            this.invoiceCategorys = res.data.data.invoiceCategory.split(',');
                        }
                        this.form = data;
                        queryInvoiceCategoryByTaxesId(res.data.data.taxesId).then((res) => {
                            if (res.data.code == 0) {
                                this.category = res.data.data;
                            }
                        });
                    }
                });
            } else {
                this.category = [];
                this.invoiceCategorys = [];
                walletFinallyDetail(this.$route.query.id).then((res) => {
                    if (res.data.code == 0) {
                        let data = {
                            ...res.data.data,
                            walletRates: res.data.data.walletRateVos ? res.data.data.walletRateVos.map(rate => ({
                                ...rate,
                                rate: ''
                            })) : [{
                                minMoney: 0,
                                maxMoney: '',
                                rate: '',
                                sequence: '1'
                            }],
                            invoiceTypes: res.data.data.invoiceType.split(',')
                        };
                        this.form = data;
                        this.merchant = '';
                        this.company = '';
                        this.bankName = '';
                        this.accountNumber = '';
                    }
                });
            }
        },
        toCloseDia() {
            this.$emit('closeDia');
        },
        toChangeSelectInfo(val) {
            let chooseVal = [...val];
            chooseVal.forEach((s, idx) => {
                if (typeof s == 'string') {
                    chooseVal[idx] = {
                        type: 1,
                        name: s
                    };
                } else {
                    chooseVal[idx] = {
                        type: 0,
                        name: s.name,
                        id: s.id
                    };
                }
            });
            this.form.invoiceCategoryDtos = chooseVal;
            this.$forceUpdate();
        },
        getFullNameEnglish(name) {
            if (name.indexOf('(') != -1 || name.indexOf(')') != -1) {
                return 1;
            } else if (name.indexOf('（') != -1 || name.indexOf('）') != -1) {
                return 2;
            }
        },
        checkNull(value) {
            value = value.replace(/\s+/g, '');
            console.log(value);
            return value;
        },
        addInfo() {
            const isNumber = this.isNumber(this.form.walletRates[0].rate);
            if (!this.merchant) return this.$message.error('请选择商户！');
            if (!this.form.companyName) return this.$message.error('请输入绑定公司！');
            if (!this.form.companyName) return this.$message.error('请输入支付宝账号！');
            if (!this.form.calculationType) return this.$message.error('请选择计算方式！');
            if (!this.form.quotaType) return this.$message.error('请选择限额检查！');
            if (!this.form.walletRates[0].maxMoney) return this.$message.error('请输入最大金额！');
            if (!isNumber) return this.$message.error('请输入费率！');
            if (this.form.invoiceTypes.length == 0) return this.$message.error('请选择开票类型！');
            if (this.invoiceCategorys.length == 0) return this.$message.error('请选择开票类目！');
            if (!this.form.invoiceStrategy) return this.$message.error('请选择开票策略！');
            this.form.invoiceType = this.form.invoiceTypes.join(',');
            let data = {
                ...this.form,
                walletType:3,
                merchantId: this.merchant.id,
                invoiceCategoryIds: this.invoiceCategorys,
                companyName: this.checkNull(this.form.companyName)
            };

            this.$confirm(`确定要添加钱包？`, '提示', {
                type: 'warning'
            })
                .then(() => {
                    this.$emit('sureInfo', data);
                })
                .catch(() => {});
        },
        //判断是不是数字
        isNumber(val) {
            var regPos = /^[0-9]+.?[0-9]*/; //判断是否是数字。
            if (regPos.test(val)) {
                return true;
            } else {
                return false;
            }
        },
        surModifyInfo() {
            const isNumber = this.isNumber(this.form.walletRates[0].rate);
            if (!this.merchant) return this.$message.error('请选择商户！');
            if (!this.form.companyName) return this.$message.error('请输入绑定公司！');
            if (!this.form.calculationType) return this.$message.error('请选择计算方式！');
            if (!this.form.quotaType) return this.$message.error('请选择限额检查！');
            if (!this.form.walletRates[0].maxMoney) return this.$message.error('请输入最大金额！');
            if (!isNumber) return this.$message.error('请输入费率！');
            if (this.form.invoiceTypes.length == 0) return this.$message.error('请选择开票类型！');
            if (this.invoiceCategorys.length == 0) return this.$message.error('请选择开票类目！');
            if (!this.form.invoiceStrategy) return this.$message.error('请选择开票策略！');
            this.form.invoiceType = this.form.invoiceTypes.join(',');
            let data = {
                ...this.form,
                merchantId: this.merchant.id,
                invoiceCategoryIds: this.invoiceCategorys,
                companyName: this.checkNull(this.form.companyName)
            };

            this.$saveCode()
                .then((res) => {
                    this.$emit('modifyInfo', {
                        ...data,
                        password: res.password
                    });
                })
                .catch(() => {});
        },
        handleMerchantChange() {
            this.invoiceCategorys = [];
            this.company = this.merchant.company;
            this.bankName = this.merchant.bankName;
            this.accountNumber = this.merchant.accountNumber;
            this.form.taxesId = this.merchant.taxesId;
            queryInvoiceCategoryByTaxesId(this.merchant.taxesId).then((res) => {
                if (res.data.code == 0 && res.data.data) {
                    this.category = res.data.data;
                }
            });
        },
        diaClose() {
            this.$emit('closeDia');
        }
    },
    props: {
        showDia: {
            type: Boolean,
            require: true
        },
        walletId: {
            type: String,
            require: true
        }
    },
    watch: {
        showDia(val) {
            this.dialogVisible = val;
        }
    },
    created() {
        listRcMerchant({
            enterpriseId: this.$route.query.id
        }).then((res) => {
            if (res.data.code == 0) {
                this.merchantList = res.data.data;
            }
        });
        typeListAll().then((res) => {
            if (res.data.code == 0) {
                this.type = res.data.data;
            }
        });
    }
};
</script>

<style scoped></style>
