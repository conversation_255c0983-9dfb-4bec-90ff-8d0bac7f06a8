<template>
    <div>
        <el-card>
            <div slot="header" class="clearfix">
                <span>钱包充值</span>
            </div>

            <el-form label-width="120px" :rules="rules" :model="form">
                <el-form-item label="钱包名称：">
                    {{ bankInfo.merchantName }}-{{ bankInfo.companyName }}-{{ bankInfo.bankName }}
                </el-form-item>
                <el-form-item label="收款公司：">
                    {{ bankInfo.company }}
                </el-form-item>
                <el-form-item label="收款账号：">
                    {{ bankInfo.accountNumber }}
                </el-form-item>
                <el-form-item label="收款银行：">
                    {{ bankInfo.bankName }}
                </el-form-item>
                <el-form-item label="钱包余额：">
                    {{ showMoney }}
                </el-form-item>
                <el-form-item label="钱包费率："> {{ bankInfo.rate }}% </el-form-item>
                <el-form-item label="充值金额：" prop="money">
                    <el-input v-model="form.money" @input="toWirteMoney" type="number" min="1" style="width: 300px"></el-input>
                </el-form-item>
                <el-form-item label="大写金额：">
                    {{ moneyStr }}
                </el-form-item>
                <el-form-item label="流水单号：" prop="orderNo">
                    <el-input v-model="form.orderNo" type="number" min="1" style="width: 300px"></el-input>
                </el-form-item>
                <el-form-item label="充值备注：">
                    <el-input
                        v-model="form.remark"
                        type="textarea"
                        style="width: 300px"
                        show-word-limit
                        maxlength="200"
                        :autosize="{ minRows: 4, maxRows: 8 }"
                    >
                    </el-input>
                </el-form-item>
                <el-button type="primary" @click="toSubmitInfo">钱包充值</el-button>
            </el-form>
        </el-card>
        <!-- 充值提示 -->
        <el-dialog :visible.sync="dialogVisible" width="60%">
            <div slot="title" class="clearfix">
                <div class="title_dia_blod">自动充值步骤</div>
            </div>
            <template v-if="bankInfo.walletType == 1">
                <div class="setp_box" v-for="(item, index) in setpList" :key="index">
                    <div class="setp_num">{{ index + 1 }}.</div>
                    <div class="setp_info">
                        <div class="setp_word">{{ item.title }}</div>
                        <div class="setp_pic" v-if="item.img">
                            <div class="setp_pic_word">示例：</div>
                            <el-image
                                :src="item.img"
                                :preview-src-list="srcList"
                                @click="toLookDetail(item.img)"
                                :class="[index == 0 ? 'setp_pic_one_img' : 'setp_pic_two_img']"
                                alt=""
                            ></el-image>
                        </div>
                    </div>
                </div>
            </template>
            <template v-if="bankInfo.walletType == 2">
                <img src="@/assets/img/alipay-from.png" style="width: 75%" alt="" />
            </template>
            <span slot="footer" class="dialog-footer">
                <div class="need_center">
                    <el-button type="primary" @click="dialogVisible = false">我知道了</el-button>
                </div>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { digitUppercase } from '@/utils/moneyChange.js';
import { recordApplyInfo,recordWXInfoWallet } from '@/api/account/account.js';
import { client, getFileNameUUID, getTimeNow } from '@/utils/oss.js';
import { Message } from 'element-ui';
import { OSS_URL } from '@/api/config';
export default {
    data() {
        return {
            form: {
                money: ''
            },
            bankInfo: {},
            moneyStr: '零元整',
            rules: {
                money: [{ required: true, message: '请输入充值金额', trigger: 'blur' }],
                orderNo: [{ required: true, message: '请输入流水单号', trigger: 'blur' }]
            },
            peopleList: [],
            fileList: [],
            showMoney: 0,
            ossUrl: OSS_URL,
            dialogVisible: false,
            setpList: [
                {
                    title: '使用对公账户向钱包的收款公司进行公对公打款',
                    img: require('@/assets/img/setp1.png')
                },
                {
                    title: '打款成功之后，等待2-5分钟即可自动到账',
                    img: require('@/assets/img/setp2.png')
                },
                {
                    title: '如未到账，请联系我们客服',
                    img: ''
                }
            ],
            srcList: [''],
            aliInfo: {}
        };
    },
    async created() {
        this.bankInfo = JSON.parse(this.$route.query.info);
        this.showMoney = (this.bankInfo.currentMoney - this.bankInfo.frozenMoney).toFixed(2);
    },
    methods: {
        toWirteMoney() {
            this.moneyStr = digitUppercase(this.form.money);
        },
        handleBeforeUpload(file) {
            const isJPEG = file.name.split('.')[1] === 'jpeg';
            const isJPG = file.name.split('.')[1] === 'jpg';
            const isPNG = file.name.split('.')[1] === 'png';
            const isWEBP = file.name.split('.')[1] === 'webp';
            const isGIF = file.name.split('.')[1] === 'gif';
            const isLt500K = file.size / 1024 / 1024 / 1024 / 1024 < 4;
            if (!isJPG && !isJPEG && !isPNG && !isWEBP && !isGIF) {
                this.$message.error('上传图片只能是 JPEG/JPG/PNG 格式!');
            }
            if (!isLt500K) {
                this.$message.error('单张图片大小不能超过 4mb!');
            }
            return (isJPEG || isJPG || isPNG || isWEBP || isGIF) && isLt500K;
        },
        uploadSuccess(res) {
            console.log('success: ', res);
        },
        uploadError(err) {
            console.log('error: ', err);
            this.$message.error('上传失败！请重试');
        },
        uploadURL(option) {
            var suffix = option.file.name.substring(option.file.name.lastIndexOf('.'));
            //注意哦，这里指定文件夹'image/'，尝试过写在配置文件，但是各种不行，写在这里就可以
            var fileName = '/account/' + getTimeNow() + '/' + getFileNameUUID() + suffix;
            console.log('这个是上传文件的名字', fileName);
            //定义唯一的文件名，打印出来的uid其实就是时间戳
            client()
                .multipartUpload(fileName, option.file, {
                    headers: {
                        'Content-Type': 'image/jpg'
                    },
                    progress: function (percentage, cpt) {
                        option.onProgress({ percent: Math.floor(percentage * 100)});
                    }
                })
                .then((res) => {
                    if(res.res.status === 200) {
                        this.fileList.push({
                            name: option.file.name,
                            url: fileName
                        });
                        option.onSuccess(res);
                    }else {
                        option.onError(res);
                    }
                }).catch((err) => {
                    option.onError(err);
                });
        },
        toRemovePic(url) {
            this.fileLists.forEach((v, index) => {
                if (url == v.url) {
                    this.fileLists.splice(index, 1);
                }
            });
        },
        toSubmitInfo() {
            this.$confirm('确认钱包充值?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then((res) => {
                recordWXInfoWallet({
                    walletId: this.bankInfo.id,
                    ...this.form
                }).then((res) => {
                    if (res.data.code === 0) {
                        Message({
                            message: '提交成功',
                            type: 'success'
                        });
                        setTimeout(() => {
                            this.$router.replace('/account/recharge');
                        }, 1300);
                    }
                });
            });
        },
        toLookDetail(img) {
            this.srcList[0] = img;
        },
        changeInput() {
            this.$forceUpdate();
        }
    }
};
</script>

<style scoped>
.upload_box {
    width: 80%;
    display: flex;
    flex-wrap: wrap;
}
.icon_size {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    color: #fff;
    font-size: 18px;
    opacity: 0;
    transition: 0.4s;
}
.pic_list {
    position: relative;
    width: 146px;
    height: 146px;
    margin-right: 15px;
    margin-bottom: 15px;
}
.notic_box {
    background: #ffffff;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.17);
    border-radius: 15px 15px 15px 15px;
    opacity: 1;
    padding: 16px 20px;
    display: inline-block;
    margin-left: 34px;
    margin-bottom: 44px;
}
.pic_list:hover .icon_size {
    opacity: 1;
}
.title_icon_box {
    display: flex;
    align-items: center;
    padding-top: 18px;
}
.title_icon_box img {
    width: 86px;
    height: 86px;
    margin-right: 18px;
}
.blue_question img {
    width: 24px;
    height: 24px;
    font-size: 18px;
    font-weight: 500;
    color: #0095f9;
    margin-right: 8px;
    user-select: none;
}
.title_icon_box .blod {
    font-size: 32px;
    font-weight: normal;
    color: #2bc15e;
    line-height: 37px;
    font-weight: 600;
}
.title_icon_box .nomal {
    font-size: 20px;
    font-weight: 400;
    color: #2bc15e;
    line-height: 24px;
    margin-top: 11px;
}
.blue_question {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #0095f9;
    margin-top: 11px;
    cursor: pointer;
    user-select: none;
}
.title_attiton {
    margin-top: 38px;
    text-align: center;
    font-size: 18px;
    color: #757575;
}
.setp_box {
    display: flex;
    margin-bottom: 23px;
}
.setp_num {
    font-size: 20px;
    font-weight: 400;
    color: #000000;
    margin-right: 20px;
    line-height: 29px;
}
.setp_word {
    font-weight: 400;
    color: #000000;
    font-size: 20px;
}
.setp_pic {
    display: flex;
    margin-top: 10px;
}
.setp_pic_one_img {
    width: 220px;
    height: 122px;
    display: block;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.17);
}
.setp_pic_two_img {
    width: 148px;
    height: 148px;
    display: block;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.17);
}
.setp_pic_word {
    width: 67px;
    font-size: 14px;
    font-weight: 400;
    color: #757575;
    line-height: 17px;
}
.title_dia_blod {
    font-size: 41px;
    font-weight: 400;
    color: #000000;
    font-weight: 600;
}
.need_center {
    display: flex;
    justify-content: center;
}
.enterprise-box {
    background-color: #fcfdff;
    display: inline-block;
}
.enterprise-box-title {
    background-color: #f6faff;
    color: #02a7f0;
    display: flex;
    align-items: center;
    font-size: 14px;
    padding: 8px 20px;
}
.enterprise-box-title .blue-line {
    background-color: #02a7f0;
    width: 4px;
    height: 16px;
    margin-right: 10px;
}
</style>
