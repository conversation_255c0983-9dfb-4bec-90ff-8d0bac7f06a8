<template>
    <div>
        <el-dialog title="钱包列表" :visible.sync="dialogVisible" width="80%" @open="openDia" @close="closeDia">
            <el-table :data="fullWalletList" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                <el-table-column prop="walletName" label="钱包名称" width="300"> </el-table-column>
                <el-table-column prop="currentMoney" label="钱包余额">
                    <template slot-scope="scope">
                        <span class="money">￥{{ scope.row.currentMoney.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="currentTicket" label="可开票额">
                    <template slot-scope="scope">
                        <span class="money">￥{{ scope.row.currentTicket.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="publicAccountInfo" label="对公充值账号" width="300">
                    <template slot-scope="scope">
                        <div>收款公司:{{ scope.row.company }}</div>
                        <div>收款银行:{{ scope.row.bankName }}</div>
                        <div>收款账户:{{ scope.row.accountNumber }}</div>
                        <div>绑定公司:{{ scope.row.companyName }}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="calculationType" label="计算方式">
                    <template slot-scope="scope">
                        <span>{{ scope.row.calculationType == 1 ? '内扣' : scope.row.calculationType == 2 ? '外扣' : '' }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="address" label="限额检查">
                    <template slot-scope="scope">
                        <span>{{ scope.row.quotaType == 1 ? '本商户' : scope.row.quotaType == 2 ? '全局' : '' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="发放范围/费率">
                    <template slot-scope="scope">
                        <span>0 - {{ scope.row.maxMoney }}/{{ scope.row.rate + '%' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="开票策略">
                    <template slot-scope="scope">
                        <span>{{ scope.row.invoiceStrategy == 1 ? '按充值' : scope.row.invoiceStrategy == 2 ? '减余额' : '' }}</span>
                    </template>
                </el-table-column>
            </el-table>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="closeDia">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { getWalletListDia } from '@/api/branch/enterprise.js';
import { Message } from 'element-ui';
import { filteredByTaxAuth } from '@/store/modules/taxAuth';
import { mapState } from 'vuex';
export default {
    data() {
        return {
            dialogVisible: false,
            fullWalletList: [],
            filteredWalletIds: []
        };
    },
    props: {
        toShowDia: {
            type: Boolean,
            require: true
        },
        enterpriseId: {
            type: String,
            require: true
        }
    },
    computed: {
        ...mapState('taxAuth', ['taxBelongId', 'taxBelongName'])
    },
    watch: {
        toShowDia(val) {
            this.dialogVisible = val;
        }
    },
    methods: {
        async openDia() {
            let res = await getWalletListDia(this.enterpriseId);
            this.fullWalletList = res.data.data || [];
            console.log('[walletListDia] fullWalletList:', this.fullWalletList);
            if (this.fullWalletList.length > 0) {
                console.log('[walletListDia] first wallet object:', this.fullWalletList[0]);
            }
            const filtered = filteredByTaxAuth({
                list: this.fullWalletList,
                taxBelongId: this.taxBelongId,
                taxBelongName: this.taxBelongName,
                field: 'company'
            });
            console.log('[walletListDia] filteredByTaxAuth result:', filtered);
            this.filteredWalletIds = filtered.map(w => w.company);
            console.log('[walletListDia] filteredWalletIds:', this.filteredWalletIds);
            // 排序：有权限的在前，无权限的在后，并处理无权限钱包字段
            this.fullWalletList = this.fullWalletList.slice().sort((a, b) => {
                const aHas = this.filteredWalletIds.includes(a.company);
                const bHas = this.filteredWalletIds.includes(b.company);
                if (aHas === bHas) return 0;
                return aHas ? -1 : 1;
            }).map(wallet => {
                if (!this.filteredWalletIds.includes(wallet.company)) {
                    return {
                        ...wallet,
                        walletName: '其他税地钱包',
                        currentMoney: '-',
                        currentTicket: '-',
                        company: '-',
                        bankName: '-',
                        accountNumber: '-',
                        companyName: '-',
                        calculationType: '-',
                        quotaType: '-',
                        maxMoney: '-',
                        rate: '-',
                        invoiceStrategy: '-'
                    };
                }
                return wallet;
            });
        },
        closeDia() {
            this.$emit('closeDia');
        },
        isWalletVisible(wallet) {
            return this.filteredWalletIds.includes(wallet.company);
        }
    }
};
</script>

<style scoped>
.rate_box {
    align-items: center;
    display: flex;
    margin-bottom: 10px;
}
.rate_box:last-child {
    margin-bottom: 0px;
}
</style>