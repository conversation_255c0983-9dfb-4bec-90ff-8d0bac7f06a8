<template>
    <div>
        <el-form label-width="120px" class="button_box">
            <el-form-item label="关键词搜索：">
                <el-input placeholder="请输入关键词" style="width: 200px" v-model="form.selectValue"></el-input>
                <el-button type="primary" style="margin-left: 20px" @click="toSeachList">搜索</el-button>
            </el-form-item>
            <div style="flex: 1"></div>
            <el-form-item>
                <el-badge :value="peopleNum" :hidden="peopleNum > 0 ? false : true" class="item-number">
                    <el-button type="primary" @click="toShowAddQrcode" v-has="'platform_enterprise_user_add'">新增账号</el-button>
                </el-badge>
                <el-button type="primary" @click="toShowAdds" v-if="backDoor">新增账号1</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
            <!-- <el-table-column prop="code" label="账号ID" min-width="120"></el-table-column> -->
            <el-table-column prop="account" label="登录账号"></el-table-column>
            <el-table-column label="绑定微信昵称">
                <template slot-scope="scope">
                    <div v-if="scope.row.wechatNickName">
                        {{ scope.row.wechatNickName }}
                        <!-- <el-button
                            type="text"
                            style="margin-left: 6px"
                            @click="toUnbindWechat(scope.row.id)"
                            v-has="'platform_enterprise_user_add'"
                            >解绑</el-button
                        > -->
                    </div>
                    <el-popover width="300" trigger="click" v-if="!scope.row.wechatNumber">
                        <wxlogin
                            href="data:text/css;base64,LmltcG93ZXJCb3ggLnFyY29kZSB7CiAgICB3aWR0aDogMTUwcHg7Cn0KLmltcG93ZXJCb3ggLnRpdGxlIHsKICAgIHRleHQtYWxpZ246IGxlZnQ7CiAgICBmb250LXNpemU6IDE2cHg7CiAgICBmb250LXdlaWdodDogYm9sZDsKfQ=="
                            id="wxcode"
                            theme=""
                            :state="scope.row.id + needUrl"
                            :appid="appid"
                            scope="snsapi_login"
                            :redirect_uri="encodeURIComponent(redirect_uri)"
                        ></wxlogin>
                        <!-- <div slot="reference" v-has="'platform_enterprise_user_add'">
                            <el-button type="text" style="margin-left: 6px">二维码绑定</el-button>
                        </div> -->
                    </el-popover>
                </template>
            </el-table-column>
            <el-table-column prop="name" label="姓名"></el-table-column>
            <el-table-column label="角色">
                <template slot-scope="scope">
                    <span v-for="i in scope.row.roleList" :key="i.id">{{ i.name }},</span>
                </template>
            </el-table-column>
            <el-table-column prop="sex" label="性别">
                <template slot-scope="scope">
                    <span v-if="scope.row.sex == 0">男</span>
                    <span v-if="scope.row.sex == 1">女</span>
                </template>
            </el-table-column>
            <!-- <el-table-column prop="createName" label="状态">
                <template slot-scope="scope">
                    {{ scope.row.lockFlag == 0 ? '未禁用' : '禁用' }}
                </template>
            </el-table-column> -->
            <el-table-column prop="remark" label="备注"></el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="200"></el-table-column>
            <el-table-column label="操作" prop="phone" width="200" fixed="right" v-has="'platform_enterprise_user_add'">
                <template slot-scope="scope">
                    <el-button type="text" @click="toModifyInfo(scope.row)" v-has="'platform_enterprise_role_add'" style="font-size: 15px;">修 改</el-button>
                    <!-- <el-button type="text" style="font-size: 15px;" @click="toLockInfo(scope.row.id, scope.row.lockFlag)">{{
                        scope.row.lockFlag == 0 ? '禁用' : '启用'
                    }}</el-button> -->
                    <!-- <el-button type="text" @click="toShowPassword(scope.row)" style="font-size: 15px;" v-if="backDoor">重置密码</el-button> -->
                    <el-button type="text" @click="toRmAccount(scope.row)" v-has="'platform_enterprise_user_add'" style="font-size: 15px;">移 除</el-button>
                    <el-button type="text" @click="toShowSavePassword(scope.row.id)" style="font-size: 15px;">重置安全码</el-button>
                    <!-- <el-button type="text" style="font-size: 15px;" @click="toGetListSysUser(scope.row.id)">绑定企业</el-button> -->
                </template>
            </el-table-column>
        </el-table>
        <div class="pagination">
            <el-pagination
                @current-change="handleCurrentChange"
                :current-page="tableData.current"
                layout="prev, pager, next, jumper"
                :total="tableData.total"
            ></el-pagination>
        </div>
        <addAccount
            :toShowDia="toShowDia"
            :modifyInfo="modifyInfo"
            @closeDia="closeDia"
            @modifyTax="modifyTax"
            @addTax="addTax"
        ></addAccount>
        <el-dialog title="重置密码" :visible.sync="passwordDia" width="60%" @close="cancelDia">
            <el-form>
                <el-form-item label="新密码">
                    <el-input type="password" v-model="password" style="width: 250px"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="cancelDia">取 消</el-button>
                <el-button type="primary" @click="toSureInfo">确 定</el-button>
            </span>
        </el-dialog>
        <el-dialog title="绑定企业" :visible.sync="lookListRoleDia" width="60%" @close="cancelDia">
            <div style="margin-bottom: 20px">
                <el-select v-model="values" @change="toChangeApi" filterable>
                    <el-option v-for="item in optionssss" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                </el-select>
                <el-input
                    placeholder="请输入企业名称"
                    v-model="searchName"
                    style="width: 250px; margin-left: 10px"
                    @blur="toSearchInfo"
                ></el-input>
            </div>
            <el-table :data="tableDatas" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                <el-table-column prop="enterpriseName" label="企业名称"> </el-table-column>
                <el-table-column prop="roleName" label="使用角色">
                    <template slot-scope="scope">
                        <span v-for="(item, index) in scope.row.roleList" :key="index">{{ item.name }},</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" prop="phone" width="170" fixed="right" v-has="'platform_enterprise_user_add'">
                    <template slot-scope="scope">
                        <el-button type="text" v-if="values == 1" @click="toCancelBind(scope.row)" style="font-size: 15px;">取消绑定</el-button>
                        <el-button type="text" v-if="values == 2" @click="toSurelBind(scope.row)" style="font-size: 15px;">选择绑定</el-button>
                    </template>
                </el-table-column>
                <el-dialog width="50%" title="选择绑定" :visible.sync="innerVisible" append-to-body>
                    <el-form ref="form" :model="form" label-width="80px">
                        <el-form-item label="企业名称">
                            {{ choseInfoQy.enterpriseName }}
                        </el-form-item>
                        <el-form-item label="角色名称">
                            <el-select v-model="roles" placeholder="请选择角色" clearable multiple style="width: 250px" @change="update">
                                <el-option v-for="item in channelPtions" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                            </el-select>
                        </el-form-item>
                    </el-form>
                    <span slot="footer" class="dialog-footer">
                        <el-button type="primary" @click="toSureInfoAddNew">确 定</el-button>
                    </span>
                </el-dialog>
            </el-table>
        </el-dialog>
        <el-dialog title="重置安全码" :visible.sync="passwordSaveDia" width="60%" @close="cancelDia">
            <el-form
                :model="ruleForm"
                status-icon
                :rules="rules"
                label-position="right"
                ref="ruleForm"
                label-width="100px"
                class="demo-ruleForm"
            >
                <el-form-item prop="newPwd" label="新密码：">
                    <el-input
                        type="password"
                        v-model="ruleForm.newPwd"
                        auto-complete="off"
                        placeholder="请输入新密码"
                        clearable
                        show-password
                        style="width: 300px"
                    ></el-input>
                </el-form-item>
                <el-form-item prop="checkPass" label="确认密码">
                    <el-input
                        type="password"
                        v-model="ruleForm.checkPass"
                        auto-complete="off"
                        placeholder="请确认密码"
                        clearable
                        show-password
                        style="width: 300px"
                    ></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="cancelDia">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')">确 定</el-button>
            </span>
        </el-dialog>
        <qrcodeVue :dialogVisible="dialogVisible" :qrcode="qrcode" @closeDia="closeDias"></qrcodeVue>
    </div>
</template>

<script>
import {
    listUser,
    addUser,
    updateTaxesUser,
    unbindWx,
    getListSysUserEnterpriseRole,
    getEnterpriseListByUserId,
    bindingEnterprise,
    selectTaxesRole,
    rmEnterprise,
    getUserQrCode,
} from '@/api/branch/enterprise.js';
import { getAuditUserList,setSaveUserPwd} from '@/api/branch/taxes.js';
import addAccount from './addAccount.vue';
import { Message } from 'element-ui';
import wxlogin from 'vue-wxlogin';
import { wxUrlBindCallback, qrcodeAppid } from '@/api/config.js';
import qrcodeVue from './qrcode.vue';
export default {
    components: {
        addAccount,
        wxlogin,
        qrcodeVue
    },
    props: {
        basicInfo: {
            type: Object,
            require: true
        }
    },
    data() {
        let validatePass1 = (rule, value, callback) => {
            if (!value) {
                callback(new Error('请输入新密码'));
            } else if (value.toString().length < 6 || value.toString().length > 18) {
                callback(new Error('密码长度为6-18位'));
            } else if (value === this.ruleForm.oldPwd) {
                callback(new Error('新密码与旧密码一致！'));
            } else {
                callback();
            }
        };
        let validatePass2 = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('请再次输入密码'));
            } else if (value !== this.ruleForm.newPwd) {
                callback(new Error('两次输入的密码不一致'));
            } else {
                callback();
            }
        };
        return {
            form: {
                current: 1,
                size: 10,
                selectValue: ''
            },
            belongId: '',
            tableData: {},
            toShowDia: false,
            modifyInfo: {},
            passwordDia: false,
            password: '',
            qrcodeUrl: '',
            redirect_uri: wxUrlBindCallback,
            needUrl: '',
            passwordId: '',
            appid: qrcodeAppid,
            lookListRoleDia: false,
            tableDatas: [],
            values: 1,
            optionssss: [
                {
                    id: 1,
                    name: '已绑定'
                },
                {
                    id: 2,
                    name: '未绑定'
                }
            ],
            userIds: 1,
            innerVisible: false,
            choseInfoQy: {},
            channelPtions: [],
            roles: [],
            searchName: '',
            dialogVisible: false,
            qrcode: {},
            backDoor: false,
            peopleNum:0,
            passwordSaveDia: false,
            ruleForm: {
                oldPwd: '',
                newPwd: '',
                checkPass: ''
            },
            rules: {
                oldPwd: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
                newPwd: [{ required: true, validator: validatePass1, trigger: 'blur' }],
                checkPass: [{ required: true, validator: validatePass2, trigger: 'blur' }]
            },
            userId: ''
        };
    },
    created() {
        const that = this;
        const url = window.location.href;
        this.needUrl = '_' + url;
        if (this.getQueryString('backDoor') == 'xudong') {
            this.backDoor = true;
        }
        this.getData();
    },
    mounted(){
        this.getPeopleNum()
    },
    methods: {
        closeDia() {
            this.toShowDia = false;
        },
        getData() {
            listUser({
                ...this.form,
                belongId: this.$route.query.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.tableData = res.data.data;
                }
            });
        },
        getQueryString(name) {
            return (
                decodeURIComponent(
                    (new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || [, ''])[1].replace(/\+/g, '%20')
                ) || null
            );
        },
        update() {
            this.$forceUpdate();
        },
        handleSizeChange(size) {
            this.form.size = size;
            this.form.current = 1;
            this.getData();
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.form.current = current;
            this.getData();
        },
        toSeachList() {
            this.form.current = 1;
            this.getData();
        },
        cancelDia() {
            this.passwordDia = false;
            this.password = '';
            this.lookListRoleDia = false;
            this.passwordSaveDia = false;
            this.ruleForm = {
                oldPwd: '',
                newPwd: '',
                checkPass: ''
            };
        },
        toUnbindWechat(id) {
            this.$confirm('是否确认解绑用户', '解绑')
                .then(() => {
                    unbindWx({
                        userId: id
                    }).then((res) => {
                        if (res.data.code == 0) {
                            this.getData();
                        }
                    });
                })
                .catch(() => {});
        },
        toSureInfo() {
            if (!this.password) return Message.error('请输入新密码');
            updateTaxesUser({
                ...this.passwordId,
                password: this.password,
                id: this.passwordId.id,
                type: 2
            }).then((res) => {
                if (res.data.code == 0) {
                    this.passwordDia = false;
                    Message.success(res.data.msg);
                    this.getData();
                }
            });
        },
        toGetListSysUser(id) {
            this.userIds = id;
            this.values = 1;
            getListSysUserEnterpriseRole({
                id: id,
                name: ''
            }).then((res) => {
                if (res.data.code == 0) {
                    this.lookListRoleDia = true;
                    this.tableDatas = res.data.data;
                }
            });
        },
        toShowPassword(id) {
            this.passwordId = id;
            this.passwordDia = true;
        },
        toShowAdds() {
            this.modifyInfo = undefined;
            this.toShowDia = true;
        },
        addTax(e) {
            addUser(e).then((res) => {
                if (res.data.code == 0) {
                    this.toShowDia = false;
                    this.form.current = 1;
                    this.getData();
                }
            });
        },
        modifyTax(e) {
            updateTaxesUser({
                ...e,
                type: 2
            }).then((res) => {
                if (res.data.code == 0) {
                    this.toShowDia = false;
                    this.getData();
                }
            });
        },
        toModifyInfo(item) {
            this.modifyInfo = item;
            this.toShowDia = true;
        },
        toLockInfo(id, ids) {
            let lockFlag = undefined;
            if (ids == 0) {
                lockFlag = 1;
            } else {
                lockFlag = 0;
            }
            this.$confirm(`确定要${lockFlag == 1 ? '禁用' : '启用'}吗？`, '提示', {
                type: 'warning'
            })
                .then(() => {
                    updateTaxesUser({
                        id,
                        lockFlag
                    }).then((res) => {
                        if (res.data.code == 0) {
                            this.toShowDia = false;
                            this.getData();
                        }
                    });
                })
                .catch(() => {});
        },
        toChangeApi() {
            if (this.values == 1) {
                getListSysUserEnterpriseRole({
                    id: this.userIds,
                    name: this.searchName
                }).then((res) => {
                    if (res.data.code == 0) {
                        this.tableDatas = res.data.data;
                    }
                });
            } else {
                getEnterpriseListByUserId({
                    id: this.userIds,
                    name: this.searchName
                }).then((res) => {
                    if (res.data.code == 0) {
                        this.tableDatas = res.data.data;
                    }
                });
            }
        },
        toSurelBind(item) {
            this.choseInfoQy = item;
            selectTaxesRole({
                belongId: item.enterpriseId
            }).then((res) => {
                if (res.data.code == 0) {
                    this.channelPtions = res.data.data;
                    this.innerVisible = true;
                }
            });
        },
        toSureInfoAddNew() {
            bindingEnterprise({
                userId: this.userIds,
                enterpriseId: this.choseInfoQy.enterpriseId,
                roleIds: this.roles
            }).then((res) => {
                if (res.data.code == 0) {
                    this.innerVisible = false;
                    this.roles = [];
                    if (this.values == 1) {
                        getListSysUserEnterpriseRole({
                            id: this.userIds,
                            name: this.searchName
                        }).then((res) => {
                            if (res.data.code == 0) {
                                this.tableDatas = res.data.data;
                            }
                        });
                    } else {
                        getEnterpriseListByUserId({
                            id: this.userIds,
                            name: this.searchName
                        }).then((res) => {
                            if (res.data.code == 0) {
                                this.tableDatas = res.data.data;
                            }
                        });
                    }
                }
            });
        },
        toCancelBind(item) {
            this.$confirm('是否确认取消绑定', '解绑').then(() => {
                rmEnterprise({
                    ...item,
                    userId: this.userIds
                }).then((res) => {
                    if (res.data.code == 0) {
                        if (this.values == 1) {
                            getListSysUserEnterpriseRole({
                                id: this.userIds,
                                name: this.searchName
                            }).then((res) => {
                                if (res.data.code == 0) {
                                    this.tableDatas = res.data.data;
                                }
                            });
                        } else {
                            getEnterpriseListByUserId({
                                id: this.userIds,
                                name: this.searchName
                            }).then((res) => {
                                if (res.data.code == 0) {
                                    this.tableDatas = res.data.data;
                                }
                            });
                        }
                    }
                });
            });
        },
        toSearchInfo() {
            if (this.values == 1) {
                getListSysUserEnterpriseRole({
                    id: this.userIds,
                    name: this.searchName
                }).then((res) => {
                    if (res.data.code == 0) {
                        this.tableDatas = res.data.data;
                    }
                });
            } else {
                getEnterpriseListByUserId({
                    id: this.userIds,
                    name: this.searchName
                }).then((res) => {
                    if (res.data.code == 0) {
                        this.tableDatas = res.data.data;
                    }
                });
            }
        },
        async toShowAddQrcode() {
            let res = await getUserQrCode({
                sceneId: this.$route.query.id,
                type: 2
            });
            if (res.data.code == 0) {
                this.qrcode = res.data.data;
                this.dialogVisible = true;
            }
        },
        closeDias() {
            this.dialogVisible = false;
            this.getData();
            this.getPeopleNum();

        },
        async toRmAccount(item) {
            let res = await this.$saveCode()
            if (res) {
                let ress = await rmEnterprise({
                    userId: item.id,
                    enterpriseId: item.belongId,
                    password:res.password
                });
                if (ress.data.code == 0) {
                    Message.success(ress.data.msg);
                    this.getData();
                }
            }
        },
        getPeopleNum() {
            getAuditUserList({
                businessId: this.$route.query.id
            }).then((res) => {
                if (res.data.code != 0) return false;
                this.peopleNum = res.data.data.length;
            });
        },
        toShowSavePassword(id) {
            this.userId = id;
            this.passwordSaveDia = true;
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    /**新设置安全密码逻辑**/
                    let values = Object.create(null);
                    values.oldPassword = this.ruleForm.oldPwd;
                    values.newPassword = this.ruleForm.newPwd;
                    values.userId = this.userId;
                    setSaveUserPwd(values).then((res) => {
                        if (res.data.code == 0) {
                            this.$message.success('安全码重置成功！');
                            this.passwordSaveDia = false;
                            this.ruleForm = {
                                oldPwd: '',
                                newPwd: '',
                                checkPass: ''
                            };
                        }
                    });
                } else {
                }
            });
        }
    }
};
</script>

<style scoped>
.button_box {
    display: flex;
    align-items: center;
}
</style>