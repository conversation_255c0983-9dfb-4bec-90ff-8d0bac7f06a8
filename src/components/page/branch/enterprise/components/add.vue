<template>
    <div>
        <el-dialog :title="form.id ? '修改企业' : '新增企业'" :visible.sync="dialogVisible" width="60%" @open="openDia" @close="closeDia">
            <el-form label-width="150px" :rules="rules" ref="form" :model="form">
                <el-form-item label="企业简称：" prop="abbrName">
                    <el-input placeholder="请输入企业简称" v-model="form.abbrName" style="width: 250px"></el-input>
                </el-form-item>
                <el-form-item label="企业全称：" prop="name">
                    <el-input placeholder="请输入企业全称" v-model="form.name" style="width: 250px"></el-input>
                </el-form-item>
                <el-form-item label="选择归属税源地：" prop="taxesIds">
                    <el-select
                        v-model="form.taxesIds"
                        placeholder="请选择税源地（可多选）"
                        multiple
                        clearable
                        filterable
                        style="width: 250px"
                        @change="toSureFormRight"
                    >
                        <el-option v-for="item in options" :key="item.taxesId" :label="item.taxesName" :value="item.taxesId"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="选择渠道：">
                    <el-select
                        v-model="form.channelIds"
                        placeholder="请选择渠道（可多选）"
                        clearable
                        style="width: 250px"
                        filterable
                        multiple
                        @change="toSureFormRight"
                    >
                        <el-option v-for="item in channelPtions" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="状态：">
                    <el-switch v-model="form.lockStatus" active-text="已禁用" inactive-text="正常"></el-switch>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeDia">取 消</el-button>
                <el-button type="primary" @click="form.id ? toModifyInfo() : toSubmitInfo()">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { getTaxesList, getChannelList, listTaxesByType, getChannelListByType, lockEnterprise } from '@/api/branch/enterprise.js';
import { Message } from 'element-ui';
export default {
    data() {
        let validatePass1 = (rule, value, callback) => {
            if (!value) {
                callback(new Error('请输入企业全称'));
            } else if (this.getFullNameEnglish(value) == 1) {
                callback(new Error('包含英文括号，注意分别!'));
            } else if (this.getFullNameEnglish(value) == 2) {
                callback(new Error('包含中文括号，注意分别!'));
            } else {
                callback();
            }
        };
        let validatePass2 = (rule, value, callback) => {
            if (!value) {
                callback(new Error('请输入企业全称'));
            } else if (this.getFullNameEnglish(value) == 1) {
                callback(new Error('包含英文括号，注意分别!'));
            } else if (this.getFullNameEnglish(value) == 2) {
                callback(new Error('包含中文括号，注意分别!'));
            } else {
                callback();
            }
        };
        return {
            dialogVisible: false,
            form: {
                taxesIds: [],
                channelIds: []
            },
            channelPtions: [],
            options: [],
            rateDetails: [
                {
                    minMoney: '',
                    maxMoney: '',
                    rate: ''
                }
            ],
            rules: {
                abbrName: [{ required: true, validator: validatePass2, trigger: 'blur' }],
                name: [{ required: true, validator: validatePass1, trigger: 'blur' }],
                taxesIds: [{ required: true, message: '请选择归属税源地', trigger: 'change' }]
            },
            visible: true
        };
    },
    props: {
        toShowDia: {
            type: Boolean,
            require: true
        },
        rowInfo: {
            type: Object,
            require: false
        }
    },
    watch: {
        toShowDia(val) {
            this.dialogVisible = val;
        },
        rowInfo(val) {
            if (val) {
                this.form = JSON.parse(JSON.stringify(val));
                this.form.taxesIds = this.form.taxesId;
                this.form.channelIds = this.form.channelId;
                console.log(this.form);
                this.$forceUpdate();
            } else {
                this.form = {
                    taxesIds: [],
                    channelIds: []
                };
            }
        }
    },
    methods: {
        toAddRateRow() {
            this.rateDetails.push({
                minMoney: '',
                maxMoney: '',
                rate: ''
            });
        },
        toDetelRate(index) {
            this.rateDetails.splice(index, 1);
        },
        async openDia() {
            let id = '';
            if (this.rowInfo) {
                id = this.rowInfo.id;
            }
            let res = await listTaxesByType({
                id,
                type: 2
            });
            let ress = await getChannelListByType({
                id,
                type: 2
            });
            this.options = res.data.data;
            this.channelPtions = ress.data.data;
        },
        closeDia() {
            this.$emit('closeDia');
        },
        checkNull(value) {
            value = value.replace(/\s+/g, '');
            console.log(value);
            return value;
        },
        getFullNameEnglish(name) {
            if (name.indexOf('(') != -1 || name.indexOf(')') != -1) {
                return 1;
            } else if (name.indexOf('（') != -1 || name.indexOf('）') != -1) {
                return 2;
            }
        },
        toSubmitInfo() {
            if (!this.form.abbrName) return Message.error('请输入企业简称！');
            // if(this.getFullNameEnglish(this.form.abbrName)==false) return Message.error('企业简称不能包含英文括号！');
            if (!this.form.name) return Message.error('请输入企业全称！');
            // if(this.getFullNameEnglish(this.form.name)==false) return Message.error('企业全称不能包含英文括号！');
            if (this.form.taxesIds.length == 0) return Message.error('请选择关联税源地！');
            let data = {
                ...this.form,
                abbrName: this.checkNull(this.form.abbrName),
                name: this.checkNull(this.form.name)
            };
            this.$confirm('是否确认新增企业', '新增')
                .then((res) => {
                    this.$emit('addEnterprise', data);
                })
                .catch(() => {});
        },
        toModifyInfo() {
            if (!this.form.abbrName) return Message.error('请输入企业简称！');
            // if(this.getFullNameEnglish(this.form.abbrName)==false) return Message.error('企业简称不能包含英文括号！');
            if (!this.form.name) return Message.error('请输入企业全称！');
            // if(this.getFullNameEnglish(this.form.name)==false) return Message.error('企业全称不能包含英文括号！');
            if (this.form.taxesIds.length == 0) return Message.error('请选择关联税源地！');
            let data = {
                ...this.form,
                enterpriseId: this.form.id,
                abbrName: this.checkNull(this.form.abbrName),
                name: this.checkNull(this.form.name)
            };
            this.$confirm('是否确认修改企业', '新增')
                .then((res) => {
                    this.$emit('modifyEnterprise', data);
                })
                .catch(() => {});
        },
        toSureFormRight() {
            this.$forceUpdate();
        },
        toChangeLock(info) {
            let lockFlag = undefined;
            if (info.lockStatus) {
                lockFlag = 1;
            } else {
                lockFlag = 0;
            }
            var formFile = new FormData();
            formFile.append('enterpriseId', info.id);
            formFile.append('lockFlag', info.lockStatus);
            lockEnterprise({
                enterpriseId: info.id,
                lockFlag: info.lockStatus
            }).then((res) => {
                if (res.data.code == 0) {
                    Message.success('修改成功！');
                    this.$emit('modifyEnterprise', info);
                }
            });
        },
    },
    created() {
        // getChannelList().then((res) => {
        //     this.channelPtions = res.data.data;
        // });
        // getTaxesList().then((res) => {
        //     this.options = res.data.data;
        // });
    }
};
</script>

<style scoped>
.rate_box {
    align-items: center;
    display: flex;
    margin-bottom: 10px;
}
.rate_box:last-child {
    margin-bottom: 0px;
}
</style>