<template>
    <div>
        <div class="filter-container" style="margin-bottom: 20px; display: flex; justify-content: space-between; align-items: center;">
            <div class="filter-left" style="display: flex; align-items: center; ">
                <el-select v-model="walletTypeFilter" placeholder="钱包类型" @change="handleFilterChange" style="width: 150px">
                    <el-option :label="`全部钱包(${totalWalletCount})`" value=""></el-option>
                    <el-option
                        v-for="type in walletTypeOptions.filter(t => t.count > 0)"
                        :key="type.value"
                        :label="`${type.label}(${type.count})`"
                        :value="type.value">
                    </el-option>
                </el-select>
                <span style="margin-left: 30px;">
                    <el-checkbox v-model="hideFrozen" @change="handleFilterChange">隐藏冻结钱包</el-checkbox>
                </span>
            </div>
            <div class="filter-right">
                <el-dropdown @command="handleCommand" v-has="'platform_enterprise_wallet_add'">
                    <el-button type="primary">
                        新增钱包<i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="normal">新增钱包</el-dropdown-item>
                        <el-dropdown-item command="ali">新增支付宝钱包</el-dropdown-item>
                        <el-dropdown-item command="wx">新增微信钱包</el-dropdown-item>
                        <el-dropdown-item command="peixin">新增聚合支付钱包</el-dropdown-item>
                        <el-dropdown-item command="huifu">新增汇付钱包</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </div>
        </div>
        <div class="wallet_box">
            <div v-for="(i, index) in filteredWalletList" :key="index" class="wallet_item">
                <div class="wallet_item" style="margin-bottom: 12px" v-show="i.accountStatus == 2">
                    <el-tooltip effect="dark" content="钱包已被锁定" placement="top">
                        <el-card shadow="never" body-style="padding: 0;" class="wallet_card_lock">
                            <div style="padding: 20px; padding-bottom: 10px;">
                                <div style="text-align: center; display: flex; align-items: center; justify-content: space-between;">
                                    <div class="bank_name">
                                        <el-tooltip :content="i.bankName" placement="top">
                                            <span>{{ i.bankName.length > 6 ? i.bankName.slice(0, 6) + '...' : i.bankName }}</span>
                                        </el-tooltip>
                                        <img width="16px" src="@/assets/img/zsyh.png" v-show="i.bankName.indexOf('招商') > -1 || i.bankName.indexOf('招行') > -1" style="margin-left: 3px" />
                                        <img width="16px" src="@/assets/img/alipay.png" v-show="i.bankName.indexOf('支付宝') > -1" style="margin-left: 3px" />
                                        <img width="16px" src="@/assets/img/pabank.png" v-show="i.bankName.indexOf('平安') > -1" style="margin-left: 3px" />
                                        <img width="16px" src="@/assets/img/wpay.png" v-show="i.bankName.indexOf('微信') > -1" style="margin-left: 3px" />
                                    </div>
                                    <div class="merchant_name">{{ i.merchantName.replace('-测试沙盒环境', '') }}</div>
                                </div>
                                <div class="company_name" style="text-align: center; margin-top: 20px;">{{ i.companyName }}</div>
                            </div>
                            <div class="num_money" style="padding: 0 20px;"><span class="money">￥{{ moneyIntPart(i.currentMoney) }}<span style="font-size: 16px">{{ moneyDeciPart(i.currentMoney) }}</span></span></div>
                            <el-row :gutter="12" style="padding: 1em; border-top: 1px solid #e8e8e8; background: #fafafa;">
                                <el-col :span="6">
                                    <el-button
                                        style="width: 100%; font-size: 16px"
                                        @click="toModifyInfo(i)"
                                        v-has="'platform_enterprise_wallet_add'"
                                    >修改</el-button
                                    >
                                </el-col>
                                <el-col :span="6">
                                    <el-button
                                        style="width: 100%; font-size: 16px"
                                        @click="toModifyCharge(i)"
                                        v-has="'platform_enterprise_wallet_balance_edi'"
                                    >余额</el-button
                                    >
                                </el-col>
                                <el-col :span="6">
                                    <el-button
                                        style="width: 100%; font-size: 16px"
                                        @click="toModifyTick(i)"
                                        v-has="'platform_enterprise_wallet_invoice_edi'"
                                    >票额</el-button
                                    >
                                </el-col>
                                <el-col :span="6">
                                    <el-tooltip
                                        :content="`可开票金额：￥${parseFloat(i.currentTicket).toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`"
                                        placement="top"
                                        :disabled="i.currentTicket < 1"
                                    >
                                        <el-button
                                            style="width: 100%; font-size: 16px"
                                            @click="toShowOpenTick(i)"
                                            v-has="'platform_enterprise_wallet_invoice_apply'"
                                            :class="['ticket-button', { 'has-ticket': i.currentTicket >= 1 }]"
                                            :type="i.currentTicket >= 1 ? 'success' : 'default'"
                                            :plain="i.currentTicket >= 1"
                                        >开票</el-button>
                                    </el-tooltip>
                                </el-col>
                            </el-row>
                        </el-card>
                    </el-tooltip>
                </div>
                <div class="wallet_item" style="margin-bottom: 12px" v-show="i.accountStatus == 1">
                    <el-card shadow="never" body-style="padding: 0;" class="wallet_card">
                        <div style="padding: 20px; padding-bottom: 10px;">
                            <div style="text-align: center; display: flex; align-items: center; justify-content: space-between;">
                                <div class="bank_name">
                                    <el-tooltip :content="i.bankName" placement="top">
                                        <span>{{ i.bankName.length > 6 ? i.bankName.slice(0, 6) + '...' : i.bankName }}</span>
                                    </el-tooltip>
                                    <img width="16px" src="@/assets/img/zsyh.png" v-show="i.bankName.indexOf('招商') > -1 || i.bankName.indexOf('招行') > -1" style="margin-left: 3px"/>
                                    <img width="16px" src="@/assets/img/alipay.png" v-show="i.bankName.indexOf('支付宝') > -1" style="margin-left: 3px"/>
                                    <img width="16px" src="@/assets/img/pabank.png" v-show="i.bankName.indexOf('平安') > -1" style="margin-left: 3px"/>
                                    <img width="16px" src="@/assets/img/wpay.png" v-show="i.bankName.indexOf('微信') > -1" style="margin-left: 3px"/>
                                </div>
                                <div class="merchant_name">{{ i.merchantName.replace('-测试沙盒环境', '') }}</div>
                            </div>
                            <div class="company_name" style="text-align: center; margin-top: 20px;">{{ i.companyName }}</div>
                        </div>
                        <el-tooltip effect="dark" placement="top">
                            <div slot="content">
                                服务费率：{{ i.rate }}%<br/>
                                冻结金额：{{ (parseFloat(i.frozenMoney)).toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}<br/>
                                可开票额：{{ parseFloat(i.currentTicket).toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}<br/>
                            </div>
                            <div class="num_money" style="padding: 0 20px; padding-bottom: 10px;"><span class="money">￥</span>{{ moneyIntPart(i.currentMoney) }}<span style="font-size: 16px">{{ moneyDeciPart(i.currentMoney) }}</span></div>
                        </el-tooltip>
                        <el-row :gutter="12" style="padding:1em; border-top:1px solid #e8e8e8; background: #fafafa">
                            <el-col :span="6">
                                <el-button
                                    style="width: 100%; font-size: 16px"
                                    @click="toModifyInfo(i)"
                                    v-has="'platform_enterprise_wallet_add'"
                                    >修改</el-button
                                >
                            </el-col>
                            <el-col :span="6">
                                <el-button
                                    style="width: 100%; font-size: 16px"
                                    @click="toModifyCharge(i)"
                                    v-has="'platform_enterprise_wallet_balance_edi'"
                                    >余额</el-button
                                >
                            </el-col>
                            <el-col :span="6">
                                <el-button
                                    style="width: 100%; font-size: 16px"
                                    @click="toModifyTick(i)"
                                    v-has="'platform_enterprise_wallet_invoice_edi'"
                                    >票额</el-button
                                >
                            </el-col>
                            <el-col :span="6">
                                <el-tooltip
                                    :content="`可开票金额：￥${parseFloat(i.currentTicket).toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`"
                                    placement="top"
                                    :disabled="i.currentTicket < 1"
                                >
                                    <el-button
                                        style="width: 100%; font-size: 16px"
                                        @click="toShowOpenTick(i)"
                                        v-has="'platform_enterprise_wallet_invoice_apply'"
                                        :class="['ticket-button', { 'has-ticket': i.currentTicket >= 1 }]"
                                        :type="i.currentTicket >= 1 ? 'success' : 'default'"
                                        :plain="i.currentTicket >= 1"
                                    >开票</el-button>
                                </el-tooltip>
                            </el-col>
                        </el-row>
                    </el-card>
                </div>
            </div>
        </div>
        <!-- 新增钱包 -->
        <walletAdd
            :showDia="showDia"
            @closeDia="closeDia"
            @sureInfo="sureInfo"
            @modifyInfo="modifyInfo"
            :walletId="walletId"
            :aliInfo="aliInfo"
            :walletType="walletType"
        ></walletAdd>
        <!-- 新增阿里钱包 -->
        <walletAliAdd
            :showDia="showDiaAli"
            @closeDia="closeDia"
            @sureInfo="sureInfoAli"
            @modifyInfo="modifyInfo"
            :walletId="walletId"
        ></walletAliAdd>
        <walletWxAdd
            :showDia="showDiaWx"
            @closeDia="closeDia"
            @sureInfo="sureInfoWx"
            @modifyInfo="modifyInfo"
            :walletId="walletId"
        ></walletWxAdd>
        <walletPeixinAdd :showDia="showDiaPeixin" @closeDia="closeDia" @sureInfo="sureInfoPeixin" :walletId="walletId"> </walletPeixinAdd>
        <walletHuifuAdd :showDia="showDiaHuifu" @closeDia="closeDia" @modifyInfo='modifyInfo' @sureInfo="sureInfoHuifu" :walletId="walletId"> </walletHuifuAdd>
        <!-- 余额调整 -->
        <chargeChange :showDia="showChargeDia" @closeDia="closeDia" :walletInfo="walletInfo" @sureMoney="sureMoney"></chargeChange>
        <!-- 票额调整 -->
        <ticketBalance :showDia="showTickDia" @closeDia="closeDia" :walletInfo="walletInfo" @sureMoney="sureTicket"></ticketBalance>
        <!-- 开票申请 -->
        <sendTicket :showDia="showSendTicketDia" @closeDia="closeDia" :walletInfo="walletInfo" @sureMoney="sureTicket"></sendTicket>
        <!-- 支付宝钱包签约 -->
        <el-dialog title="扫码签约" :visible.sync="dialogVisible" width="40%" top="5vh">
            <iframe :src="qcodePlace" style="height: 600px; width: 100%; border: none" class="iframe-size"></iframe>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="toSureAliWallet">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { walletList, insertAdd, updateWallet, canSureSend, insertAliPay, insertWxPay, insertPeixin, insertHuifu } from '@/api/branch/enterprise';
import { Message } from 'element-ui';
import walletAdd from './walletAdd.vue';
import walletAliAdd from './walletAliAdd.vue';
import walletWxAdd from './walletWxAdd.vue';
import walletPeixinAdd from './walletPeixinAdd.vue'
import walletHuifuAdd from './walletHuifuAdd.vue'
import chargeChange from './chargeChange.vue';
import ticketBalance from './ticketBalance.vue';
import sendTicket from './sendTicket.vue';
import { getUserInfo, getUserEnterprise } from '@/api/system/login';
import { mapState } from 'vuex';
import { filteredByTaxAuth } from '@/store/modules/taxAuth';

export default {
    components: {
        walletAdd,
        chargeChange,
        ticketBalance,
        sendTicket,
        walletAliAdd,
        walletWxAdd,
        walletPeixinAdd,
        walletHuifuAdd
    },
    props: {
        basicInfo: {
            type: Object,
            require: true
        }
    },
    watch: {
        basicInfo(val) {
            this.enterpriseId = val.enterpriseId;
            this.getData(val.enterpriseId);
        }
    },
    computed: {
        ...mapState('taxAuth', ['taxBelongId', 'taxBelongName'])
    },
    data() {
        return {
            form: {
                current: 1,
                size: 10,
                enterpriseName: ''
            },
            hideFrozen: true,
            walletTypeFilter: '',
            tableData: {},
            walletList: [],
            filteredWalletList: [],
            showDia: false,
            enterpriseId: '',
            walletId: undefined,
            showChargeDia: false,
            walletInfo: {
                companyName: ''
            },
            showTickDia: false,
            showSendTicketDia: false,
            showDiaAli: false,
            dialogVisible: false,
            qcodePlace: '',
            aliInfo: {},
            walletType: '',
            showDiaWx: false,
            showDiaPeixin: false,
            showDiaHuifu: false,
            totalWalletCount: 0,
            walletTypeOptions: [
                { label: '招商银行', value: '1', count: 0 },
                { label: '支付宝', value: '2', count: 0 },
                { label: '微信财付通', value: '3', count: 0 },
                { label: '平安银行', value: '4', count: 0 },
                { label: '聚合支付-PX', value: '5', count: 0 },
                { label: '兴业银行', value: '6', count: 0 },
                { label: '汇付支付', value: '7', count: 0 },
                { label: '盛付通', value: '8', count: 0 },
                { label: '其他', value: 'other', count: 0 }
            ],
        };
    },
    methods: {
        getData(val) {
            walletList({
                enterpriseId: this.$route.query.id
            }).then((res) => {
                if (res.data.code == 0) {
                    res.data.data.forEach((s) => {
                        s.canUseMoney = (parseFloat(s.currentMoney) - parseFloat(s.frozenMoney)).toFixed(2);
                    });
                    // 1. 拿到原始钱包数组
                    this.allWallets = res.data.data || [];
                    let filtered = filteredByTaxAuth({
                        list: res.data.data,
                        taxBelongId: this.taxBelongId,
                        taxBelongName: this.taxBelongName,
                        field: 'company'
                    });
                    this.walletList = filtered;
                    // 2. 被过滤的钱包数量
                    this.disabledWalletCount = this.allWallets.length - this.walletList.length;
                    this.walletTypeOptions.forEach(type => {
                        type.count = 0;
                    });
                    this.walletList.forEach(wallet => {
                        const type = this.walletTypeOptions.find(t => t.value === wallet.walletType);
                        if (type) {
                            type.count++;
                        }
                    });
                    this.totalWalletCount = this.allWallets.length;
                    if(this.disabledWalletCount > 0){
                        this.walletTypeOptions.push({
                            label: '其他税地钱包',
                            value: 'disabled',
                            count: this.disabledWalletCount
                        });
                    }
                    this.handleFilterChange();
                } else {
                    Message.error(res.data.msg);
                }
            });
        },
        handleFilterChange() {
            // 应用过滤条件
            this.filteredWalletList = this.walletList.filter(item => {
                // 如果勾选了隐藏冻结的钱包，则过滤掉冻结的钱包
                if (this.hideFrozen && item.accountStatus === 2) {
                    return false;
                }
                // 如果选择了钱包类型，则过滤对应的钱包类型
                if (this.walletTypeFilter && item.walletType !== this.walletTypeFilter) {
                    return false;
                }
                return true;
            });

            // 排序：先按currentMoney倒序，再按currentTicket倒序
            this.filteredWalletList.sort((a, b) => {
                const moneyDiff = parseFloat(b.currentMoney) - parseFloat(a.currentMoney);
                if (moneyDiff !== 0) return moneyDiff;
                return parseFloat(b.currentTicket) - parseFloat(a.currentTicket);
            });
        },
        sureInfo(e) {
            insertAdd({
                ...e,
                enterpriseId: this.$route.query.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.getData();
                    this.showDia = false;
                    this.walletId = undefined;
                } else {
                    // Message.error(res.data.msg)
                }
            });
        },
        sureInfoAli(e) {
            insertAliPay({
                ...e,
                enterpriseId: this.$route.query.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.showDiaAli = false;
                    this.walletId = '';
                    this.dialogVisible = true;
                    this.qcodePlace = res.data.data.body;
                } else {
                    // Message.error(res.data.msg)
                }
            });
        },
        sureInfoWx(e) {
            insertWxPay({
                ...e,
                enterpriseId: this.$route.query.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.getData();
                    this.showDiaWx = false;
                    this.walletId = '';
                } else {
                    // Message.error(res.data.msg)
                }
            });
        },
        sureInfoPeixin(e) {
            insertPeixin({
                ...e,
                enterpriseId: this.$route.query.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.getData();
                    this.showDiaPeixin = false;
                    this.walletId = '';
                } else {
                    // Message.error(res.data.msg)
                }
            });
        },
        sureInfoHuifu(e) {
            insertHuifu({
                ...e,
                enterpriseId: this.$route.query.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.getData();
                    this.showDiaHuifu = false;
                    this.walletId = '';
                } else {
                    // Message.error(res.data.msg)
                }
            });
        },
        modifyInfo(e) {
            updateWallet({
                ...e,
                enterpriseId: this.$route.query.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.getData();
                    this.showDia = false;
                    this.showDiaAli = false;
                    this.showDiaWx = false;
                    this.showDiaPeixin = false;
                    this.showDiaHuifu = false;
                    this.walletId = undefined;
                }
            });
        },
        sureMoney() {
            this.showChargeDia = false;
            this.getData();
        },
        sureTicket() {
            this.showTickDia = false;
            this.showSendTicketDia = false;
            this.getData();
        },
        closeDia() {
            this.walletId = undefined;
            this.walletType = '';
            this.showDia = false;
            this.showChargeDia = false;
            this.showTickDia = false;
            this.showSendTicketDia = false;
            this.showDiaAli = false;
            this.showDiaWx = false;
            this.showDiaPeixin = false;
            this.showDiaHuifu = false;
        },
        toSureAliWallet() {
            this.dialogVisible = false;
            this.getData();
        },
        handleCommand(command) {
            if (command === 'normal') {
                this.walletId = '';
                this.showDia = true;
            } else if (command === 'ali') {
                this.walletId = '';
                this.showDiaAli = true;
            } else if (command === 'wx') {
                this.walletId = '';
                this.showDiaWx = true;
            } else if (command === 'peixin') {
                this.walletId = '';
                this.showDiaPeixin = true;
            } else if (command === 'huifu') {
                this.walletId = '';
                this.showDiaHuifu = true;
            }
        },
        toModifyInfo(item) {
            this.walletType = item.walletType;
            this.walletId = item.id;
            // 支付宝
            if (item.walletType == 2) {
                this.aliInfo = {
                    aliUserName: item.aliUserName,
                    alipay: item.alipay
                };
                this.showDiaAli = true;
            }
            // 微信
            else if (item.walletType == 3) {
                this.showDiaWx = true;
            }
            // 聚合支付-PX
            else if (item.walletType == 5) {
                this.showDiaPeixin = true;
            }
            // 汇付支付
            else if (item.walletType == 7) {
                this.showDiaHuifu = true;
            }
            // 其他（招商、平安、兴业、其他）
            else {
                this.showDia = true;
            }
        },
        toModifyCharge(item) {
            this.walletInfo = item;
            this.showChargeDia = true;
        },
        toModifyTick(item) {
            this.walletInfo = item;
            this.showTickDia = true;
        },
        toShowOpenTick(item) {
            this.walletInfo = item;
            canSureSend({
                enterpriseId: this.$route.query.id
            }).then((res) => {
                if (res.data.data) {
                    this.$router.push('/account/ticket_apply?info=' + JSON.stringify(item));
                } else {
                    Message.error('请去企业详情补充信息');
                }
            });
        },
        toJumpAddMoney(i) {
            this.$router.push(`/account/applyMoney?info=${JSON.stringify(i)}`);
        },
        moneyIntPart(money) {
            money = parseFloat(money)
            let res = money.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
            let dotIndex = res.indexOf('.')
            res = res.slice(0, dotIndex);
            return res;
        },
        moneyDeciPart(money) {
            money = parseFloat(money)
            let res = money.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
            let dotIndex = res.indexOf('.')
            res = res.slice(dotIndex);
            return res;
        }
    },
};
</script>

<style scoped>
.word_money_box {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    width: 100%;
    left: 0;
    top: 10px;
}
.company_name {
    font-size: 1.1em;
    margin-top: 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.topinfo {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.wallet_box {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}
.wallet_item {
    width: 350px;
    margin-right: 20px;
}
.wallet_item:nth-child(4n + 4) {
    margin-right: 0;
}
.bank_name {
    font-size: 12px;
    border: 1px solid #d9d9d9;
    padding: 0 7px;
    border-radius: 4px;
    color: rgba(0,0,0,0.65);
    background: #fafafa;
    align-items: center;
    display: flex
}
.num_money {
    font-family: SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;
    font-size: 32px;
    text-align: center;
    margin-top: 10px;
}
.word_info {
    text-align: center;
    margin-bottom: 40px;
    position: relative;
    color: rgba(0,0,0,0.65)
}
.merchant_name {
    font-size: 12px;
    border: 1px solid transparent;
    background-color: #e9f4fe;
    padding: 1px 7px;
    border-radius: 4px;
    color: #439ef9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 150px;
}
.el-card {
    border-radius: 8px;

}
.wallet_card {
    background: linear-gradient(to top right, #ffffff 25%, #ffd700d9 100%);
    background-origin: center;
}
.wallet_card_lock {
    background: linear-gradient(to top right, #ffffff 25%, #c0c4cc 100%);
    background-origin: center;
}
.ticket-button {
  position: relative;
  transition: all 0.3s;
}

.ticket-button.has-ticket {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(103, 194, 58, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0);
  }
}
</style>
