<template>
    <el-dialog :visible.sync="dialogVisible" @close="toCloseDia" width="50%" title="开票申请">
        <el-form label-width="120px" :model="form" :rules="rules">
            <el-form-item label="钱包：">
                <span>{{ walletInfo.merchantName }}-{{ walletInfo.companyName }}-{{walletInfo.bankName}}</span>
            </el-form-item>
            <el-form-item label="开票金额：" prop="money">
                <el-input v-model="form.money" style="width: 250px" type='number'/>
                <div>
                    可开额<span class="money" style="color: #1b8d1b">￥{{ parseFloat(showMoney).toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</span
                    >元
                </div>
            </el-form-item>
            <el-form-item label="开票类型：" prop="invoiceType">
                <el-select v-model="form.invoiceType" style="width: 250px" filterable>
                    <el-option v-for="item in type" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="开票类目：" prop="invoiceCategory">
                <el-select v-model="form.invoiceCategory" style="width: 250px" filterable>
                    <el-option v-for="item in category" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="审核人：" prop="auditUserId">
                <el-select v-model="form.auditUserId" style="width: 250px" @change="inputChange">
                    <el-option v-for="item in peopleList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="发票备注：" >
                <el-input
                        v-model="form.remark"
                        type="textarea"
                        style="width: 400px"
                        show-word-limit
                        maxlength="200"
                        :autosize="{ minRows: 3, maxRows: 8 }"
                    >
                    </el-input>
                    <div style="color: red;">以上信息会加在发票右下角备注栏</div>
            </el-form-item>
            <el-form-item label="提醒事项：">
                <el-input
                    v-model="form.tips"
                    type="textarea"
                    style="width: 400px"
                    show-word-limit
                    maxlength="200"
                    :autosize="{ minRows: 3, maxRows: 8 }"
                >
                </el-input>
                <div style="color: red;">可在此处给审核员留言</div>
            </el-form-item>
            <el-form-item label="结算单据：">
                    <el-upload action="" list-type="picture-card"
                        :http-request="uploadURL"
                        :multiple="false"
                        :on-success="uploadSuccess"
                        :on-error="uploadError"
                        :on-remove="fileRemove"
                        :limit="1"
                    >
                        <i class="el-icon-upload"></i>
                    </el-upload>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="toCloseDia">取 消</el-button>
            <el-button type="primary" @click="surModifyInfo()">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>

import { insertTicketRecord, getTypeInfoByWalletId, getAuditPersonList, uploadTemplate } from '@/api/branch/enterprise';
import { client, getFileNameUUID, getTimeNow } from '../../../../../utils/oss.js';
import { Message } from 'element-ui';
export default {
    data() {
        return {
            dialogVisible: false,
            form: {
                file: {}
            },
            type: [],
            category: [],
            peopleList: [],
            showMoney: 0,
            rules: {
                money: [{ required: true, message: '请输入开票金额', trigger: 'blur' }],
                invoiceType: [{ required: true, message: '请选择开票类型', trigger: 'change' }],
                auditUserId: [{ required: true, message: '请选择审核人', trigger: 'blur' }],
                invoiceCategory: [{ required: true, message: '请选择开票类目', trigger: 'change' }],
            },
            uploadSuccessed: false,
        };
    },
    methods: {
        toCloseDia() {
            this.$emit('closeDia');
        },
        toDown() {
            uploadTemplate().then((res) => {
                window.open(res.data.data.url);
            });
        },
        surModifyInfo() {
            if (!this.form.money) {
                Message.error('请输入开票金额！');
                return false;
            }
            let ticketMoney =  Number(this.form.money);
            if (ticketMoney <= 0) {
                Message.error('开票金额必须大于0！');
                return false;
            }
            if (ticketMoney > this.showMoney) {
                Message.error('开票金额大于可开票金额！');
                return false;
            }
            if (!this.form.invoiceCategory || !this.form.invoiceType) {
                Message.error('请选择开票类目和发票类型！');
                return false;
            }

            var confirmText = this.uploadSuccessed? '确认要开票?' : '未提供单据，确认要开票?';
            this.$confirm(confirmText, '提示', {
                type: 'warning'
            })
                .then(() => {
                    insertTicketRecord({
                        ...this.form,
                        walletId: this.walletInfo.id
                    }).then((res) => {
                        if (res.data.code == 0) {
                            this.$emit('sureMoney');
                        } else {
                            Message.error(res.data.msg);
                        }
                    });
                })
                .catch(() => {});
        },
        uploadSuccess(res) {
            this.uploadSuccessed = true;
            this.$message.success('上传成功！');

            let parentElements = document.querySelectorAll('.el-upload-list__item');
            parentElements.forEach((parentElement) => {
                let thumbnailImg =
                    parentElement.querySelector('.el-upload-list__item-thumbnail');
                if (res.name.split('.')[1] === "pdf" && thumbnailImg) {
                   thumbnailImg.src = require('@/assets/img/misc/thumbnail_invoice.png');
                }
            });
        },
        uploadError(err) {
            this.uploadSuccessed = false;
            this.$message.error('上传失败！请重试');
        },
        fileRemove(file, uploadFiles) {
            this.uploadSuccessed = false;
        },
        uploadURL(option) {
            this.uploadSuccessed = false;
            //注意哦，这里指定文件夹'image/'，尝试过写在配置文件，但是各种不行，写在这里就可以
            var suffix = option.file.name.substring(option.file.name.lastIndexOf('.'));
            var fileName = '/billFile/' + getTimeNow() + '/' + getFileNameUUID() + suffix;
            //定义唯一的文件名，打印出来的uid其实就是时间戳
            client()
                .multipartUpload(fileName, option.file, {
                    progress: function (percentage, cpt) {}
                })
                .then((res) => {
                    if(res.res.status === 200) {
                        this.form.file = {
                            url: fileName,
                            name: option.file.name
                        };
                        option.onSuccess(res);
                    }else {
                        option.onError(res);
                    }
                }).catch((err) => {
                    option.onError(err);
                });
        },
        inputChange(){
            this.$forceUpdate()
        }
    },
    props: {
        showDia: {
            type: Boolean,
            require: true
        },
        walletInfo: {
            type: Object,
            require: true
        }
    },
    watch: {
        showDia(val) {
            this.dialogVisible = val;
        },
        walletInfo(val) {
            getTypeInfoByWalletId({
                walletId: val.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.type = res.data.data.type;
                    this.category = res.data.data.category;
                }
            }),
                getAuditPersonList({
                    type: 2,
                    walletId: val.id,
                    enterpriseId: ''
                }).then((res) => {
                    this.peopleList = res.data.data;
                    this.form.auditUserId = this.peopleList[0].id
                });
            this.showMoney = Number((val.currentTicket - val.frozenTicket).toFixed(2));
        }
    },
    created() {}
};
</script>

<style scoped>
.down_up {
    display: flex;
    justify-content: space-between;
}
</style>
