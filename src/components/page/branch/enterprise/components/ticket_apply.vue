<template>
    <div>
        <!-- 步骤指示器 -->
        <el-steps :active="currentStep - 1" align-center style="margin-bottom: 30px;">
            <el-step title="选择票额记录" description="选择需要开票的票额记录"></el-step>
            <el-step title="填写申请信息" description="完善开票申请详细信息"></el-step>
        </el-steps>

        <!-- 第一步：选择票额记录 -->
        <div v-if="currentStep === 1">
            <!-- 钱包票额记录选择 -->
            <el-card style="margin-top: 20px;" class="invoice-record-card">
                <div slot="header" class="clearfix">
                    <p>选择票额记录</p>
                    <span style="color: #606266; font-size: 14px;">
                        <strong>钱包：</strong>{{ walletInfo.merchantName }}-{{ walletInfo.companyName }}-{{ walletInfo.bankName }}
                    </span>
                    <el-button style="float: right; padding: 3px 0" type="text" @click="loadInvoiceRecords">刷新</el-button>
                </div>

                <div v-loading="loading" class="table-container">
                    <div v-if="invoiceRecords.length === 0" style="text-align: center; color: #999; padding: 20px;">
                        暂无可开票额记录
                    </div>
                    <div v-else>
                        <div style="margin-bottom: 15px;">
                            <el-alert
                                :type="isAllPagesSelected ? 'success' : 'info'"
                                :closable="false"
                                show-icon>
                                <template slot="title">
                                    <span v-if="isAllPagesSelected" style="color: #67C23A;">
                                        <i class="el-icon-success"></i> 已全选所有记录
                                    </span>
                                    <span v-else>
                                        已选择 {{ selectedRecords.length }} / {{ allInvoiceRecords.length }} 项
                                    </span>
                                    ，总开票金额：<span class="money" style="color: #E6A23C; font-weight: bold;">￥{{ formatMoney(calculateTotalAmount()) }}</span>
                                    <span v-if="selectedRecords.length > 0 && !isAllPagesSelected" style="margin-left: 10px; color: #909399; font-size: 12px;">
                                        ({{ Math.round(selectedRecords.length / allInvoiceRecords.length * 100) }}%)
                                    </span>
                                </template>
                            </el-alert>
                        </div>

                        <!-- 跨分页操作按钮 -->
                        <div class="cross-page-actions">
                            <div class="action-buttons">
                                <el-button-group>
                                    <el-tooltip content="选择所有页面的全部记录" placement="top">
                                        <el-button
                                            size="small"
                                            type="primary"
                                            :disabled="isAllPagesSelected"
                                            @click="selectAllPages">
                                            <i class="el-icon-check"></i> 全选所有页 ({{ allInvoiceRecords.length }}项)
                                        </el-button>
                                    </el-tooltip>
                                    <el-tooltip content="取消所有选择" placement="top">
                                        <el-button
                                            size="small"
                                            type="warning"
                                            :disabled="selectedRecords.length === 0"
                                            @click="clearAllPages">
                                            <i class="el-icon-close"></i> 取消全选
                                        </el-button>
                                    </el-tooltip>
                                </el-button-group>
                            </div>

                            <div class="page-info">
                                <span class="info-text">
                                    共 {{ Math.ceil(allInvoiceRecords.length / pagination.size) }} 页，当前第 {{ pagination.current }} 页
                                </span>
                                <el-tag v-if="selectedRecords.length > 0" size="mini" :type="isAllPagesSelected ? 'success' : 'primary'">
                                    {{ isAllPagesSelected ? '全选状态' : '部分选择' }}
                                </el-tag>
                            </div>
                        </div>


                        <el-table
                            :data="invoiceRecords"
                            style="width: 100%; color: rgba(0, 0, 0, 0.65)"
                            ref="invoiceTable"
                            header-row-class-name="table_header"
                            header-cell-class-name="table_header_cell"
                            cell-class-name="table_cell"
                            :row-class-name="getRowClassName"
                            @selection-change="handleSelectionChange"
                            @select-all="handleSelectAll">
                            <el-table-column type="selection" width="45" :selectable="checkSelectable"></el-table-column>
                            <el-table-column prop="typeStr" label="业务类型" width="100" align="center">
                                <template slot-scope="scope">
                                    <el-tag :type="getTypeTagType(scope.row.type)" size="small">
                                        {{ scope.row.typeStr }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="subjectId" label="业务ID" min-width="160" show-overflow-tooltip>
                                <template slot-scope="scope">
                                    <span style="font-family: monospace; font-size: 12px;">{{ scope.row.subjectId }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="projectName" label="项目名称" min-width="150" show-overflow-tooltip>
                                <template slot-scope="scope">
                                    <span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: block;">{{ getProjectNameForRecord(scope.row) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="totalMoney" label="全部票额" min-width="120" align="right">
                                <template slot-scope="scope">
                                    <span class="money" style="color: #67C23A; font-weight: bold;">￥{{ formatMoney(scope.row.totalMoney) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="usedMoney" label="已用票额" min-width="120" align="right">
                                <template slot-scope="scope">
                                    <span class="money" style="color: #E6A23C;">￥{{ formatMoney(scope.row.usedMoney) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="availableMoney" label="可用票额" min-width="120" align="right">
                                <template slot-scope="scope">
                                    <div style="display: flex; align-items: center; justify-content: flex-end;">
                                        <span class="money" :style="{
                                            color: scope.row.isRequired ? '#F56C6C' : '#409EFF',
                                            fontWeight: 'bold'
                                        }">
                                            ￥{{ formatMoney(scope.row.availableMoney) }}
                                        </span>
                                        <el-tooltip v-if="scope.row.isRequired" content="负数票额，必须选择" placement="top">
                                            <i class="el-icon-warning" style="color: #F56C6C; margin-left: 5px;"></i>
                                        </el-tooltip>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="status" label="状态" width="100" align="center">
                                <template slot-scope="scope">
                                    <el-tag :type="scope.row.status === 1 ? 'warning' : 'info'" size="small">
                                        {{ getStatusDescription(scope.row.status) }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="createTime" label="创建时间" min-width="160">
                                <template slot-scope="scope">
                                    <span style="font-size: 12px;">{{ formatTime(scope.row.createTime) }}</span>
                                </template>
                            </el-table-column>
                        </el-table>

                        <!-- 分页组件 -->
                        <div class="pagination">
                            <el-pagination
                                @current-change="handlePageChange"
                                :current-page="pagination.current"
                                :page-size="pagination.size"
                                :total="pagination.total"
                                layout="prev, pager, next, jumper">
                            </el-pagination>
                        </div>
                    </div>
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <el-button
                        type="primary"
                        @click="goToNextStep"
                        :disabled="selectedRecords.length === 0 || calculateTotalAmount() < 0">
                        下一步
                    </el-button>
                    <div v-if="calculateTotalAmount() < 0 && selectedRecords.length > 0" style="color: #F56C6C; font-size: 12px; margin-top: 8px;">
                        <i class="el-icon-warning"></i> 总开票金额为负值，无法提交申请
                    </div>
                </div>
            </el-card>
        </div>

        <!-- 第二步：填写申请信息 -->
        <div v-if="currentStep === 2">
            <!-- 如果没有选择记录，显示提示并返回第一步 -->
            <div v-if="selectedRecords.length === 0" style="text-align: center; padding: 50px;">
                <el-alert
                    title="请先选择票额记录"
                    description="您还没有选择任何票额记录，请先返回第一步选择记录。"
                    type="warning"
                    :closable="false"
                    show-icon>
                </el-alert>
                <el-button type="primary" @click="goToPrevStep" style="margin-top: 20px;">
                    返回第一步
                </el-button>
            </div>

            <!-- 开票预览 -->
            <div v-else>
            <!-- 项目开票类目选择 -->
            <el-card v-if="projectInvoiceData.length > 0" v-loading="loadingProject">
                <div slot="header" class="clearfix">
                    <span>选择开票类目</span>
                    <el-tag size="mini" type="info" style="margin-left: 10px;">{{ projectInvoiceData.length }}个项目</el-tag>
                </div>
                <div>
                    <el-alert
                        title="请为每个项目选择对应的开票类目"
                        description="选择的开票类目将用于该项目相关的发票开具，请根据项目实际情况选择合适的开票类目。"
                        type="info"
                        :closable="false"
                        show-icon
                        style="margin-bottom: 20px;">
                    </el-alert>

                    <div class="project-invoice-list">
                        <div v-for="(project, index) in projectInvoiceData" :key="index" class="project-item">
                            <el-row :gutter="20" type="flex" align="middle">
                                <el-col :span="8">
                                    <div class="project-info">
                                        <div class="project-name">
                                            <i class="el-icon-folder-opened" style="color: #409EFF; margin-right: 8px;"></i>
                                            <span style="font-weight: bold; color: #303133;">{{ project.projectName }}</span>
                                            <span class="money" style="margin-left: 10px; color: #E6A23C; font-weight: bold; font-size: 14px;">
                                                ￥{{ formatMoney(getProjectInvoiceAmount(project)) }}
                                            </span>
                                        </div>
                                    </div>
                                </el-col>
                                <el-col :span="16">
                                    <div class="category-selector">
                                        <!-- 负值项目提示 -->
                                        <div v-if="project.isNegativeAmount">
                                            <el-alert
                                                title="负值项目"
                                                :description="`该项目金额为负值（${formatMoney(getProjectInvoiceAmount(project))}），负值项目无需选择开票类目，会自动计入总开票金额。`"
                                                type="warning"
                                                :closable="false"
                                                show-icon
                                                size="small">
                                            </el-alert>
                                        </div>

                                        <el-select
                                            v-model="project.selectedCategoryId"
                                            placeholder="请选择开票类目"
                                            style="width: 300px;"
                                            filterable
                                            clearable
                                            v-if="!project.isNegativeAmount"
                                            @change="onProjectCategoryChange(project, $event)">
                                            <el-option
                                                v-for="category in project.invoiceCategories"
                                                :key="category.invoiceCategoryId"
                                                :label="category.fullCategoryName"
                                                :value="category.invoiceCategoryId">
                                                <span style="float: left;">{{ category.fullCategoryName }}</span>
                                                <span style="float: right; color: #8492a6; font-size: 12px;">
                                                    ID: {{ category.invoiceCategoryId.substring(0, 8) }}...
                                                </span>
                                            </el-option>
                                        </el-select>
                                        <el-button
                                            v-if="project.selectedCategoryId"
                                            size="mini"
                                            type="success"
                                            icon="el-icon-check"
                                            style="margin-left: 10px;">
                                            已选择
                                        </el-button>
                                    </div>
                                </el-col>
                            </el-row>
                        </div>
                    </div>

                    <!-- 选择状态汇总 -->
                    <div style="margin-top: 20px; padding: 15px; background-color: #f5f7fa; border-radius: 4px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <span style="color: #606266;">选择进度：</span>
                                <el-tag :type="getProjectSelectionProgressType()" size="small">
                                    {{ getSelectedProjectCount() }} / {{ projectInvoiceData.length }} 项目已选择
                                </el-tag>
                            </div>
                            <div>
                            </div>
                        </div>
                    </div>
                </div>
            </el-card>

            <el-card style="margin-top: 20px;">
                <div slot="header" class="clearfix">
                    <span>开票预览</span>
                </div>
                <div>
                    <p><strong>钱包名称：</strong>{{ walletInfo.merchantName }}-{{ walletInfo.companyName }}-{{ walletInfo.bankName }}</p>
                    <p><strong>开票总额：</strong><span class="money" style="color: #E6A23C; font-weight: bold; font-size: 18px;">￥{{ formatMoney(calculateCategorySummaryTotal()) }}</span></p>
                </div>

                <!-- 开票类目金额汇总 -->
                <div style="margin-top: 15px;">
                    <h4 style="margin-bottom: 15px; color: #409EFF;">各开票类目金额汇总：</h4>

                    <!-- 如果有项目开票类目选择，显示按类目汇总 -->
                    <div v-if="projectInvoiceData.length > 0 && getSelectedProjectCount() > 0">
                        <!-- 汇总表格样式 -->
                        <div class="summary-table-container">
                            <table class="summary-table">
                                <thead>
                                    <tr>
                                        <th class="category-header">开票类目</th>
                                        <th class="unit-header">单位</th>
                                        <th class="quantity-header">数量</th>
                                        <th class="price-header">单价</th>
                                        <th class="amount-header">金额</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="(categoryInfo, categoryName) in calculateCategorySummary()" :key="categoryName"
                                        class="summary-row"
                                        :class="{ 'deducted-row': categoryInfo.hasDeduction }">
                                        <td class="category-cell">
                                            {{ categoryName }}
                                            <span v-if="categoryInfo.hasDeduction" class="deduction-info" style="color: #E6A23C; font-size: 12px; display: block; font-weight: bold;">
                                                扣减前：￥{{ formatMoney(categoryInfo.originalAmount) }} → 扣减后：￥{{ formatMoney(categoryInfo.amount) }}
                                            </span>
                                        </td>
                                        <td class="unit-cell">项</td>
                                        <td class="quantity-cell">1</td>
                                        <td class="price-cell">{{ formatMoney(categoryInfo.amount) }}</td>
                                        <td class="amount-cell">{{ formatMoney(categoryInfo.amount) }}</td>
                                    </tr>
                                </tbody>
                                <tfoot>
                                    <tr class="total-row">
                                        <td class="total-label" colspan="4">合计</td>
                                        <td class="total-amount">{{ formatMoney(calculateCategorySummaryTotal()) }}</td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>

                        <!-- 统计信息 -->
                        <div style="margin-top: 10px; padding: 10px; background: #f5f7fa; border-radius: 4px; font-size: 13px; color: #606266;">
                            <span>共 {{ Object.keys(calculateCategorySummary()).length }} 个开票类目，</span>
                            <span>涉及 {{ getSelectedProjectCount() }} 个项目，</span>
                            <span>包含 {{ selectedRecords.length }} 条记录</span>
                        </div>

                        <!-- 负值扣减提示 -->
                        <div v-if="hasNegativeOtherProject()" style="margin-top: 10px;">
                            <el-alert
                                :title="checkNegativeAmountLimit().isValid ? '开票金额调整提示' : '开票金额超限提示'"
                                :type="checkNegativeAmountLimit().isValid ? 'warning' : 'error'"
                                show-icon
                                :closable="false">
                                <div style="line-height: 1.5;">
                                    <p style="margin: 0 0 8px 0; font-weight: bold;">
                                        检测到"其它"项目存在负值金额：<span style="color: #E6A23C;">￥{{ formatMoney(Math.abs(getNegativeOtherProjectAmount())) }}</span>
                                    </p>

                                    <div v-if="checkNegativeAmountLimit().isValid">
                                        <p style="margin: 0 0 8px 0; color: #909399;">
                                            系统已从金额最大的开票类目"<span style="color: #409EFF; font-weight: bold;">{{ getMaxAmountCategory().categoryName }}</span>"中自动扣减该负值金额。
                                        </p>
                                        <p style="margin: 0 0 8px 0; color: #909399;">
                                            扣减前：￥{{ formatMoney(getMaxAmountCategory().amount) }} → 扣减后：￥{{ formatMoney(getMaxAmountCategory().finalAmount) }}
                                        </p>
                                        <p style="margin: 0; color: #909399;">
                                            财务会根据实际情况确认扣减，最终开票金额以财务确认为准。
                                        </p>
                                    </div>

                                    <div v-else>
                                        <p style="margin: 0 0 8px 0; color: #F56C6C; font-weight: bold;">
                                            负值金额超过了所有开票类目的最大金额（￥{{ formatMoney(checkNegativeAmountLimit().maxCategoryAmount) }}），
                                            无法进行自动扣减！
                                        </p>
                                        <p style="margin: 0; color: #F56C6C;">
                                            请调整选择的记录或联系管理员处理负值票额问题。
                                        </p>
                                    </div>
                                </div>
                            </el-alert>
                        </div>

                        <!-- 如果还有未选择类目的项目，提示信息 -->
                        <div v-if="getSelectedProjectCount() < projectInvoiceData.length" style="margin-top: 10px;">
                            <el-alert
                                title="部分项目未选择开票类目"
                                :description="`还有 ${projectInvoiceData.length - getSelectedProjectCount()} 个项目未选择开票类目，请完成选择后查看完整汇总`"
                                type="warning"
                                :closable="false"
                                show-icon
                                style="margin-bottom: 15px;">
                            </el-alert>
                        </div>
                    </div>

                    <!-- 如果没有项目或未选择类目，显示提示信息 -->
                    <div v-else>
                        <el-alert
                            title="暂无开票类目选择"
                            description="请先为项目选择开票类目，然后查看按类目汇总的金额信息"
                            type="info"
                            :closable="false"
                            show-icon
                            style="margin-bottom: 15px;">
                        </el-alert>
                    </div>

                    <!-- 详细记录列表 -->
                    <el-collapse style="margin-top: 20px;">
                        <el-collapse-item title="查看详细记录列表" name="detail">
                            <div class="table-container">
                                <el-table
                                    :data="selectedRecords"
                                    style="width: 100%; color: rgba(0, 0, 0, 0.65)"
                                    header-row-class-name="table_header"
                                    header-cell-class-name="table_header_cell"
                                    cell-class-name="table_cell">
                                    <el-table-column prop="typeStr" label="业务类型" width="100" align="center">
                                        <template slot-scope="scope">
                                            <el-tag :type="getTypeTagType(scope.row.type)" size="small">
                                                {{ scope.row.typeStr }}
                                            </el-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="subjectId" label="业务ID" min-width="160" show-overflow-tooltip>
                                        <template slot-scope="scope">
                                            <span style="font-family: monospace; font-size: 12px;">{{ scope.row.subjectId }}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="projectName" label="项目名称" min-width="150" show-overflow-tooltip>
                                        <template slot-scope="scope">
                                            <span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: block;">{{ getProjectNameForRecord(scope.row) }}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="availableMoney" label="开票金额" min-width="120" align="right">
                                        <template slot-scope="scope">
                                            <span class="money" style="color: #409EFF; font-weight: bold;">￥{{ formatMoney(scope.row.availableMoney) }}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="createTime" label="创建时间" min-width="160">
                                        <template slot-scope="scope">
                                            <span style="font-size: 12px;">{{ formatTime(scope.row.createTime) }}</span>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </el-collapse-item>
                    </el-collapse>
                </div>
            </el-card>

            <!-- 开票申请表单 -->
            <el-card style="margin-top: 20px;" class="invoice-form-card">
                <div slot="header" class="clearfix">
                    <span style="font-size: 16px; font-weight: bold; color: #303133;">
                        <i class="el-icon-edit-outline" style="margin-right: 8px; color: #409EFF;"></i>
                        开票申请详情
                    </span>
                </div>

                <div class="form-container">
                    <el-form label-width="140px" :rules="rules" :model="form" ref="invoiceForm" class="invoice-form">
                        <!-- 基本信息区域 -->
                        <div class="form-section">
                            <h4 class="section-title">
                                <i class="el-icon-document" style="color: #409EFF; margin-right: 5px;"></i>
                                基本信息
                            </h4>

                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="发票类型：" prop="invoiceType">
                                        <el-select v-model="form.invoiceType" style="width: 100%" filterable>
                                            <el-option v-for="(item, index) in type" :key="index" :label="item.name" :value="item.id">
                                                <span style="float: left">{{ item.name }}</span>
                                                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.id }}</span>
                                            </el-option>
                                        </el-select>
                                        <div class="form-tip">
                                            <i class="el-icon-info" style="margin-right: 3px;"></i>
                                            默认选择电子专票
                                        </div>
                                    </el-form-item>
                                </el-col>

                                <el-col :span="12">
                                    <el-form-item label="审批人员：" prop="auditUserId">
                                        <el-select v-model="form.auditUserId" style="width: 100%" @change="changeInput">
                                            <el-option v-for="(item, index) in peopleList" :key="index" :label="item.name" :value="item.id">
                                                <span style="float: left">{{ item.name }}</span>
                                                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.id.substring(0, 8) }}...</span>
                                            </el-option>
                                        </el-select>
                                        <div class="form-tip">
                                            <i class="el-icon-user" style="margin-right: 3px;"></i>
                                            选择负责审批的人员
                                        </div>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>

                        <!-- 备注信息区域 -->
                        <div class="form-section">
                            <h4 class="section-title">
                                <i class="el-icon-edit" style="color: #E6A23C; margin-right: 5px;"></i>
                                备注信息
                            </h4>

                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="发票备注：">
                                        <el-input
                                            v-model="form.remark"
                                            type="textarea"
                                            placeholder="请输入发票备注信息..."
                                            show-word-limit
                                            maxlength="200"
                                            :autosize="{ minRows: 3, maxRows: 6 }"
                                            style="width: 100%"
                                        >
                                        </el-input>
                                        <div class="form-warning">
                                            <i class="el-icon-warning" style="margin-right: 3px;"></i>
                                            以上信息会加在发票右下角备注栏
                                        </div>
                                    </el-form-item>
                                </el-col>

                                <el-col :span="12">
                                    <el-form-item label="提醒事项：">
                                        <el-input
                                            v-model="form.tips"
                                            type="textarea"
                                            placeholder="可在此处给审核员留言..."
                                            show-word-limit
                                            maxlength="200"
                                            :autosize="{ minRows: 3, maxRows: 6 }"
                                            style="width: 100%"
                                        >
                                        </el-input>
                                        <div class="form-warning">
                                            <i class="el-icon-chat-line-square" style="margin-right: 3px;"></i>
                                            可在此处给审核员留言
                                        </div>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>

                        <!-- 文件上传区域 -->
                        <div class="form-section">
                            <h4 class="section-title">
                                <i class="el-icon-folder-add" style="color: #67C23A; margin-right: 5px;"></i>
                                附件上传
                            </h4>

                            <el-form-item label="单据打包上传：">
                                <div class="upload-container">
                                    <el-upload
                                        action=""
                                        :http-request="uploadURL"
                                        :multiple="false"
                                        :on-success="uploadSuccess"
                                        :on-error="uploadError"
                                        :on-remove="fileRemove"
                                        :limit="1"
                                        class="invoice-upload"
                                        :show-file-list="true">
                                        <div class="upload-dragger">
                                            <i class="el-icon-upload"></i>
                                            <div class="upload-text">点击上传</div>
                                        </div>
                                    </el-upload>
                                    <div class="upload-tips">
                                        <div class="form-tip">
                                            <i class="el-icon-info" style="margin-right: 3px;"></i>
                                            支持 ZIP/RAR 格式，单个文件不超过 4MB
                                        </div>
                                        <div class="form-tip" style="margin-top: 3px;">
                                            <i class="el-icon-document" style="margin-right: 3px;"></i>
                                            请上传相关单据的打包文件，便于审核员查看
                                        </div>
                                    </div>
                                </div>
                            </el-form-item>
                        </div>
                    </el-form>

                    <!-- 操作按钮区域 -->
                    <div class="form-actions">
                        <el-divider></el-divider>
                        <div class="button-group">
                            <el-button size="medium" @click="goToPrevStep">
                                <i class="el-icon-arrow-left" style="margin-right: 5px;"></i>
                                上一步
                            </el-button>
                            <el-button type="primary" size="medium" @click="toSubmitInfo">
                                <i class="el-icon-check" style="margin-right: 5px;"></i>
                                提交申请
                            </el-button>
                        </div>
                    </div>
                </div>
            </el-card>
            </div>
        </div>
    </div>
</template>

<script>
import { digitUppercase } from '@/utils/moneyChange.js';
import { auditPersonList,  ticketRecordSaveApply, getTypeInfoByWalletId, getByWalletIdAndStatusList } from '@/api/account/account.js';
import { getProjectInvoiceCategoryByIds } from '@/api/project/project.js';
import { getAuditPersonList } from '@/api/branch/enterprise.js';
import { client, getFileNameUUID, getTimeNow } from '@/utils/oss.js';
import { Message } from 'element-ui';
import { OSS_URL } from '@/api/config';
export default {

    data() {
        return {
            form: {
                money: '0.00'
            },
            walletInfo: {},
            moneyStr: '零元整',
            rules: {
                auditUserId: [{ required: true, message: '请选择审批人员', trigger: 'change' }],
                invoiceCategory: [{ required: true, message: '请选择开票类目', trigger: 'change' }],
                invoiceType: [{ required: true, message: '请选择发票类型', trigger: 'change' }]
            },
            peopleList: [],
            fileList: {},
            category: [],
            type: [],
            showTicket: 0,
            uploadSuccessed: false,
            // 钱包票额记录
            invoiceRecords: [], // 当前页显示的记录
            allInvoiceRecords: [], // 全部记录
            groupedRecords: {},
            loading: false,
            // 选择的记录
            selectedRecords: [],
            // 步骤控制
            currentStep: 1,
            // 各类型金额汇总
            typeSummary: {},
            // 分页相关
            pagination: {
                current: 1,
                size: 10,
                total: 0
            },
            // 防止选择事件循环调用的标志
            updatingSelection: false,
            // 标记是否正在翻页
            pageChanging: false,
            // 项目相关数据
            projectInvoiceData: [], // 项目开票类目数据
            loadingProject: false // 项目数据加载状态
        };
    },
    computed: {
        // 判断是否所有页面都已选择
        isAllPagesSelected() {
            if (this.allInvoiceRecords.length === 0) return false;
            return this.selectedRecords.length === this.allInvoiceRecords.length;
        }
    },

    created() {
        this.walletInfo = JSON.parse(this.$route.query.info);
        this.showTicket = (this.walletInfo.currentTicket - this.walletInfo.frozenTicket).toFixed(2);
        getAuditPersonList({
            type: 2,
            walletId: this.walletInfo.id
        }).then((res) => {
            if (res.data.code === 0) {
                this.peopleList = res.data.data;
                this.form.auditUserId = res.data.data[0].id
                //设置默认开票审核员
                this.peopleList.forEach((v,k) => {
                    if (v.name.indexOf("闹") > -1 || v.id == '33dcb151f7175d0e171e291200f2a91a') {
                        this.peopleList[k].name =  v.name + " (指定开票审核人)"
                        this.form.auditUserId = v.id;
                    }
                });
            }
        });
        getTypeInfoByWalletId({
            walletId: this.walletInfo.id
        }).then((res) => {
            this.category = res.data.data.category;
            this.type = res.data.data.type;

            // 设置默认发票类型
            this.setDefaultInvoiceType();
        });

        // 加载钱包票额记录
        this.loadInvoiceRecords();

        // 初始化开票金额
        this.form.money = '0.00';
        this.toWirteMoney();
    },
    methods: {
        // 加载钱包票额记录
        async loadInvoiceRecords(current = 1, resetSelection = true) {
            this.loading = true;
            try {
                // 只有在重置选择时才重新请求数据，否则使用缓存数据进行分页
                if (resetSelection || this.allInvoiceRecords.length === 0) {
                    // 使用新的API，一次性获取全部记录
                    const response = await getByWalletIdAndStatusList({
                        walletId: this.walletInfo.id,
                        statusList: [1, 2], // 未开票和部分开票
                    });

                    let allRecords = [];

                    if (response.data.code === 0) {
                        allRecords = response.data.data || [];
                    }

                    // 按创建时间倒序排列
                    allRecords.sort((a, b) => new Date(b.createTime) - new Date(a.createTime));

                    // 为每条记录添加类型描述和必选标记
                    allRecords.forEach(record => {
                        record.typeStr = this.getTypeDescription(record.type);
                        // 可用票额为负数的记录标记为必选
                        record.isRequired = parseFloat(record.availableMoney || 0) < 0;
                    });

                    this.allInvoiceRecords = allRecords;
                    this.pagination.total = allRecords.length;
                }

                // 前端分页处理
                this.pagination.current = current;
                const startIndex = (current - 1) * this.pagination.size;
                const endIndex = startIndex + this.pagination.size;
                this.invoiceRecords = this.allInvoiceRecords.slice(startIndex, endIndex);

                this.groupRecordsByType();

                // 如果需要重置选择状态（刷新时）
                if (resetSelection) {
                    this.clearAllSelections();
                    this.currentStep = 1;

                    // 自动选择必选记录（可用票额为负数的记录）
                    this.autoSelectRequiredRecords();
                } else {
                    // 分页时恢复选择状态
                    this.updateTableSelection();
                }

            } catch (error) {
                console.error('加载钱包票额记录失败：', error);
                this.$message.error('加载票额记录失败');
            } finally {
                this.loading = false;
            }
        },



        // 自动选择必选记录（可用票额为负数的记录）
        autoSelectRequiredRecords() {
            const requiredRecords = this.allInvoiceRecords.filter(record => record.isRequired);
            if (requiredRecords.length > 0) {
                this.selectedRecords = [...requiredRecords];
                this.calculateAndSetMoney();

                // 更新表格选择状态
                this.$nextTick(() => {
                    this.updateTableSelection();
                });

                console.log(`自动选择了 ${requiredRecords.length} 条必选记录（可用票额为负数）`);
                this.$message.info(`已自动选择 ${requiredRecords.length} 条必选记录（可用票额为负数），这些记录不可取消选择`);
            }
        },

        // 处理分页变化
        handlePageChange(currentPage) {
            // 设置翻页标志
            this.pageChanging = true;

            this.loadInvoiceRecords(currentPage, false);

            // 在下一个tick中重置翻页标志
            this.$nextTick(() => {
                setTimeout(() => {
                    this.pageChanging = false;
                }, 100); // 给一点延迟确保所有事件都已处理
            });
        },

        // 处理表格选择变化
        handleSelectionChange(selection) {
            // 如果正在翻页或正在更新选择状态，跳过处理
            if (this.pageChanging || this.updatingSelection) {
                return;
            }

            // 获取当前页面的记录唯一标识（使用id作为唯一标识）
            const currentPageRecordIds = this.invoiceRecords.map(record => record.id);

            // 移除当前页面的所有记录（之前选中的）
            this.selectedRecords = this.selectedRecords.filter(record =>
                !currentPageRecordIds.includes(record.id)
            );

            // 添加当前页面新选中的记录
            this.selectedRecords = [...this.selectedRecords, ...selection];

            // 确保必选记录始终被选中
            this.ensureRequiredRecordsSelected();

            // 去重处理（基于id）
            this.removeDuplicateSelections();

            this.calculateAndSetMoney();
        },

        // 处理全选/取消全选
        handleSelectAll(selection) {
            // 如果正在翻页或正在更新选择状态，跳过处理
            if (this.pageChanging || this.updatingSelection) {
                return;
            }

            // 获取当前页面的记录唯一标识（使用id作为唯一标识）
            const currentPageRecordIds = this.invoiceRecords.map(record => record.id);

            // 移除当前页面的所有记录
            this.selectedRecords = this.selectedRecords.filter(record =>
                !currentPageRecordIds.includes(record.id)
            );

            // 添加当前页面新选中的记录
            this.selectedRecords = [...this.selectedRecords, ...selection];

            // 确保必选记录始终被选中
            this.ensureRequiredRecordsSelected();

            // 去重处理（基于id）
            this.removeDuplicateSelections();

            this.calculateAndSetMoney();
        },

        // 确保必选记录始终被选中
        ensureRequiredRecordsSelected() {
            const requiredRecords = this.allInvoiceRecords.filter(record => record.isRequired);
            const selectedIds = new Set(this.selectedRecords.map(record => record.id));

            requiredRecords.forEach(record => {
                if (!selectedIds.has(record.id)) {
                    this.selectedRecords.push(record);
                }
            });
        },

        // 去重处理（基于id）
        removeDuplicateSelections() {
            const uniqueRecords = [];
            const seenIds = new Set();

            this.selectedRecords.forEach(record => {
                if (!seenIds.has(record.id)) {
                    seenIds.add(record.id);
                    uniqueRecords.push(record);
                }
            });

            this.selectedRecords = uniqueRecords;
        },

        // 跨分页操作方法

        // 全选所有页面的记录
        selectAllPages() {
            this.selectedRecords = [...this.allInvoiceRecords];
            this.calculateAndSetMoney();
            this.updateTableSelection();
            this.$message.success(`已选择所有 ${this.allInvoiceRecords.length} 项记录`);
        },

        // 清除所有页面的选择（但保留必选记录）
        clearAllPages() {
            const oldCount = this.selectedRecords.length;
            // 保留必选记录
            const requiredRecords = this.allInvoiceRecords.filter(record => record.isRequired);
            this.selectedRecords = [...requiredRecords];

            this.calculateAndSetMoney();
            this.updateTableSelection();

            const clearedCount = oldCount - requiredRecords.length;
            if (requiredRecords.length > 0) {
                this.$message.info(`已取消选择 ${clearedCount} 项记录，保留 ${requiredRecords.length} 项必选记录`);
            } else {
                this.$message.info(`已取消选择 ${oldCount} 项记录`);
            }
        },

        // 更新表格选择状态 - 在数据加载完成后恢复选择状态
        updateTableSelection() {
            this.$nextTick(() => {
                if (this.$refs.invoiceTable) {
                    // 设置标志防止循环调用
                    this.updatingSelection = true;

                    // 清除所有选择
                    this.$refs.invoiceTable.clearSelection();

                    // 恢复当前页中已选择的记录
                    this.invoiceRecords.forEach(record => {
                        const isSelected = this.selectedRecords.some(selected => selected.id === record.id);
                        if (isSelected) {
                            this.$refs.invoiceTable.toggleRowSelection(record, true);
                        }
                    });

                    // 重置标志
                    this.$nextTick(() => {
                        this.updatingSelection = false;
                    });
                }
            });
        },

        // 清除所有选择状态（但保留必选记录）
        clearAllSelections() {
            // 保留必选记录
            const requiredRecords = this.allInvoiceRecords.filter(record => record.isRequired);
            this.selectedRecords = [...requiredRecords];

            // 如果有必选记录，重新计算金额
            if (requiredRecords.length > 0) {
                this.calculateAndSetMoney();
            } else {
                this.form.money = '0.00';
                this.typeSummary = {};
                this.toWirteMoney();
            }

            this.pageChanging = false; // 重置翻页状态

            // 清除表格选择状态
            this.$nextTick(() => {
                if (this.$refs.invoiceTable) {
                    this.updatingSelection = true;
                    this.$refs.invoiceTable.clearSelection();
                    this.$nextTick(() => {
                        this.updatingSelection = false;
                    });
                }
            });
        },

        // 按业务类型分组记录
        groupRecordsByType() {
            const grouped = {};
            this.allInvoiceRecords.forEach(record => {
                const typeKey = record.type;
                if (!grouped[typeKey]) {
                    grouped[typeKey] = [];
                }
                grouped[typeKey].push(record);
            });
            this.groupedRecords = grouped;
        },

        // 获取记录的项目名称
        getProjectNameForRecord(record) {
            if (record.type === 1) {
                // type=1时，直接返回接口的projectName，为空就留空
                return record.projectName || '';
            } else {
                // 其它type直接返回业务类型名称
                return this.getTypeDescription(record.type);
            }
        },

        // 获取业务类型描述
        getTypeDescription(type) {
            const typeMap = {
                1: '发放批次',
                2: '票额调整',
                3: '钱包充值',
                9: '综合票额'
            };
            return typeMap[type] || '未知类型';
        },

        // 获取业务类型标签颜色
        getTypeTagType(type) {
            const typeMap = {
                1: 'success',    // 发放批次 - 绿色
                2: 'warning',    // 票额调整 - 橙色
                3: 'primary',    // 钱包充值 - 蓝色
                9: 'info'        // 综合票额 - 灰色
            };
            return typeMap[type] || '';
        },

        // 获取状态描述
        getStatusDescription(status) {
            const statusMap = {
                1: '未开票',
                2: '部分开票',
                3: '完成开票'
            };
            return statusMap[status] || '未知状态';
        },

        // 格式化时间
        formatTime(time) {
            if (!time) return '';
            return new Date(time).toLocaleString('zh-CN');
        },

        // 格式化金额
        formatMoney(money) {
            if (!money) return '0.00';
            return parseFloat(money).toFixed(2);
        },



        // 计算总金额
        calculateTotalAmount() {
            return this.selectedRecords.reduce((total, record) => {
                return total + parseFloat(record.availableMoney || 0);
            }, 0);
        },

        // 计算并设置开票金额
        calculateAndSetMoney() {
            let totalAmount;

            // 如果在第二步且有项目开票类目数据，使用类目汇总总额（已扣减负值）
            if (this.currentStep === 2 && this.projectInvoiceData.length > 0) {
                totalAmount = this.calculateCategorySummaryTotal();
            } else {
                // 第一步或没有项目数据时，使用所有选中记录的总额
                totalAmount = this.calculateTotalAmount();
            }

            this.form.money = totalAmount.toFixed(2);
            this.toWirteMoney();
            this.calculateTypeSummary();
        },

        // 计算各类型金额汇总
        calculateTypeSummary() {
            const summary = {};
            this.selectedRecords.forEach(record => {
                const type = record.type;
                if (!summary[type]) {
                    summary[type] = 0;
                }
                summary[type] += parseFloat(record.availableMoney || 0);
            });
            this.typeSummary = summary;
        },

        // 计算开票类目汇总
        calculateCategorySummary() {
            const categorySummary = {};

            this.projectInvoiceData.forEach(project => {
                if (project.selectedCategoryId) {
                    // 排除负值的"其它"项目
                    if (project.projectId === 'system_other_all' && project.totalAmount < 0) {
                        return; // 跳过负值的"其它"项目，不参与类目汇总
                    }

                    // 找到选择的开票类目
                    const selectedCategory = project.invoiceCategories.find(
                        cat => cat.invoiceCategoryId === project.selectedCategoryId
                    );

                    if (selectedCategory) {
                        const categoryName = selectedCategory.fullCategoryName;
                        const projectAmount = this.getProjectInvoiceAmount(project);

                        if (!categorySummary[categoryName]) {
                            categorySummary[categoryName] = {
                                amount: 0,
                                recordCount: 0,
                                projectCount: 0,
                                originalAmount: 0  // 记录原始金额，用于显示扣减前的金额
                            };
                        }

                        categorySummary[categoryName].amount += projectAmount;
                        categorySummary[categoryName].originalAmount += projectAmount;
                        categorySummary[categoryName].recordCount += this.getProjectRecordCount(project);
                        categorySummary[categoryName].projectCount += 1;
                    }
                }
            });

            // 处理负值扣减：从最大金额的类目中扣减
            const negativeOtherProject = this.projectInvoiceData.find(project =>
                project.projectId === 'system_other_all' && project.totalAmount < 0
            );

            if (negativeOtherProject && Object.keys(categorySummary).length > 0) {
                const negativeAmount = Math.abs(negativeOtherProject.totalAmount);

                // 找到金额最大的类目
                let maxCategoryName = '';
                let maxAmount = 0;

                Object.keys(categorySummary).forEach(categoryName => {
                    const amount = categorySummary[categoryName].amount;
                    if (amount > maxAmount) {
                        maxAmount = amount;
                        maxCategoryName = categoryName;
                    }
                });

                // 从最大金额的类目中扣减负值
                if (maxCategoryName && maxAmount >= negativeAmount) {
                    categorySummary[maxCategoryName].amount -= negativeAmount;
                    categorySummary[maxCategoryName].hasDeduction = true;
                    categorySummary[maxCategoryName].deductedAmount = negativeAmount;

                    console.log(`从类目"${maxCategoryName}"中扣减负值金额￥${negativeAmount.toFixed(2)}，扣减前：￥${maxAmount.toFixed(2)}，扣减后：￥${categorySummary[maxCategoryName].amount.toFixed(2)}`);
                }
            }

            return categorySummary;
        },

        // 计算开票类目汇总的总金额
        calculateCategorySummaryTotal() {
            const categorySummary = this.calculateCategorySummary();
            let total = 0;

            Object.keys(categorySummary).forEach(categoryName => {
                total += categorySummary[categoryName].amount;
            });

            return total;
        },

        // 获取负值的"其它"项目金额（用于显示扣减提示）
        getNegativeOtherProjectAmount() {
            const negativeOtherProject = this.projectInvoiceData.find(project =>
                project.projectId === 'system_other_all' && project.totalAmount < 0
            );

            return negativeOtherProject ? negativeOtherProject.totalAmount : 0;
        },

        // 检查是否有负值的"其它"项目
        hasNegativeOtherProject() {
            return this.projectInvoiceData.some(project =>
                project.projectId === 'system_other_all' && project.totalAmount < 0
            );
        },

        // 检查负值扣减是否超出限制
        checkNegativeAmountLimit() {
            const negativeOtherProject = this.projectInvoiceData.find(project =>
                project.projectId === 'system_other_all' && project.totalAmount < 0
            );

            if (!negativeOtherProject) {
                return { isValid: true, maxCategoryAmount: 0, negativeAmount: 0 };
            }

            const negativeAmount = Math.abs(negativeOtherProject.totalAmount);

            // 计算类目汇总但不包含扣减，以获取原始最大金额
            const categorySummary = {};

            this.projectInvoiceData.forEach(project => {
                if (project.selectedCategoryId) {
                    // 排除负值的"其它"项目
                    if (project.projectId === 'system_other_all' && project.totalAmount < 0) {
                        return;
                    }

                    const selectedCategory = project.invoiceCategories.find(
                        cat => cat.invoiceCategoryId === project.selectedCategoryId
                    );

                    if (selectedCategory) {
                        const categoryName = selectedCategory.fullCategoryName;
                        const projectAmount = this.getProjectInvoiceAmount(project);

                        if (!categorySummary[categoryName]) {
                            categorySummary[categoryName] = {
                                amount: 0
                            };
                        }

                        categorySummary[categoryName].amount += projectAmount;
                    }
                }
            });

            // 找到最大的开票类目原始金额
            let maxCategoryAmount = 0;
            Object.keys(categorySummary).forEach(categoryName => {
                const amount = categorySummary[categoryName].amount;
                if (amount > maxCategoryAmount) {
                    maxCategoryAmount = amount;
                }
            });

            const isValid = maxCategoryAmount >= negativeAmount;

            return {
                isValid,
                maxCategoryAmount,
                negativeAmount,
                canDeduct: isValid && maxCategoryAmount > 0
            };
        },

        // 获取最大金额的开票类目信息
        getMaxAmountCategory() {
            const categorySummary = this.calculateCategorySummary();
            let maxCategoryName = '';
            let maxOriginalAmount = 0;
            let hasDeduction = false;
            let deductedAmount = 0;

            Object.keys(categorySummary).forEach(categoryName => {
                const categoryInfo = categorySummary[categoryName];
                const originalAmount = categoryInfo.originalAmount || categoryInfo.amount;

                if (originalAmount > maxOriginalAmount) {
                    maxOriginalAmount = originalAmount;
                    maxCategoryName = categoryName;
                    hasDeduction = categoryInfo.hasDeduction || false;
                    deductedAmount = categoryInfo.deductedAmount || 0;
                }
            });

            return {
                categoryName: maxCategoryName,
                amount: maxOriginalAmount,
                hasDeduction: hasDeduction,
                deductedAmount: deductedAmount,
                finalAmount: maxOriginalAmount - deductedAmount
            };
        },

        // 获取指定类型的记录数量
        getTypeRecordCount(type) {
            return this.selectedRecords.filter(record => record.type === type).length;
        },

        // 前往下一步
        async goToNextStep() {
            if (this.selectedRecords.length === 0) {
                this.$message.error('请先选择需要开票的记录！');
                return;
            }

            // 检查总开票金额是否为负值
            const totalAmount = this.calculateTotalAmount();
            if (totalAmount < 0) {
                this.$message.error('总开票金额为负值，无法提交申请！请重新选择记录。');
                return;
            }

            // 获取发放批次类型的记录的subjectId
            const batchRecords = this.selectedRecords.filter(record => record.type === 1); // type=1表示发放批次

            // 初始化项目数据数组
            let allProjectData = [];

            if (batchRecords.length > 0) {
                // 提取发放批次记录的subjectId
                const salaryBillIds = batchRecords.map(record => record.subjectId);
                // 从钱包信息中获取taxesId
                const taxesId = this.walletInfo.taxesId;

                this.loadingProject = true;

                try {
                    // 调用新接口获取项目开票类目信息
                    const response = await getProjectInvoiceCategoryByIds({
                        salaryBillIds: salaryBillIds,
                        taxesId: taxesId
                    });

                    if (response.data.code === 0) {
                        allProjectData = response.data.data || [];

                        // 为每个项目添加对应的批次记录信息和金额计算
                        allProjectData.forEach(project => {
                            // 根据项目ID找到对应的批次记录
                            // 首先通过salaryBillIds匹配(如果接口返回了这个字段)
                            let projectBatchRecords = [];

                            if (project.salaryBillIds && project.salaryBillIds.length > 0) {
                                // 通过salaryBillIds匹配选中的批次记录
                                projectBatchRecords = batchRecords.filter(record =>
                                    project.salaryBillIds.includes(record.subjectId)
                                );
                            } else {
                                // 备用方案：通过projectId匹配(如果批次记录中有projectId字段)
                                projectBatchRecords = batchRecords.filter(record =>
                                    record.projectId === project.projectId
                                );
                            }

                            // 如果还是没有匹配到，尝试通过项目名称匹配
                            if (projectBatchRecords.length === 0) {
                                projectBatchRecords = batchRecords.filter(record =>
                                    record.projectName === project.projectName
                                );
                            }

                            // 计算该项目的总开票金额
                            project.totalInvoiceAmount = projectBatchRecords.reduce((sum, record) => {
                                return sum + parseFloat(record.availableMoney || 0);
                            }, 0);

                            // 保存关联的批次记录
                            project.batchRecords = projectBatchRecords;

                            console.log(`项目 "${project.projectName}" 匹配到 ${projectBatchRecords.length} 条记录，总金额: ${project.totalInvoiceAmount}`);
                        });

                        // 过滤掉没有关联记录的项目（但保留有记录的项目，无论金额正负）
                        allProjectData = allProjectData.filter(project => project.batchRecords && project.batchRecords.length > 0);

                        // 为负值金额的项目添加特殊标记
                        allProjectData.forEach(project => {
                            if (project.totalInvoiceAmount < 0) {
                                project.isNegativeAmount = true;
                                console.log(`项目 "${project.projectName}" 为负值金额项目: ${project.totalInvoiceAmount}，将禁用开票类目选择`);
                            }
                        });

                        // 找出所有已匹配的批次记录ID
                        const matchedRecordIds = new Set();
                        allProjectData.forEach(project => {
                            if (project.batchRecords) {
                                project.batchRecords.forEach(record => {
                                    matchedRecordIds.add(record.subjectId);
                                });
                            }
                        });

                        // 找出未匹配的发放批次记录
                        const unmatchedBatchRecords = batchRecords.filter(record =>
                            !matchedRecordIds.has(record.subjectId)
                        );

                        // 获取所有非发放批次记录
                        const nonBatchRecords = this.selectedRecords.filter(record => record.type !== 1);

                        console.log(`=== "其它"项目创建检查 ===`);
                        console.log(`总选中记录数: ${this.selectedRecords.length}`);
                        console.log(`发放批次记录数: ${batchRecords.length}`);
                        console.log(`已匹配的发放批次记录数: ${matchedRecordIds.size}`);
                        console.log(`未匹配的发放批次记录数: ${unmatchedBatchRecords.length}`);
                        console.log(`非发放批次记录数: ${nonBatchRecords.length}`);

                        if (unmatchedBatchRecords.length > 0) {
                            console.log('未匹配的发放批次记录详情:');
                            unmatchedBatchRecords.forEach(record => {
                                console.log(`  - ID: ${record.subjectId}, 类型: ${record.typeStr}, 金额: ${record.availableMoney}, 项目名: ${record.projectName || '无'}`);
                            });
                        }

                        if (nonBatchRecords.length > 0) {
                            console.log('非发放批次记录详情:');
                            nonBatchRecords.forEach(record => {
                                console.log(`  - ID: ${record.subjectId}, 类型: ${record.typeStr}, 金额: ${record.availableMoney}`);
                            });
                        }
                        console.log(`========================`);

                        // 将未匹配的发放批次记录添加到非发放批次记录中，统一归入"其它"项目
                        if (unmatchedBatchRecords.length > 0) {
                            console.log(`发现 ${unmatchedBatchRecords.length} 条未匹配的发放批次记录，将并入"其它"项目`);
                            // 将未匹配的批次记录添加到非批次记录数组中
                            const allNonProjectRecords = [...(this.selectedRecords.filter(record => record.type !== 1) || []), ...unmatchedBatchRecords];

                            console.log(`准备创建"其它"项目，总共包含 ${allNonProjectRecords.length} 条记录（${unmatchedBatchRecords.length} 条未匹配发放批次 + ${allNonProjectRecords.length - unmatchedBatchRecords.length} 条非发放批次）`);

                            // 如果有任何非项目记录（包括未匹配的发放批次），创建"其它"项目
                            if (allNonProjectRecords.length > 0) {
                                const systemProject = {
                                    projectId: 'system_other_all',
                                    projectName: '其它（含钱包充值、票额调整、综合票额等）',
                                    taxesId: this.walletInfo.taxesId,
                                    invoiceCategories: this.category.map(cat => ({
                                        invoiceCategoryId: cat.id,
                                        invoiceCategoryName: cat.name,
                                        parentCategoryName: null,
                                        fullCategoryName: cat.name
                                    })),
                                    selectedCategoryId: null,
                                    isSystemProject: true, // 标记为系统项目
                                    recordCount: allNonProjectRecords.length,
                                    totalAmount: allNonProjectRecords.reduce((sum, record) => sum + parseFloat(record.availableMoney || 0), 0),
                                    allOtherRecords: allNonProjectRecords, // 保存所有非项目记录
                                    unmatchedBatchCount: unmatchedBatchRecords.length,
                                    nonBatchCount: allNonProjectRecords.length - unmatchedBatchRecords.length
                                };

                                // 为负值金额的"其它"项目添加特殊标记
                                if (systemProject.totalAmount < 0) {
                                    systemProject.isNegativeAmount = true;
                                    console.log(`"其它"项目为负值金额项目: ${systemProject.totalAmount}，将禁用开票类目选择`);
                                }

                                allProjectData.push(systemProject);

                                console.log(`创建"其它"项目，包含 ${allNonProjectRecords.length} 条记录（其中 ${unmatchedBatchRecords.length} 条未匹配发放批次，${allNonProjectRecords.length - unmatchedBatchRecords.length} 条非发放批次），总金额: ${systemProject.totalAmount}`);
                            }
                        } else {
                            // 即使没有未匹配的发放批次，也检查是否有非发放批次记录需要归入"其它"
                            const nonBatchRecords = this.selectedRecords.filter(record => record.type !== 1);
                            console.log(`没有未匹配的发放批次记录，但检查是否有非发放批次记录: ${nonBatchRecords.length} 条`);

                            if (nonBatchRecords.length > 0) {
                                const systemProject = {
                                    projectId: 'system_other_all',
                                    projectName: '其它（含钱包充值、票额调整、综合票额等）',
                                    taxesId: this.walletInfo.taxesId,
                                    invoiceCategories: this.category.map(cat => ({
                                        invoiceCategoryId: cat.id,
                                        invoiceCategoryName: cat.name,
                                        parentCategoryName: null,
                                        fullCategoryName: cat.name
                                    })),
                                    selectedCategoryId: null,
                                    isSystemProject: true, // 标记为系统项目
                                    recordCount: nonBatchRecords.length,
                                    totalAmount: nonBatchRecords.reduce((sum, record) => sum + parseFloat(record.availableMoney || 0), 0),
                                    allOtherRecords: nonBatchRecords, // 保存所有非项目记录
                                    unmatchedBatchCount: 0,
                                    nonBatchCount: nonBatchRecords.length
                                };

                                // 为负值金额的"其它"项目添加特殊标记
                                if (systemProject.totalAmount < 0) {
                                    systemProject.isNegativeAmount = true;
                                    console.log(`"其它"项目为负值金额项目: ${systemProject.totalAmount}，将禁用开票类目选择`);
                                }

                                allProjectData.push(systemProject);

                                console.log(`创建"其它"项目，包含 ${nonBatchRecords.length} 条非发放批次记录，总金额: ${systemProject.totalAmount}`);
                            } else {
                                console.log(`没有非发放批次记录，不需要创建"其它"项目`);
                            }
                        }

                        console.log('项目开票类目数据：', allProjectData);
                    } else {
                        console.warn('获取项目开票类目失败：', response.data.msg);
                        this.$message.warning('获取项目开票类目信息失败，但不影响开票申请');
                        allProjectData = [];
                    }
                } catch (error) {
                    console.error('调用项目开票类目接口失败：', error);
                    this.$message.warning('获取项目开票类目信息失败，但不影响开票申请');
                    allProjectData = [];
                } finally {
                    this.loadingProject = false;
                }
            } else {
                // 如果没有发放批次记录，但有其它类型记录，也要创建"其它"项目
                const nonBatchRecords = this.selectedRecords.filter(record => record.type !== 1);
                console.log(`没有发放批次记录，检查是否有其它类型记录: ${nonBatchRecords.length} 条`);
                if (nonBatchRecords.length > 0) {
                    const systemProject = {
                        projectId: 'system_other_all',
                        projectName: '其它（含钱包充值、票额调整、综合票额等）',
                        taxesId: this.walletInfo.taxesId,
                        invoiceCategories: this.category.map(cat => ({
                            invoiceCategoryId: cat.id,
                            invoiceCategoryName: cat.name,
                            parentCategoryName: null,
                            fullCategoryName: cat.name
                        })),
                        selectedCategoryId: null,
                        isSystemProject: true, // 标记为系统项目
                        recordCount: nonBatchRecords.length,
                        totalAmount: nonBatchRecords.reduce((sum, record) => sum + parseFloat(record.availableMoney || 0), 0),
                        allOtherRecords: nonBatchRecords, // 保存所有非项目记录
                        unmatchedBatchCount: 0,
                        nonBatchCount: nonBatchRecords.length
                    };

                    // 为负值金额的"其它"项目添加特殊标记
                    if (systemProject.totalAmount < 0) {
                        systemProject.isNegativeAmount = true;
                        console.log(`"其它"项目为负值金额项目: ${systemProject.totalAmount}，将禁用开票类目选择`);
                    }

                    allProjectData.push(systemProject);

                    console.log(`创建"其它"项目，包含 ${nonBatchRecords.length} 条非发放批次记录，总金额: ${systemProject.totalAmount}`);
                } else {
                    console.log(`没有非发放批次记录，不创建"其它"项目`);
                }
            }

            // 对项目列表进行排序，将"其它"项目放在最后
            allProjectData.sort((a, b) => {
                // "其它"项目放在最后
                if (a.projectId === 'system_other_all') return 1;
                if (b.projectId === 'system_other_all') return -1;

                // 其它系统项目排在真实项目之后，但在"其它"之前
                if (a.isSystemProject && !b.isSystemProject) return 1;
                if (!a.isSystemProject && b.isSystemProject) return -1;

                // 同类型项目按项目名称排序
                return a.projectName.localeCompare(b.projectName, 'zh-CN');
            });

            this.projectInvoiceData = allProjectData;

            // 添加最终项目列表的调试信息
            console.log(`=== 最终项目列表 ===`);
            console.log(`项目总数: ${allProjectData.length}`);
            allProjectData.forEach((project, index) => {
                const amount = this.getProjectInvoiceAmount(project);
                console.log(`${index + 1}. 项目: ${project.projectName}`);
                console.log(`   - ID: ${project.projectId}`);
                console.log(`   - 金额: ${amount}`);
                console.log(`   - 系统项目: ${project.isSystemProject || false}`);
                console.log(`   - 负值项目: ${project.isNegativeAmount || false}`);
                if (project.projectId === 'system_other_all') {
                    console.log(`   - "其它"项目包含记录数: ${project.recordCount}`);
                }
            });
            console.log(`==================`);

            // 添加调试信息
            if (allProjectData.length > 0) {
                console.log('=== 项目金额匹配调试信息 ===');
                allProjectData.forEach(project => {
                    const amount = this.getProjectInvoiceAmount(project);
                    console.log(`项目: ${project.projectName}, 计算金额: ${amount}, 预计算金额: ${project.totalInvoiceAmount || project.totalAmount}`);
                    if (project.isSystemProject) {
                        console.log(`  - 系统项目类型: ${project.projectId}`);
                        if (project.projectId === 'system_other_all') {
                            console.log(`  - "其它"项目包含所有非正常项目记录`);
                            if (project.unmatchedBatchCount > 0) {
                                console.log(`    - 未匹配发放批次记录: ${project.unmatchedBatchCount} 条`);
                            }
                            if (project.nonBatchCount > 0) {
                                console.log(`    - 非发放批次记录: ${project.nonBatchCount} 条`);
                            }
                        }
                    }
                    if (project.batchRecords) {
                        console.log(`  - 关联记录数: ${project.batchRecords.length}`);
                        project.batchRecords.forEach(record => {
                            console.log(`    记录ID: ${record.subjectId}, 金额: ${record.availableMoney}, 项目名: ${record.projectName || '无'}`);
                        });
                    }
                });
                console.log('========================');
            }

            if (allProjectData.length > 0) {
                const realProjectCount = allProjectData.filter(p => !p.isSystemProject).length;
                const systemProjectCount = allProjectData.filter(p => p.isSystemProject).length;
                const otherProjectCount = allProjectData.filter(p => p.projectId === 'system_other_all').length;

                let message = '';
                if (realProjectCount > 0 && systemProjectCount > 0) {
                    let systemProjectDetails = [];
                    if (otherProjectCount > 0) {
                        systemProjectDetails.push('其它（含钱包充值、票额调整、综合票额等）');
                    }
                    const systemProjectText = systemProjectDetails.length > 0 ? `(${systemProjectDetails.join('、')})` : '';
                    message = `成功获取${realProjectCount}个项目和${systemProjectCount}个系统项目${systemProjectText}的开票类目信息`;
                } else if (realProjectCount > 0) {
                    message = `成功获取${realProjectCount}个项目的开票类目信息`;
                } else {
                    let systemProjectDetails = [];
                    if (otherProjectCount > 0) {
                        systemProjectDetails.push('其它（含钱包充值、票额调整、综合票额等）');
                    }
                    const systemProjectText = systemProjectDetails.length > 0 ? `(${systemProjectDetails.join('、')})` : '';
                    message = `成功获取${systemProjectCount}个系统项目${systemProjectText}的开票类目信息`;
                }
                this.$message.success(message);
            } else {
                console.log('选择的记录中没有需要项目开票类目选择的类型');
            }

            this.currentStep = 2;

            // 设置默认发票类型为电子专票
            this.setDefaultInvoiceType();

            // 页面滚动到顶部 - 确保滚动到真正的页面顶部
            this.$nextTick(() => {
                setTimeout(() => {
                    // 方法1：强制滚动到页面最顶部
                    try {
                        window.scrollTo(0, 0);
                        document.documentElement.scrollTop = 0;
                        document.body.scrollTop = 0;

                        // 处理可能的滚动容器
                        const scrollContainers = document.querySelectorAll('.el-main, .main-container, .app-main, .content-wrapper');
                        scrollContainers.forEach(container => {
                            if (container) {
                                container.scrollTop = 0;
                            }
                        });

                        // 方法2：尝试滚动到步骤指示器位置
                        const stepsElement = document.querySelector('.el-steps');
                        if (stepsElement) {
                            stepsElement.scrollIntoView({
                                behavior: 'instant',
                                block: 'start',
                                inline: 'nearest'
                            });
                        }
                    } catch (error) {
                        // 忽略滚动处理异常
                    }
                }, 50);
            });
        },

        // 返回上一步
        goToPrevStep() {
            this.currentStep = 1;
            // 页面滚动到顶部
            this.$nextTick(() => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });

                // 恢复表格选择状态
                this.updateTableSelection();
            });
        },

        // 设置默认发票类型为电子专票
        setDefaultInvoiceType() {
            if (this.type && this.type.length > 0) {
                // 查找电子专票类型（通常包含"电子"、"专票"等关键词）
                const electronicType = this.type.find(t =>
                    t.name && (t.name.includes('电子') && t.name.includes('专'))
                );
                if (electronicType) {
                    this.form.invoiceType = electronicType.id;
                } else {
                    // 如果找不到电子专票，设置为第一个类型
                    this.form.invoiceType = this.type[0].id;
                }
            }
        },

        toWirteMoney() {
            this.moneyStr = digitUppercase(this.form.money);
        },
        handleBeforeUpload(file) {
            const isJPEG = file.name.split('.')[1] === 'zip';
            const isJPG = file.name.split('.')[1] === 'rar';
            const isPNG = file.name.split('.')[1] === 'png';
            if (!isJPG && !isJPEG && !isPNG && !isWEBP && !isGIF) {
                this.$message.error('上传图片只能是 zip/rar 格式!');
            }
            if (!isLt500K) {
                this.$message.error('单张图片大小不能超过 4mb!');
            }
            return (isJPEG || isJPG || isPNG || isWEBP || isGIF) && isLt500K;
        },
        uploadSuccess(res) {
            this.uploadSuccessed = true;
            this.$message.success('上传成功！');

            let parentElements = document.querySelectorAll('.el-upload-list__item');
            parentElements.forEach((parentElement) => {
                let thumbnailImg = parentElement.querySelector('.el-upload-list__item-thumbnail');
                if (res.name.split('.')[1] === "pdf" && thumbnailImg) {
                   thumbnailImg.src = require('@/assets/img/misc/thumbnail_invoice.png');
                }
            });
        },
        uploadError(err) {
            this.uploadSuccessed = false;
            this.$message.error('上传失败！请重试');
        },
        fileRemove(file, uploadFiles) {
            this.uploadSuccessed = false;
        },
        uploadURL(option) {
            this.uploadSuccessed = false;
            var suffix = option.file.name.substring(option.file.name.lastIndexOf('.'));
            //注意哦，这里指定文件夹'image/'，尝试过写在配置文件，但是各种不行，写在这里就可以
            var fileName = '/ticket/' + getTimeNow() + '/' + getFileNameUUID() + suffix;
            //定义唯一的文件名，打印出来的uid其实就是时间戳
            client()
                .multipartUpload(fileName, option.file, {
                    progress: function (percentage, cpt) {}
                })
                .then((res) => {
                    if(res.res.status === 200) {
                        this.fileList = {
                            name: option.file.name,
                            url: fileName
                        };
                        option.onSuccess(res);
                    }else {
                        option.onError(res);
                    }
                }).catch((err) => {
                    option.onError(err);
                });
        },
        toSubmitInfo() {
            // 验证表单
            this.$refs.invoiceForm.validate((valid) => {
                if (!valid) {
                    this.$message.error('请完善必填信息！');
                    return false;
                }

                // 验证是否有选择记录
                if (this.selectedRecords.length === 0) {
                    this.$message.error('请先选择需要开票的记录！');
                    this.currentStep = 1; // 返回第一步
                    return false;
                }

                // 验证开票金额
                const totalAmount = this.calculateTotalAmount();
                if (totalAmount <= 0) {
                    this.$message.error('开票金额必须大于0元！');
                    return false;
                }

                // 验证项目开票类目选择
                if (this.projectInvoiceData.length > 0) {
                    // 排除负值的"其它"项目，这些项目不需要选择开票类目
                    const unselectedProjects = this.projectInvoiceData.filter(project =>
                        !project.selectedCategoryId &&
                        !(project.projectId === 'system_other_all' && project.totalAmount < 0)
                    );
                    if (unselectedProjects.length > 0) {
                        const projectNames = unselectedProjects.map(p => p.projectName).join('、');
                        this.$message.error(`请为以下项目选择开票类目：${projectNames}`);
                        return false;
                    }
                }

                // 验证负值扣减限制
                if (this.hasNegativeOtherProject()) {
                    const limitCheck = this.checkNegativeAmountLimit();
                    if (!limitCheck.isValid) {
                        this.$message.error(`负值金额￥${this.formatMoney(limitCheck.negativeAmount)}超过了所有开票类目的最大金额￥${this.formatMoney(limitCheck.maxCategoryAmount)}，无法提交申请！请调整选择的记录。`);
                        return false;
                    }
                }

                this.submitApplication();
            });
        },

        // 提交申请
        submitApplication() {
            const confirmText = this.uploadSuccessed? '确认提交申请?' : '未提供单据，确认提交申请?';
            this.$confirm(confirmText, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then((res) => {
                // 构建项目开票类目数据
                const projectInvoiceCategories = this.buildProjectInvoiceCategoryData();

                // 构建开票类目金额列表
                const invoiceCategoryAmounts = this.buildInvoiceCategoryAmountList();

                // 构建提交数据
                const submitData = {
                    file: this.fileList,
                    walletId: this.walletInfo.id,
                    projectInvoiceCategories: projectInvoiceCategories, // 按项目分组的开票类目数据
                    invoiceCategoryAmounts: invoiceCategoryAmounts, // 开票类目与用户编辑金额列表
                    ...this.form
                };

                // 调试输出
                console.log('=== 开票申请提交数据 ===');
                console.log('总开票金额:', this.form.money);
                console.log('选择记录数量:', this.selectedRecords.length);
                console.log('项目开票类目数量:', projectInvoiceCategories.length);
                console.log('开票类目金额列表:', invoiceCategoryAmounts);
                console.log('项目开票类目详情:', projectInvoiceCategories);
                console.log('完整提交数据:', submitData);
                console.log('========================');

                ticketRecordSaveApply(submitData).then((res) => {
                    if (res.data.code === 0) {
                        Message({
                            message: '提交成功',
                            type: 'success'
                        });

                        // 清理选择状态和重置步骤
                        this.clearAllSelections();
                        this.currentStep = 1;

                        setTimeout(() => {
                            this.$router.replace('/account/invoicing');
                        }, 1300);
                    } else {
                        Message.error(res.data.msg);
                    }
                });
            });
        },

        // 构建开票类目金额列表
        buildInvoiceCategoryAmountList() {
            const categoryAmountList = [];

            // 直接从开票预览的类目汇总中获取数据
            const categorySummary = this.calculateCategorySummary();

            Object.keys(categorySummary).forEach(categoryName => {
                const categoryData = categorySummary[categoryName];
                const editedAmount = categoryData.editableAmount || 0;

                // 找到对应的类目ID（从第一个选择了该类目的项目中获取）
                let categoryId = null;
                let categoryInfo = null;

                for (const project of this.projectInvoiceData) {
                    if (project.selectedCategoryId) {
                        const selectedCategory = project.invoiceCategories.find(
                            cat => cat.invoiceCategoryId === project.selectedCategoryId
                        );

                        if (selectedCategory && selectedCategory.fullCategoryName === categoryName) {
                            categoryId = project.selectedCategoryId;
                            categoryInfo = selectedCategory;
                            break;
                        }
                    }
                }

                if (categoryId && categoryInfo) {
                    categoryAmountList.push({
                        invoiceCategoryId: categoryId,
                        invoiceCategoryName: categoryInfo.invoiceCategoryName || categoryInfo.fullCategoryName,
                        fullCategoryName: categoryName,
                        parentCategoryName: categoryInfo.parentCategoryName || null,
                        editedAmount: editedAmount,
                        originalAmount: parseFloat((categoryData.amount || 0).toFixed(2))
                    });
                }
            });

            return categoryAmountList;
        },

        // 构建项目开票类目数据
        buildProjectInvoiceCategoryData() {
            const projectCategories = [];

            this.projectInvoiceData.forEach(project => {
                // 找到选择的开票类目详细信息（如果有的话）
                const selectedCategory = project.selectedCategoryId ?
                    project.invoiceCategories.find(cat => cat.invoiceCategoryId === project.selectedCategoryId) :
                    null;

                const invoiceAmount = this.getProjectInvoiceAmount(project);
                const recordCount = this.getProjectRecordCount(project);

                const projectCategoryData = {
                    projectId: project.projectId,
                    projectName: project.projectName,
                    isSystemProject: project.isSystemProject || false,
                    // 开票类目信息 - 有就填，没有就为空
                    invoiceCategoryId: project.selectedCategoryId || null,
                    invoiceCategoryName: selectedCategory ? (selectedCategory.invoiceCategoryName || selectedCategory.fullCategoryName) : null,
                    fullCategoryName: selectedCategory ? selectedCategory.fullCategoryName : null,
                    parentCategoryName: selectedCategory ? (selectedCategory.parentCategoryName || null) : null,
                    // 金额和记录信息
                    invoiceAmount: parseFloat(invoiceAmount.toFixed(2)),
                    recordCount: recordCount,
                    // 关联的票额记录
                    relatedRecords: []
                };

                // 标记负值项目
                if (project.projectId === 'system_other_all' && project.totalAmount < 0) {
                    projectCategoryData.isNegativeAmount = true;
                }

                // 获取该项目关联的票额记录
                if (project.isSystemProject && project.projectId === 'system_other_all') {
                    // 系统"其它"项目：包含所有非项目记录
                    projectCategoryData.systemProjectType = 'other';
                    projectCategoryData.unmatchedBatchCount = project.unmatchedBatchCount || 0;
                    projectCategoryData.nonBatchCount = project.nonBatchCount || 0;

                    // 添加所有非发放批次记录 + 未匹配的发放批次记录
                    const nonBatchRecords = this.selectedRecords.filter(record => record.type !== 1);
                    const unmatchedBatchRecords = this.selectedRecords.filter(record => {
                        if (record.type !== 1) return false;
                        // 检查是否被其它真实项目匹配
                        return !this.projectInvoiceData.some(p =>
                            !p.isSystemProject && p.batchRecords &&
                            p.batchRecords.some(br => br.subjectId === record.subjectId)
                        );
                    });

                    projectCategoryData.relatedRecords = [
                        ...nonBatchRecords.map(record => ({
                            id: record.id,
                            subjectId: record.subjectId,
                            type: record.type,
                            typeStr: record.typeStr,
                            availableMoney: parseFloat(record.availableMoney || 0),
                            projectName: record.projectName || ''
                        })),
                        ...unmatchedBatchRecords.map(record => ({
                            id: record.id,
                            subjectId: record.subjectId,
                            type: record.type,
                            typeStr: record.typeStr,
                            availableMoney: parseFloat(record.availableMoney || 0),
                            projectName: record.projectName || '',
                            isUnmatchedBatch: true
                        }))
                    ];
                } else if (!project.isSystemProject && project.batchRecords) {
                    // 真实项目：包含匹配的发放批次记录
                    projectCategoryData.relatedRecords = project.batchRecords.map(record => ({
                        id: record.id,
                        subjectId: record.subjectId,
                        type: record.type,
                        typeStr: record.typeStr,
                        availableMoney: parseFloat(record.availableMoney || 0),
                        projectName: record.projectName || ''
                    }));
                }

                projectCategories.push(projectCategoryData);

                console.log(`构建项目数据 - 项目: ${project.projectName}, 类目: ${selectedCategory ? selectedCategory.fullCategoryName : '未选择'}, 金额: ${invoiceAmount}, 记录数: ${recordCount}`);
            });

            return projectCategories;
        },
        changeInput(){
            this.$forceUpdate()
        },

        // 计算项目对应的开票金额总和
        getProjectInvoiceAmount(project) {
            if (project.isSystemProject) {
                // 系统项目直接返回已计算好的总金额
                if (project.projectId === 'system_other_all') {
                    return project.totalAmount || 0;
                }
                return project.totalAmount || project.totalInvoiceAmount || 0;
            }

            // 优先使用项目中预计算的总金额（包括负值）
            if (project.totalInvoiceAmount !== undefined) {
                return project.totalInvoiceAmount;
            }

            // 备用方案：通过项目关联的批次记录计算
            if (project.batchRecords && project.batchRecords.length > 0) {
                return project.batchRecords.reduce((total, record) => {
                    return total + parseFloat(record.availableMoney || 0);
                }, 0);
            }

            // 最后备用方案：根据项目信息匹配选中记录中的发放批次
            const projectRecords = this.selectedRecords.filter(record => {
                if (record.type !== 1) return false; // 只处理发放批次类型

                // 多种匹配方式
                return record.projectId === project.projectId ||
                       record.projectName === project.projectName ||
                       (project.salaryBillIds && project.salaryBillIds.includes(record.subjectId));
            });

            return projectRecords.reduce((total, record) => {
                return total + parseFloat(record.availableMoney || 0);
            }, 0);
        },

        // 获取项目对应的记录数量
        getProjectRecordCount(project) {
            if (project.isSystemProject) {
                // 系统项目返回已计算好的记录数量
                if (project.projectId === 'system_other_all') {
                    return project.recordCount || 0;
                }
                return project.recordCount || (project.batchRecords ? project.batchRecords.length : 0);
            }

            // 优先使用项目中预计算的记录数量
            if (project.batchRecords && project.batchRecords.length > 0) {
                return project.batchRecords.length;
            }

            // 备用方案：根据项目信息匹配选中记录中的发放批次
            const projectRecords = this.selectedRecords.filter(record => {
                if (record.type !== 1) return false; // 只处理发放批次类型

                // 多种匹配方式
                return record.projectId === project.projectId ||
                       record.projectName === project.projectName ||
                       (project.salaryBillIds && project.salaryBillIds.includes(record.subjectId));
            });

            return projectRecords.length;
        },

        // 项目相关方法

        // 获取已选择的项目数量（包括负值项目）
        getSelectedProjectCount() {
            return this.projectInvoiceData.filter(project => project.selectedCategoryId || project.isNegativeAmount).length;
        },

        // 获取需要选择开票类目的项目总数（不包括负值项目）
        getSelectableProjectCount() {
            return this.projectInvoiceData.filter(project => !project.isNegativeAmount).length;
        },

        // 获取实际已选择开票类目的项目数量
        getActualSelectedProjectCount() {
            return this.projectInvoiceData.filter(project => project.selectedCategoryId).length;
        },

        // 获取负值项目数量
        getNegativeProjectCount() {
            return this.projectInvoiceData.filter(project => project.isNegativeAmount).length;
        },

        // 获取选择状态对应的标签类型
        getProjectSelectionProgressType() {
            const selectedCount = this.getSelectedProjectCount();
            if (selectedCount === 0) {
                return 'info';
            } else if (selectedCount === this.projectInvoiceData.length) {
                return 'success';
            } else {
                return 'warning';
            }
        },

        // 项目开票类目变化处理
        onProjectCategoryChange(project, categoryId) {
            // 记录项目原来选择的类目（如果有的话）
            const oldCategoryId = project.selectedCategoryId;
            let oldCategoryName = null;

            if (oldCategoryId) {
                const oldCategory = project.invoiceCategories.find(cat => cat.invoiceCategoryId === oldCategoryId);
                if (oldCategory) {
                    oldCategoryName = oldCategory.fullCategoryName;
                }
            }

            // 更新项目的选择的开票类目ID
            project.selectedCategoryId = categoryId;

            // 找到新选择的类目信息
            let newCategoryName = null;
            if (categoryId) {
                const selectedCategory = project.invoiceCategories.find(cat => cat.invoiceCategoryId === categoryId);
                if (selectedCategory) {
                    newCategoryName = selectedCategory.fullCategoryName;
                    console.log(`项目 "${project.projectName}" 选择了开票类目: ${selectedCategory.fullCategoryName} (ID: ${categoryId})`);
                }
            } else {
                console.log(`项目 "${project.projectName}" 清除了开票类目选择`);
            }

            // 重新计算开票预览
            this.recalculateInvoicePreview();
        },

        // 重新计算开票预览
        recalculateInvoicePreview() {
            // 重新计算开票金额
            this.calculateAndSetMoney();

            console.log('开票预览重新计算完成');
        },

        // 重置项目选择
        resetProjectSelections() {
            this.projectInvoiceData.forEach(project => {
                project.selectedCategoryId = null;
            });

            this.$message.info('已重置所有项目的开票类目选择');
        },

        // 检查记录是否可选择（必选记录不允许取消选择）
        checkSelectable(row, index) {
            // 必选记录（可用票额为负数的记录）不可取消选择
            return !row.isRequired;
        },

        // 获取必选记录数量
        getRequiredRecordCount() {
            return this.allInvoiceRecords.filter(record => record.isRequired).length;
        },

        // 获取表格行的样式类名
        getRowClassName({row, rowIndex}) {
            return row.isRequired ? 'required-row' : '';
        }
    }
};
</script>

<style scoped>
/* 汇总表格样式 */
.summary-table-container {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;
    background: #fff;
}

.summary-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
    color: #606266;
}

.summary-table thead {
    background: #f5f7fa;
}

.summary-table th {
    padding: 12px 8px;
    text-align: center;
    font-weight: 600;
    border-bottom: 1px solid #e4e7ed;
    border-right: 1px solid #e4e7ed;
    color: #303133;
}

.summary-table th:last-child {
    border-right: none;
}

.summary-table .category-header {
    width: 35%;
    text-align: left;
}

.summary-table .unit-header {
    width: 10%;
}

.summary-table .quantity-header {
    width: 15%;
}

.summary-table .price-header {
    width: 20%;
}

.summary-table .amount-header {
    width: 20%;
}

.summary-table tbody tr {
    transition: background-color 0.3s;
}

.summary-table tbody tr:hover {
    background: #f5f7fa;
}

/* 被扣减行的样式 */
.summary-table tbody tr.deducted-row {
    background: #fff7e6;
    border-left: 3px solid #E6A23C;
}

.summary-table tbody tr.deducted-row:hover {
    background: #fef5e7;
}

.summary-table td {
    padding: 8px;
    border-bottom: 1px solid #e4e7ed;
    border-right: 1px solid #e4e7ed;
}

.summary-table td:last-child {
    border-right: none;
}

.summary-table .category-cell {
    text-align: left;
    font-weight: 500;
    color: #303133;
    padding: 12px 8px;
}

.summary-table .unit-cell {
    text-align: center;
    padding: 12px 8px;
}

.summary-table .quantity-cell {
    text-align: center;
    font-weight: 500;
    padding: 12px 8px;
}

.summary-table .price-cell {
    text-align: right;
    font-family: 'Courier New', monospace;
    color: #409EFF;
    padding: 12px 8px;
}

.summary-table .amount-cell {
    text-align: right;
    padding: 6px;
}

/* 金额输入框样式 */
.summary-table .amount-cell .el-input {
    font-family: 'Courier New', monospace;
}

.summary-table .amount-cell .el-input__inner {
    text-align: right;
    font-weight: 600;
    color: #E6A23C;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    font-size: 13px;
    padding-right: 8px;
}

.summary-table .amount-cell .el-input__inner:focus {
    border-color: #409EFF;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.summary-table .amount-cell .el-input-group__prepend {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 500;
    border: 1px solid #dcdfe6;
    border-right: none;
    padding: 0 8px;
}

.summary-table tfoot {
    background: #fafbfc;
}

.summary-table .total-row td {
    border-bottom: none;
    font-weight: 600;
    color: #303133;
}

.summary-table .total-label {
    text-align: center;
    background: #f0f2f5;
}

.summary-table .total-amount {
    text-align: right;
    font-size: 14px;
    color: #E6A23C;
    font-weight: bold;
    font-family: 'Courier New', monospace;
    background: #f0f2f5;
}

.upload_box {
    width: 80%;
    display: flex;
    flex-wrap: wrap;
}
.icon_size {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    color: #fff;
    font-size: 18px;
    opacity: 0;
    transition: 0.4s;
}
.pic_list {
    position: relative;
    width: 146px;
    height: 146px;
    margin-right: 15px;
    margin-bottom: 15px;
}
.pic_list:hover .icon_size {
    opacity: 1;
}

/* 票额记录表格样式 */
.invoice-record-card {
    width: 100%;
}

.table-container {
    width: 100%;
    overflow-x: auto;
}

.table-container .el-table {
    width: 100% !important;
    min-width: 100%;
}

.table-container .el-table__body-wrapper {
    overflow-x: auto;
}

/* 确保表格在小屏幕上也能正常显示 */
@media (max-width: 768px) {
    .table-container .el-table {
        min-width: 800px;
    }
}

/* 分页样式 */
.pagination {
    text-align: center;
    margin-top: 20px;
}

/* 金额样式 */
.money {
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-weight: 500;
}

/* 跨分页操作样式 */
.cross-page-actions {
    margin-bottom: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e4e7ed;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.action-buttons {
    display: flex;
    align-items: center;
}

.page-info {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.info-text {
    color: #909399;
    font-size: 12px;
    white-space: nowrap;
}

/* 项目开票类目选择样式 */
.project-invoice-list {
    margin-top: 10px;
}

.project-item {
    padding: 10px;
    background: #fdfdfd;
    border: 1px solid #ebedf0;
    border-radius: 4px;
    margin-bottom: 10px;
}

.project-info {
    display: flex;
    align-items: center;
}

.project-name {
    font-weight: bold;
    color: #303133;
}

.project-meta {
    margin-top: 5px;
}

.category-selector {
    display: flex;
    align-items: center;
}

.category-selector .el-select {
    flex: 1;
}

.category-selector .el-button {
    margin-left: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .cross-page-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .action-buttons {
        margin-bottom: 10px;
    }

    .page-info {
        justify-content: center;
    }
}

/* 必选记录样式 */
.el-table tbody .el-table__row.required-row {
    background-color: #fef0f0 !important;
}

.el-table tbody .el-table__row.required-row:hover {
    background-color: #fde2e2 !important;
}

/* 开票申请表单样式 */
.invoice-form-card {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
}

.form-container {
    background: #fafbfc;
    border-radius: 6px;
    padding: 20px;
}

.invoice-form {
    background: white;
    border-radius: 6px;
    padding: 25px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.section-title {
    margin: 0 0 20px 0;
    font-size: 15px;
    font-weight: 600;
    color: #303133;
    display: flex;
    align-items: center;
    padding-bottom: 8px;
    border-bottom: 2px solid #f0f0f0;
}

.form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
    display: flex;
    align-items: center;
    line-height: 1.4;
}

.form-warning {
    font-size: 12px;
    color: #F56C6C;
    margin-top: 5px;
    display: flex;
    align-items: center;
    line-height: 1.4;
}

.upload-container {
    display: flex;
    align-items: flex-start;
    gap: 20px;
}

.invoice-upload {
    flex-shrink: 0;
}

.invoice-upload .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 148px;
    height: 148px;
    transition: border-color 0.3s;
}

.invoice-upload .el-upload:hover {
    border-color: #409EFF;
}

.upload-dragger {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.upload-dragger .el-icon-upload {
    font-size: 28px;
    color: #8c939d;
    margin-bottom: 8px;
    line-height: 1;
}

.upload-dragger .upload-text {
    font-size: 14px;
    color: #8c939d;
    margin: 0;
    line-height: 1;
}

.upload-tips {
    flex: 1;
    padding-top: 10px;
}

.form-actions {
    margin-top: 20px;
    background: white;
    border-radius: 6px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.button-group {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 10px;
}

/* 表单项样式优化 */
.invoice-form .el-form-item {
    margin-bottom: 20px;
}

.invoice-form .el-form-item__label {
    font-weight: 600;
    color: #303133;
}

.invoice-form .el-input__inner,
.invoice-form .el-textarea__inner,
.invoice-form .el-select .el-input__inner {
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.invoice-form .el-input:hover .el-input__inner,
.invoice-form .el-textarea:hover .el-textarea__inner,
.invoice-form .el-select:hover .el-input__inner {
    border-color: #c0c4cc;
}

.invoice-form .el-input__inner:focus,
.invoice-form .el-textarea__inner:focus,
.invoice-form .el-select .el-input.is-focus .el-input__inner {
    border-color: #409EFF;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .form-container {
        padding: 15px;
    }

    .invoice-form {
        padding: 20px;
    }

    .upload-container {
        flex-direction: column;
        gap: 15px;
    }

    .button-group {
        flex-direction: column;
        align-items: center;
    }

    .button-group .el-button {
        width: 100%;
        max-width: 200px;
    }
}

@media (max-width: 992px) {
    .invoice-form .el-col {
        margin-bottom: 10px;
    }
}
</style>
