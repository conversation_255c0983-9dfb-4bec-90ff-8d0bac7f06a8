<template>
    <div>
        <el-form label-width="150px" v-if="showId == 1" :rules="rules" ref="form" :model="form">
            <el-form-item label="企业简称：" prop="abbrName">
                <el-input placeholder="请输入企业简称" v-model="form.abbrName" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="企业全称：" prop="name">
                <el-input placeholder="请输入企业全称" v-model="form.name" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="负责人：">
                <el-input placeholder="请输入负责人" v-model="form.contacts" style="width: 250px"></el-input>
            </el-form-item>
        </el-form>
        <el-form label-width="150px" v-if="showId == 2">
            <el-form-item label="纳税识别号：">
                <el-input placeholder="纳税识别号" v-model="form.taxIdentifyNumber" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="企业类型：">
                <el-select v-model="form.companyType" style="width: 250px">
                    <el-option :value="0" label="一般纳税人"></el-option>
                    <el-option :value="1" label="小规模纳税人"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="企业地址：">
                <el-input placeholder="请输入企业地址" v-model="form.address" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="开户银行：">
                <el-input placeholder="请输入开户行" v-model="form.bankName" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="银行账号：">
                <el-input placeholder="请输入银行账户" v-model="form.accountNumber" style="width: 250px"></el-input>
            </el-form-item>
        </el-form>
        <el-form label-width="150px" v-if="showId == 3">
            <el-form-item label="收件人：">
                <el-input placeholder="请输入收件人" v-model="form.consignee" style="width: 250px"></el-input>
            </el-form-item>
            <el-form-item label="邮寄地址：">
                <el-input placeholder="请输入企业邮寄地址" v-model="form.consigneeAddress" style="width: 250px"></el-input>
            </el-form-item>
        </el-form>
        <el-form label-width="150px" v-if="showId == 4">
            <el-form-item label="营业执照：">
                <el-upload 
                    action="" 
                    :http-request="uploadURL" 
                    :on-success="uploadSuccess"
                    :on-error="uploadError"
                    :multiple="true" 
                    :limit="1" 
                    drag
                >
                    <i class="el-icon-upload"></i>
                </el-upload>
            </el-form-item>
            <el-form-item label="企业合同：">
                <el-upload
                    action=""
                    :http-request="uploadURLLD"
                    :on-success="uploadSuccess"
                    :on-error="uploadError"
                    :multiple="true"
                    show-file-list
                    drag
                    :on-remove="removeLD"
                    :file-list="fileList"
                >
                    <i class="el-icon-upload"></i>
                </el-upload>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="diaClose">取 消</el-button>
            <el-button type="primary" @click="surModifyInfo">确 定</el-button>
        </span>
    </div>
</template>

<script>
import { client, getFileNameUUID, getTimeNow } from '../../../../../utils/oss.js';
import { Message } from 'element-ui';
export default {
    data() {
        let validatePass1 = (rule, value, callback) => {
            if (!value) {
                callback(new Error('请输入企业全称'));
            } else if (this.getFullNameEnglish(value) == 1) {
                callback(new Error('包含英文括号，注意分别!'));
            } else if (this.getFullNameEnglish(value) == 2) {
                callback(new Error('包含中文括号，注意分别!'));
            } else {
                callback();
            }
        };
        let validatePass2 = (rule, value, callback) => {
            if (!value) {
                callback(new Error('请输入企业全称'));
            } else if (this.getFullNameEnglish(value) == 1) {
                callback(new Error('包含英文括号，注意分别!'));
            } else if (this.getFullNameEnglish(value) == 2) {
                callback(new Error('包含中文括号，注意分别!'));
            } else {
                callback();
            }
        };
        return {
            form: {},
            fileList: [],
            rules: {
                abbrName: [{ required: true, validator: validatePass2, trigger: 'blur' }],
                name: [{ required: true, validator: validatePass1, trigger: 'blur' }]
            }
        };
    },
    props: {
        showId: {
            type: Number,
            require: true
        },
        info: {
            type: Object,
            require: true
        }
    },
    watch: {
        info(val) {
            this.form = JSON.parse(JSON.stringify(val));
        },
        showId(val) {
            if (val) {
            }
        }
    },
    created() {
        this.form = JSON.parse(JSON.stringify(this.info));
        this.fileList = this.info.contractUrls;
    },
    methods: {
        diaClose() {
            this.$emit('diaClose');
        },
        getFullNameEnglish(name) {
            if (name.indexOf('(') != -1 || name.indexOf(')') != -1) {
                return 1;
            } else if (name.indexOf('（') != -1 || name.indexOf('）') != -1) {
                return 2;
            }
        },
        checkNull(value) {
            value = value.replace(/\s+/g, '');
            console.log(value);
            return value;
        },
        surModifyInfo() {
            if (!this.form.abbrName) return Message.error('请输入企业简称！');
            // if(this.getFullNameEnglish(this.form.abbrName)==false) return Message.error('企业简称不能包含英文括号！');
            if (!this.form.name) return Message.error('请输入企业全称！');
            // if(this.getFullNameEnglish(this.form.name)==false) return Message.error('企业全称不能包含英文括号！');
            this.$emit('modifyInfo', { ...this.form, abbrName: this.checkNull(this.form.abbrName), name: this.checkNull(this.form.name) });
        },
        uploadSuccess(res) {
            console.log('success: ', res);
        },
        uploadError(err) {
            console.log('error: ', err);
            this.$message.error('上传失败！请重试');
        },
        uploadURL(option) {
            //注意哦，这里指定文件夹'image/'，尝试过写在配置文件，但是各种不行，写在这里就可以
            var suffix = option.file.name.substring(option.file.name.lastIndexOf('.'));
            var fileName = '/businessLicenseUrl/' + getTimeNow() + '/' + getFileNameUUID() + suffix;
            //定义唯一的文件名，打印出来的uid其实就是时间戳
            client()
                .multipartUpload(fileName, option.file, {
                    progress: function (percentage, cpt) {
                        option.onProgress({ percent: Math.floor(percentage * 100)});
                    }
                })
                .then((res) => {
                    if(res.res.status === 200) {
                        this.form.businessLicenseUrl = fileName;
                        option.onSuccess(res);
                    }else {
                        option.onError(res);
                    }
                }).catch((err) => {
                    option.onError(err);
                });
        },
        uploadURLLD(option) {
            var suffix = option.file.name.substring(option.file.name.lastIndexOf('.'));
            var fileName = '/laborContract/' + getTimeNow() + '/' + getFileNameUUID() + suffix;
            //定义唯一的文件名，打印出来的uid其实就是时间戳
            client()
                .multipartUpload(fileName, option.file, {
                    progress: function (percentage, cpt) {
                        option.onProgress({ percent: Math.floor(percentage * 100)});
                    }
                })
                .then((res) => {
                    if(res.res.status === 200) {
                        this.form.contractUrls.push({
                            url: fileName,
                            name: option.file.name,
                            uid: option.file.uid
                        });
                        option.onSuccess(res);
                    }else {
                        option.onError(res);
                    }
                }).catch((err) => {
                    option.onError(err);
                });
        },
        removeLD(file, fileList, index) {
            this.form.contractUrls.forEach((s, index) => {
                if (s.uid) {
                    if (file.uid == s.uid) {
                        this.form.contractUrls.splice(index, 1);
                    }
                } else {
                    if (file.url == s.url) {
                        this.form.contractUrls.splice(index, 1);
                    }
                }
            });
        }
    }
};
</script>

<style>
</style>