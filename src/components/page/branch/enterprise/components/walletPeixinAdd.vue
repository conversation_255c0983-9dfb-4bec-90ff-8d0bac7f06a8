<template>
    <el-dialog
        :visible.sync="dialogVisible"
        @close="toCloseDia"
        width="50%"
        :title="walletId ? '修改钱包' : '新增聚合支付钱包'"
    >
        <el-tabs type="border-card">
            <el-tab-pane label="基本信息">
                <el-form label-width="350px" :rules="rules" ref="form" :model="form">
                    <el-form-item label="选择商户：">
                        <el-select
                            v-model="merchant"
                            placeholder="请选择"
                            :disabled="walletId ? true : false"
                            style="width: 250px"
                            @change="toChangeInfo"
                            value-key="id"
                            filterable
                        >
                            <el-option :label="item.merchantName" :value="item" v-for="item in merchantList" :key="item.id"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="绑定公司：" prop="companyName">
                        <el-input v-model="form.companyName" style="width: 250px" ref="companyInput"></el-input>
                    </el-form-item>
                    <el-form-item label="接口账号（非聚合支付后台登录账号）：" prop="apiAccount">
                        <el-input v-model="form.peixinApiAccount" style="width: 250px"></el-input>
                    </el-form-item>
                    <el-form-item label="接口账号密码（非聚合支付后台登录密码）：" prop="apiPwd">
                        <el-input v-model="form.peixinApiPwd" style="width: 250px" show-password></el-input>
                    </el-form-item>
                    <el-form-item label="任务模版ID（通过聚合支付后台新增任务模版）" prop="taskTempId">
                        <el-input type="number" v-model="form.peixinTaskTempId" style="width: 250px"></el-input>
                    </el-form-item>
                </el-form>
            </el-tab-pane>
            <el-tab-pane label="钱包费率">
                <el-form label-width="120px">
                    <el-form-item label="计算方式：">
                        <el-select v-model="form.calculationType" placeholder="请选择计算方式" style="width: 250px">
                            <el-option label="外扣" :value="2"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="限额检查：">
                        <el-select v-model="form.quotaType" placeholder="请选择限额检查" style="width: 250px">
                            <el-option label="本商户" :value="1"></el-option>
                            <el-option label="全局" :value="2"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="发放范围/费率：">
                        <el-input v-model="form.walletRates[0].minMoney" style="width: 80px" placeholder="最小金额" disabled></el-input>
                        <el-input v-model="form.walletRates[0].maxMoney" style="width: 80px" placeholder="最大金额"></el-input>
                        <el-input v-model="form.walletRates[0].rate" style="width: 80px" placeholder="费率"></el-input>%
                    </el-form-item>
                    <el-alert
                        title="内扣手续费在结算金额内扣除，发放总金额=发放金额+手续费"
                        type="warning"
                        v-if="form.calculationType == 1"
                        :closable="false"
                    >
                    </el-alert>
                    <el-alert
                        title="外扣手续费在结算金额外扣除，发放总金额=发放金额+发放金额X费率"
                        type="warning"
                        v-if="form.calculationType == 2"
                        :closable="false"
                    >
                    </el-alert>
                </el-form>
            </el-tab-pane>
            <el-tab-pane label="开票设置">
                <el-form label-width="120px">
                    <el-form-item label="发票类型：">
                        <el-select
                            v-model="form.invoiceTypes"
                            placeholder="请选择发票类型"
                            multiple
                            style="width: 250px"
                            clearable
                            filterable
                        >
                            <el-option :label="item.name" :value="item.id" v-for="item in type" :key="item.id"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="开票类目：">
                        <el-select
                            v-model="invoiceCategorys"
                            placeholder="请选择开票类目"
                            multiple
                            allow-create
                            default-first-option
                            style="width: 250px"
                            filterable
                            value-key="id"
                            @change="toChangeSelectInfo"
                            clearable
                        >
                            <el-option :label="item.nameStr" :value="item" v-for="item in category" :key="item.id"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="开票策略：">
                        <el-radio v-model="form.invoiceStrategy" :label="1">按充值</el-radio>
                        <el-radio v-model="form.invoiceStrategy" :label="2">减余额</el-radio>
                    </el-form-item>
                </el-form>
            </el-tab-pane>
        </el-tabs>
        <span slot="footer" class="dialog-footer">
            <el-button @click="toCloseDia">取 消</el-button>
            <el-button type="primary" @click="walletId ? surModifyInfo() : addInfo()">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>
import {
    typeListAll,
    categoryListAll,
    walletDetail,
    walletFinallyDetail,
    listRcMerchantPeixinPay
} from '@/api/branch/enterprise';
export default {
    data() {
        let validatePass1 = (rule, value, callback) => {
            if (!value) {
                callback(new Error('请输入企业全称'));
            } else if (this.getFullNameEnglish(value) == 1) {
                callback(new Error('包含英文括号，注意分别!'));
            } else if (this.getFullNameEnglish(value) == 2) {
                callback(new Error('包含中文括号，注意分别!'));
            } else {
                callback();
            }
        };
        return {
            rules: {
                companyName: [{ required: true, validator: validatePass1, trigger: 'blur' }],
            },
            dialogVisible: false,
            form: {
                walletRates: [
                    {
                        minMoney: 0,
                        maxMoney: '',
                        rate: 0,
                        sequence: '1'
                    }
                ]
            },
            merchantList: [],
            company: '',
            apiAccounnt: '',
            apiPwd: '',
            type: [],
            category: [],
            merchant: undefined,
            invoiceCategorys: []
        };
    },
    methods: {
        toCloseDia() {
            this.$emit('closeDia');
            this.form = {
                walletRates: [
                    {
                        minMoney: 0,
                        maxMoney: '',
                        rate: 0,
                        sequence: '1'
                    }
                ],
                peixinApiAccount: '',
                peixinApiPwd: '',
                peixinTaskTempId: 0
            }
        },
        toChangeSelectInfo(val) {
            let chooseVal = [...val];
            chooseVal.forEach((s, idx) => {
                if (typeof s == 'string') {
                    chooseVal[idx] = {
                        type: 1,
                        name: s
                    };
                } else {
                    chooseVal[idx] = {
                        type: 0,
                        name: s.name,
                        id: s.id
                    };
                }
            });
            this.form.invoiceCategoryDtos = chooseVal;
            this.$forceUpdate();
        },
        getFullNameEnglish(name) {
            if (name.indexOf('(') != -1 || name.indexOf(')') != -1) {
                return 1;
            } else if (name.indexOf('（') != -1 || name.indexOf('）') != -1) {
                return 2;
            }
        },
        checkNull(value) {
            value = value.replace(/\s+/g, '');
            console.log(value);
            return value;
        },
        addInfo() {
            const isNumber = this.isNumber(this.form.walletRates[0].rate);
            if (!this.merchant) return this.$message.error('请选择商户！');
            if (!this.form.companyName) return this.$message.error('请输入绑定公司！');
            if (!this.form.peixinApiAccount) return this.$message.error('请输入聚合支付接口账号！');
            if (!this.form.peixinApiPwd) return this.$message.error('请输入聚合支付接口账号对应的密码！');
            if (!this.form.peixinTaskTempId) return this.$message.error('请输入任务模版ID！');
            if (!this.form.calculationType) return this.$message.error('请选择计算方式！');
            if (!this.form.quotaType) return this.$message.error('请选择限额检查！');
            if (!this.form.walletRates[0].maxMoney) return this.$message.error('请输入最大金额！');
            if (!isNumber) return this.$message.error('请输入费率！');
            if (this.form.invoiceTypes.length == 0) return this.$message.error('请选择开票类型！');
            if (this.invoiceCategorys.length == 0) return this.$message.error('请选择开票类目！');
            if (!this.form.invoiceStrategy) return this.$message.error('请选择开票策略！');
            this.form.invoiceType = this.form.invoiceTypes.join(',');
            let invoiceCategoryDtos = this.form.invoiceCategoryDtos.map((s, idx) => {
                if (!s.id && typeof s == 'string') {
                    this.form.invoiceCategoryDtos[idx] = {
                        type: 1,
                        name: s
                    };
                } else if (!s.id && typeof s != 'string') {
                    this.form.invoiceCategoryDtos[idx] = {
                        type: 1,
                        name: s.name
                    };
                } else {
                    this.form.invoiceCategoryDtos[idx] = {
                        type: 0,
                        name: s.name,
                        id: s.id
                    };
                }
                return this.form.invoiceCategoryDtos;
            });
            let data = {
                ...this.form,
                walletType:5,
                merchantId: this.merchant.id,
                invoiceCategoryDtos: invoiceCategoryDtos[0],
                companyName: this.checkNull(this.form.companyName)
            };

            this.$confirm(`确定要添加钱包？`, '提示', {
                type: 'warning'
            }).then(() => {
                this.$emit('sureInfo', data);
            }).catch(() => {});
        },
        //判断是不是数字
        isNumber(val) {
            var regPos = /^[0-9]+.?[0-9]*/; //判断是否是数字。
            if (regPos.test(val)) {
                return true;
            } else {
                return false;
            }
        },
        surModifyInfo() {
            const isNumber = this.isNumber(this.form.walletRates[0].rate);
            if (!this.merchant) return this.$message.error('请选择商户！');
            if (!this.form.companyName) return this.$message.error('请输入绑定公司！');
            if (!this.form.peixinApiAccount) return this.$message.error('请输入聚合支付接口账号！');
            if (!this.form.peixinApiPwd) return this.$message.error('请输入聚合支付接口账号对应的密码！');
            if (!this.form.peixinTaskTempId) return this.$message.error('请输入任务模版ID！');
            if (!this.form.calculationType) return this.$message.error('请选择计算方式！');
            if (!this.form.quotaType) return this.$message.error('请选择限额检查！');
            if (!this.form.walletRates[0].maxMoney) return this.$message.error('请输入最大金额！');
            if (!isNumber) return this.$message.error('请输入费率！');
            if (this.form.invoiceTypes.length == 0) return this.$message.error('请选择开票类型！');
            if (this.invoiceCategorys.length == 0) return this.$message.error('请选择开票类目！');
            if (!this.form.invoiceStrategy) return this.$message.error('请选择开票策略！');
            this.form.invoiceType = this.form.invoiceTypes.join(',');
            console.log(this.form.invoiceCategoryDtos);
            let invoiceCategoryDtos = this.form.invoiceCategoryDtos.map((s, idx) => {
                if (!s.id && typeof s == 'string') {
                    this.form.invoiceCategoryDtos[idx] = {
                        type: 1,
                        name: s
                    };
                } else if (!s.id && typeof s != 'string') {
                    this.form.invoiceCategoryDtos[idx] = {
                        type: 1,
                        name: s.name
                    };
                } else {
                    this.form.invoiceCategoryDtos[idx] = {
                        type: 0,
                        name: s.name,
                        id: s.id
                    };
                }
                return this.form.invoiceCategoryDtos;
            });
            let data = {
                ...this.form,
                merchantId: this.merchant.id,
                invoiceCategoryDtos: invoiceCategoryDtos[0],
                companyName: this.checkNull(this.form.companyName)
            };

            this.$saveCode()
                .then((res) => {
                    this.$emit('modifyInfo', {
                        ...data,
                        password: res.password
                    });
                })
                .catch(() => {});
        },
        toChangeInfo() {
            this.company = this.merchant.company.trim();
            this.bankNameFull = this.merchant.bankNameFull.trim();
            this.accountNumber = this.merchant.accountNumber.trim();
            this.form.taxesId = this.merchant.taxesId;
        }
    },
    props: {
        showDia: {
            type: Boolean,
            require: true
        },
        walletId: {
            type: String,
            require: true
        }
    },
    watch: {
        showDia(val) {
            this.dialogVisible = val;
        },
        walletId(val) {
            if (val) {
                walletDetail({
                    walletId: val
                }).then((res) => {
                    if (res.data.code == 0) {
                        let data = {
                            ...res.data.data,
                            walletRates: res.data.data.walletRateVos ? res.data.data.walletRateVos.map(rate => ({
                                ...rate
                            })) : [{
                                minMoney: 0,
                                maxMoney: '',
                                rate: '',
                                sequence: '1'
                            }],
                            invoiceTypes: res.data.data.invoiceType.split(',')
                        };
                        this.merchantList.forEach((s) => {
                            if (s.id == res.data.data.merchantId) {
                                this.merchant = s;
                            }
                        });
                        this.company = res.data.data.company;
                        this.bankNameFull = res.data.data.bankNameFull;
                        this.accountNumber = res.data.data.accountNumber;
                        res.data.data.invoiceCategoryDtos.map((s, index) => {
                            if (s.type == 1) {
                                res.data.data.invoiceCategoryDtos[index] = s.name;
                            }
                            return s;
                        });
                        this.invoiceCategorys = res.data.data.invoiceCategoryDtos;
                        this.form = data;
                    }
                });
            } else {
                walletFinallyDetail(this.$route.query.id).then((res) => {
                    if (res.data.code == 0) {
                        let data = {
                            ...res.data.data,
                            walletRates: res.data.data.walletRateVos ? res.data.data.walletRateVos.map(rate => ({
                                ...rate,
                                rate: ''
                            })) : [{
                                minMoney: 0,
                                maxMoney: '',
                                rate: '',
                                sequence: '1'
                            }],
                            invoiceTypes: res.data.data.invoiceType.split(',')
                        };
                        res.data.data.invoiceCategoryDtos.map((s, index) => {
                            if (s.type == 1) {
                                res.data.data.invoiceCategoryDtos[index] = s.name;
                            }
                            return s;
                        });
                        this.invoiceCategorys = res.data.data.invoiceCategoryDtos;
                        this.form = data;
                        this.merchant = '';
                        this.company = '';
                        this.bankNameFull = '';
                        this.accountNumber = '';
                    }
                });
            }
        }
    },
    created() {
        listRcMerchantPeixinPay({
            enterpriseId: this.$route.query.id
        }).then((res) => {
            if (res.data.code == 0) {
                this.merchantList = res.data.data;
            }
        });
        typeListAll().then((res) => {
            if (res.data.code == 0) {
                this.type = res.data.data;
            }
        });
        categoryListAll().then((res) => {
            if (res.data.code == 0) {
                if (res.data.data) {
                    res.data.data.forEach((element) => {
                        element.nameStr = element.parentName + '——' + element.name;
                    });
                    this.category = res.data.data;
                }
            }
        });
        walletFinallyDetail(this.$route.query.id).then((res) => {
            if (res.data.code == 0) {
                let data = {
                    ...res.data.data,
                    walletRates: res.data.data.walletRateVos ? res.data.data.walletRateVos.map(rate => ({
                        ...rate,
                        rate: ''
                    })) : [{
                        minMoney: 0,
                        maxMoney: '',
                        rate: '',
                        sequence: '1'
                    }],
                    invoiceTypes: res.data.data.invoiceType.split(',')
                };
                res.data.data.invoiceCategoryDtos.map((s, index) => {
                    if (s.type == 1) {
                        res.data.data.invoiceCategoryDtos[index] = s.name;
                    }
                    return s;
                });
                this.invoiceCategorys = res.data.data.invoiceCategoryDtos;
                this.form = data;
            }
        });
    }
};
</script>

<style scoped></style>
