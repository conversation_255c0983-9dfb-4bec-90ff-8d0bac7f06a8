<template>
    <div>
        <el-row>
            <el-col :span="12">
                <div class="project_top">
                    <div class="blue_box">
                        基本信息
                        <el-button
                            type="text"
                            class="modify_btn"
                            @click="
                                toOpenDia({
                                    name: '基本信息',
                                    id: 1
                                })
                            "
                            v-has="'platform_enterprise_edi'"
                            >修改</el-button
                        >
                    </div>
                    <!--临时打开移除企业功能-->
                    <el-button type="danger" v-has="'platform_enterprise_del'" @click="toRemoveCompany" v-if="creatTax">移除企业</el-button>
                </div>
                <el-form label-width="120px">
                    <el-form-item label="企业简称："> {{ basicInfo.abbrName }} </el-form-item>
                    <el-form-item label="企业全称：">
                        <span :class="[basicInfo.name ? '' : 'red_word']">{{ basicInfo.name ? basicInfo.name : '未补充' }}</span>
                    </el-form-item>
                    <el-form-item label="负责人：">
                        <span :class="[basicInfo.contacts ? '' : 'red_word']">{{ basicInfo.contacts ? basicInfo.contacts : '未补充' }}</span>
                    </el-form-item>
                    <el-form-item label="创建账号">
                        <span>{{ basicInfo.createName }}</span>
                    </el-form-item>
                    <el-form-item label="创建时间">
                        <span>{{ basicInfo.createTime }}</span>
                    </el-form-item>
                    <el-form-item label="状态：">
                        <el-switch v-model="basicInfo.lockStatus" @change="toChangeLock(basicInfo)" active-text="已禁用" inactive-text="正常"></el-switch>
                    </el-form-item>
                </el-form>
                <div class="project_top">
                    <div class="blue_box">
                        企业信息
                        <el-button
                            type="text"
                            class="modify_btn"
                            @click="
                                toOpenDia({
                                    name: '企业信息',
                                    id: 2
                                })
                            "
                            v-has="'platform_enterprise_edi'"
                            >修改</el-button
                        >
                    </div>
                </div>
                <el-form label-width="120px">
                    <el-form-item label="开票企业名称："> {{ basicInfo.name }} </el-form-item>
                    <el-form-item label="纳税识别号：">
                        <span :class="[basicInfo.taxIdentifyNumber ? '' : 'red_word']">{{
                            basicInfo.taxIdentifyNumber ? basicInfo.taxIdentifyNumber : '未补充'
                        }}</span>
                    </el-form-item>
                    <el-form-item label="企业类型：">
                        <span :class="[basicInfo.companyType !== null ? '' : 'red_word']">{{
                            basicInfo.companyType !== null ? (basicInfo.companyType == 1 ? '小规模纳税人' : '一般纳税人 ') : '未补充'
                        }}</span>
                    </el-form-item>
                    <el-form-item label="企业地址：">
                        <span :class="[basicInfo.address ? '' : 'red_word']">{{ basicInfo.address ? basicInfo.address : '未补充' }}</span>
                    </el-form-item>
                    <el-form-item label="开户银行：">
                        <span :class="[basicInfo.bankName ? '' : 'red_word']">{{ basicInfo.bankName ? basicInfo.bankName : '未补充' }}</span>
                    </el-form-item>
                    <el-form-item label="银行账号：">
                        <span :class="[basicInfo.accountNumber ? '' : 'red_word']">{{
                            basicInfo.accountNumber ? basicInfo.accountNumber : '未补充'
                        }}</span>
                    </el-form-item>
                </el-form>
            </el-col>
            <el-col :span="12">
                <div class="project_top">
                    <div class="blue_box">
                        邮寄地址
                        <el-button
                            type="text"
                            class="modify_btn"
                            @click="
                                toOpenDia({
                                    name: '邮寄地址',
                                    id: 3
                                })
                            "
                            v-has="'platform_enterprise_edi'"
                            >修改</el-button
                        >
                    </div>
                </div>
                <el-form label-width="120px">
                    <el-form-item label="收件人：">
                        <span :class="[basicInfo.consignee ? '' : 'red_word']">{{ basicInfo.consignee ? basicInfo.consignee : '未补充' }}</span>
                    </el-form-item>
                    <el-form-item label="邮寄地址：">
                        <span :class="[basicInfo.consigneeAddress ? '' : 'red_word']">{{
                            basicInfo.consigneeAddress ? basicInfo.consigneeAddress : '未补充'
                        }}</span>
                    </el-form-item>
                </el-form>
                <div class="project_top">
                    <div class="blue_box">
                        文件材料
                        <el-button type="text" class="modify_btn" @click="extraAttachmentsDialogVisible = true">补录材料</el-button>
                    </div>
                </div>
                <el-form label-width="220px">
                    <el-form-item label="营业执照：">
                        <span :class="[basicInfo.businessLicenseUrl ? '' : 'red_word']">{{
                            basicInfo.businessLicenseUrl ? '已存在' : '未补充'
                        }}</span>
                        <el-button type="text" v-if="basicInfo.businessLicenseUrl" @click="toDownFile(basicInfo.businessLicenseUrl, '营业执照')" style="font-size: 15px; margin-left: 10px"
                            >营业执照</el-button
                        >
                    </el-form-item>
                    <el-form-item label="企业合同：" v-if="filteredContracts && filteredContracts.length">
                        <div v-for="(item, index) in filteredContracts" :key="index" class="file-item">
                            <el-tooltip :content="getFileName(item.name, `合同_${index + 1}`)" placement="top" :open-delay="1000">
                                <el-button class="file-name" type="text" v-if="item.url" @click="toDownFile(item.url, getFileName(item.name, `合同_${index + 1}`))" style="font-size: 15px; margin-left: 10px">
                                    {{ getFileName(item.name, `合同_${index + 1}`) }}
                                </el-button>
                            </el-tooltip>
                            <el-tooltip content="删除" placement="top" :open-delay="500">
                                <el-button type="text" style="font-size: 15px; margin-left: 10px; color: red;" icon="el-icon-delete" @click="deleteFile(item.id)"></el-button>
                            </el-tooltip>
                            <el-tooltip content="编辑文件名" placement="top" :open-delay="500">
                                <el-button type="text" style="font-size: 15px; margin-left: 10px; color: green;" icon="el-icon-edit" @click="doEditFileName(item.id, getFileName(item.name, `合同_${index + 1}`))"></el-button>
                            </el-tooltip>
                        </div>
                    </el-form-item>
                    <el-form-item label="社保、完税证明或上下游发票：" v-if="basicInfo.socialTaxProofs">
                        <div v-for="(item, index) in basicInfo.socialTaxProofs" :key="index" class="file-item">
                            <el-tooltip :content="getFileName(item.name, `社保、完税证明或上下游发票_${index + 1}`)" placement="top" :open-delay="1000">
                                <el-button class="file-name" type="text" v-if="item.url" @click="toDownFile(item.url, getFileName(item.name, `社保、完税证明或上下游发票_${index + 1}`))" style="font-size: 15px; margin-left: 10px">
                                    {{ getFileName(item.name, `社保、完税证明或上下游发票_${index + 1}`) }}
                                </el-button>
                            </el-tooltip>
                            <el-tooltip content="删除" placement="top" :open-delay="500">
                                <el-button type="text" style="font-size: 15px; margin-left: 10px; color: red;" icon="el-icon-delete" @click="deleteFile(item.id)"></el-button>
                            </el-tooltip>
                            <el-tooltip content="编辑文件名" placement="top" :open-delay="500">
                                <el-button type="text" style="font-size: 15px; margin-left: 10px; color: green;" icon="el-icon-edit" @click="doEditFileName(item.id, getFileName(item.name, `社保、完税证明或上下游发票_${index + 1}`))"></el-button>
                            </el-tooltip>
                        </div>
                    </el-form-item>
                    <el-form-item label="企业门头照：" v-if="basicInfo.enterpriseDoorPhotos">
                        <div v-for="(item, index) in basicInfo.enterpriseDoorPhotos" :key="index" class="file-item">
                            <el-tooltip :content="getFileName(item.name, `企业门头照_${index + 1}`)" placement="top" :open-delay="1000">
                                <el-button class="file-name" type="text" v-if="item.url" @click="toDownFile(item.url, getFileName(item.name, `企业门头照_${index + 1}`))" style="font-size: 15px; margin-left: 10px">{{ getFileName(item.name, `企业门头照_${index + 1}`) }}</el-button>
                            </el-tooltip>
                            <el-tooltip content="删除" placement="top" :open-delay="500">
                                <el-button type="text" style="font-size: 15px; margin-left: 10px; color: red;" icon="el-icon-delete" @click="deleteFile(item.id)"></el-button>
                            </el-tooltip>
                            <el-tooltip content="编辑文件名" placement="top" :open-delay="500">
                                <el-button type="text" style="font-size: 15px; margin-left: 10px; color: green;" icon="el-icon-edit" @click="doEditFileName(item.id, getFileName(item.name, `企业门头照_${index + 1}`))"></el-button>
                            </el-tooltip>
                        </div>
                    </el-form-item>
                    <el-form-item label="办公室场景照：" v-if="basicInfo.officeScenePhotos">
                        <div v-for="(item, index) in basicInfo.officeScenePhotos" :key="index" class="file-item">
                            <el-tooltip :content="getFileName(item.name, `办公室场景照_${index + 1}`)" placement="top" :open-delay="1000">
                                <el-button class="file-name" type="text" v-if="item.url" @click="toDownFile(item.url, getFileName(item.name, `办公室场景照_${index + 1}`))" style="font-size: 15px; margin-left: 10px">{{ getFileName(item.name, `办公室场景照_${index + 1}`) }}</el-button>
                            </el-tooltip>
                            <el-tooltip content="删除" placement="top" :open-delay="500">
                                <el-button type="text" style="font-size: 15px; margin-left: 10px; color: red;" icon="el-icon-delete" @click="deleteFile(item.id)"></el-button>
                            </el-tooltip>
                            <el-tooltip content="编辑文件名" placement="top" :open-delay="500">
                                <el-button type="text" style="font-size: 15px; margin-left: 10px; color: green;" icon="el-icon-edit" @click="doEditFileName(item.id, getFileName(item.name, `办公室场景照_${index + 1}`))"></el-button>
                            </el-tooltip>
                        </div>
                    </el-form-item>
                    <el-form-item label="C端用工场景照：" v-if="basicInfo.employmentScenePhoto" v-has="'platform_enterprise_employment_scene_photo'">
                        <div v-for="(item, index) in basicInfo.employmentScenePhoto" :key="index" class="file-item">
                            <el-tooltip :content="getFileName(item.name, `C端用工场景照_${index + 1}`)" placement="top" :open-delay="1000">
                                <el-button class="file-name" type="text" v-if="item.url" @click="toDownFile(item.url, getFileName(item.name, `C端用工场景照_${index + 1}`))" style="font-size: 15px; margin-left: 10px;">{{ getFileName(item.name, `C端用工场景照_${index + 1}`) }}</el-button>
                            </el-tooltip>
                            <el-tooltip content="删除" placement="top" :open-delay="500">
                                <el-button type="text" style="font-size: 15px; margin-left: 10px; color: red;" icon="el-icon-delete" @click="deleteFile(item.id)"></el-button>
                            </el-tooltip>
                            <el-tooltip content="编辑文件名" placement="top" :open-delay="500">
                                <el-button type="text" style="font-size: 15px; margin-left: 10px; color: green;" icon="el-icon-edit" @click="doEditFileName(item.id, getFileName(item.name, `C端用工场景照_${index + 1}`))"></el-button>
                            </el-tooltip>
                        </div>
                    </el-form-item>
                    <el-form-item label="银行基本户信息" v-if="basicInfo.basicBankInfo">
                        <div v-for="(item, index) in basicInfo.basicBankInfo" :key="index" class="file-item">
                            <el-tooltip :content="getFileName(item.name, `银行基本户信息（企业开户证明）_${index + 1}`)" placement="top" :open-delay="1000">
                                <el-button class="file-name" type="text" v-if="item.url" @click="toDownFile(item.url, getFileName(item.name, `银行基本户信息（企业开户证明）_${index + 1}`))" style="font-size: 15px; margin-left: 10px;">{{ getFileName(item.name, `银行基本户信息（企业开户证明）_${index + 1}`) }}</el-button>
                            </el-tooltip>
                            <el-tooltip content="删除" placement="top" :open-delay="500">
                                <el-button type="text" style="font-size: 15px; margin-left: 10px; color: red;" icon="el-icon-delete" @click="deleteFile(item.id)"></el-button>
                            </el-tooltip>
                            <el-tooltip content="编辑文件名" placement="top" :open-delay="500">
                                <el-button type="text" style="font-size: 15px; margin-left: 10px; color: green;" icon="el-icon-edit" @click="doEditFileName(item.id, getFileName(item.name, `银行基本户信息（企业开户证明）_${index + 1}`))"></el-button>
                            </el-tooltip>
                        </div>
                    </el-form-item>
                    <el-form-item label="法人身份证" v-if="basicInfo.legalPersonIdCard">
                        <div v-for="(item, index) in basicInfo.legalPersonIdCard" :key="index" class="file-item">
                            <el-tooltip :content="getFileName(item.name, `法人身份证_${index + 1}`)" placement="top" :open-delay="1000">
                                <el-button class="file-name" type="text" v-if="item.url" @click="toDownFile(item.url, getFileName(item.name, `法人身份证_${index + 1}`))" style="font-size: 15px; margin-left: 10px;">{{ getFileName(item.name, `法人身份证_${index + 1}`) }}</el-button>
                            </el-tooltip>
                            <el-tooltip content="删除" placement="top" :open-delay="500">
                                <el-button type="text" style="font-size: 15px; margin-left: 10px; color: red;" icon="el-icon-delete" @click="deleteFile(item.id)"></el-button>
                            </el-tooltip>
                            <el-tooltip content="编辑文件名" placement="top" :open-delay="500">
                                <el-button type="text" style="font-size: 15px; margin-left: 10px; color: green;" icon="el-icon-edit" @click="doEditFileName(item.id, getFileName(item.name, `法人身份证_${index + 1}`))"></el-button>
                            </el-tooltip>
                        </div>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <!-- 修改企业 -->
        <el-dialog :visible.sync="dialogVisible" :title="diaTitle" @close="diaClose" @open="openDia">
            <modifyInfo :info="basicInfos" :showId="showId" v-if="showId" @diaClose="diaClose" @modifyInfo="modifyInfoDia"></modifyInfo>
        </el-dialog>

        <el-dialog title="材料补录" :visible.sync="extraAttachmentsDialogVisible" :before-close="closeExtraAttachmentsDialog">
            <el-form ref="extraAttachmentForm">
                <el-form-item label="营业执照: " style="margin-right: 10px;" prop="businessLicenceList">
                    <el-upload
                        class="file-upload"
                        :class="{ 'hide-upload': extraAttachments.businessLicenceList.length > 0 }"
                        action=""
                        ref="businessLicenceUploader"
                        :http-request="uploadFiles"
                        :multiple="false"
                        :limit="1"
                        accept=".jpg,.jpeg,.png,.pdf,.zip,.rar,.7z"
                        :on-success="businessLicenceUploadSuccess"
                        :on-error="businessLicenceUploadError"
                        :on-remove="businessLicenceeRemove"
                        :on-cancel="businessLicenceeRemove"
                        :on-exceed="businessLicenceExceed"
                        :file-list="extraAttachments.businessLicenceList">
                        <el-button size="small" type="primary">选择文件</el-button>
                    </el-upload>
                </el-form-item>
                <el-form-item label="企业合同: " prop="contractList">
                    <el-upload
                        class="file-upload"
                        action=""
                        :multiple="true"
                        :http-request="uploadFiles"
                        :on-success="contractUploadSuccess"
                        :on-error="contractUploadError"
                        :on-remove="contractRemove"
                        :on-cancel="contractRemove"
                        :file-list="extraAttachments.contractList"
                        accept=".jpg,.jpeg,.png,.pdf,.zip,.rar,.7z">
                        <el-button size="small" type="primary">选择文件</el-button>
                    </el-upload>
                </el-form-item>
                <el-form-item label="社保证明、完税证明或上下游发票:" prop="socialSecurityList">
                    <el-upload
                        class="file-upload"
                        action=""
                        :multiple="true"
                        :http-request="uploadFiles"
                        :on-success="socialSecurityUploadSuccess"
                        :on-error="socialSecurityUploadError"
                        :on-remove="socialSecurityRemove"
                        :on-cancel="socialSecurityRemove"
                        :file-list="extraAttachments.socialSecurityList"
                        accept=".jpg,.jpeg,.png,.pdf,.zip,.rar,.7z">
                        <el-button size="small" type="primary">选择文件</el-button>
                    </el-upload>
                </el-form-item>
                <el-form-item label="企业门头照: " prop="doorPicList">
                    <el-upload
                        class="file-upload"
                        action=""
                        :multiple="true"
                        :http-request="uploadFiles"
                        :on-success="doorPicUploadSuccess"
                        :on-error="doorPicUploadError"
                        :on-remove="doorPicRemove"
                        :on-cancel="doorPicRemove"
                        :file-list="extraAttachments.doorPicList"
                        accept=".jpg,.jpeg,.png,.pdf,.zip,.rar,.7z">
                        <el-button size="small" type="primary">选择文件</el-button>
                    </el-upload>
                </el-form-item>
                <el-form-item label="办公室场景照: " prop="officePicList">
                    <el-upload
                        class="file-upload"
                        action=""
                        :multiple="true"
                        :http-request="uploadFiles"
                        :on-success="officePicUploadSuccess"
                        :on-error="officePicUploadError"
                        :on-remove="officePicRemove"
                        :on-cancel="officePicRemove"
                        :file-list="extraAttachments.officePicList"
                        accept=".jpg,.jpeg,.png,.pdf,.zip,.rar,.7z">
                        <el-button size="small" type="primary">选择文件</el-button>
                    </el-upload>
                </el-form-item>
                <el-form-item label="C端用工场景照: " prop="workingPicList">
                    <el-upload
                        class="file-upload"
                        action=""
                        :multiple="true"
                        :http-request="uploadFiles"
                        :on-success="workingPicUploadSuccess"
                        :on-error="workingPicUploadError"
                        :on-remove="workingPicRemove"
                        :on-cancel="workingPicRemove"
                        :file-list="extraAttachments.workingPicList"
                        accept=".jpg,.jpeg,.png,.pdf,.zip,.rar,.7z">
                        <el-button size="small" type="primary">选择文件</el-button>
                    </el-upload>
                </el-form-item>
                <el-form-item label="银行基本户信息" prop="basicBankInfo">
                    <el-upload
                        class="file-upload"
                        action=""
                        :multiple="true"
                        :http-request="uploadFiles"
                        :on-success="basicBankInfoUploadSuccess"
                        :on-error="basicBankInfoUploadError"
                        :on-remove="basicBankInfoRemove"
                        :on-cancel="basicBankInfoRemove"
                        :file-list="extraAttachments.basicBankInfo"
                        accept=".jpg,.jpeg,.png,.pdf,.zip,.rar,.7z">
                        <el-button size="small" type="primary">选择文件</el-button>
                    </el-upload>
                </el-form-item>
                <el-form-item label="法人身份证" prop="legalPersonIdCard">
                    <el-upload
                        class="file-upload"
                        action=""
                        :multiple="true"
                        :http-request="uploadFiles"
                        :on-success="legalPersonIdCardUploadSuccess"
                        :on-error="legalPersonIdCardUploadError"
                        :on-remove="legalPersonIdCardRemove"
                        :on-cancel="legalPersonIdCardRemove"
                        :file-list="extraAttachments.legalPersonIdCard"
                        accept=".jpg,.jpeg,.png,.pdf,.zip,.rar,.7z">
                        <el-button size="small" type="primary">选择文件</el-button>
                    </el-upload>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="closeExtraAttachmentsDialog">取 消</el-button>
                <el-button type="primary" @click="saveExtraAttachments">保 存</el-button>
            </div>
        </el-dialog>

        <el-dialog title="编辑文件名" :visible.sync="editFileNameDialogVisible" @close="editFileNameDialogClose" width="30%">
            <el-form>
                <el-form-item label="文件名">
                    <el-input v-model="editFileName" placeholder="请输入文件名"></el-input>
                </el-form-item>
                <el-input type="hidden" v-model="editFileId"></el-input>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="editFileNameDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="saveEditFileName">保 存</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { Message } from 'element-ui';
import modifyInfo from './modifyInfo.vue';
import { updateEnterprise, getentErpriseInfo, deleteEnterprise, lockEnterprise, saveEnterpriseExtraAttachments } from '@/api/branch/enterprise.js';
import { fileUpload, delFileById, changeFileName } from '@/api/system/user.js'
import axios from 'axios';
import { getUserInfo } from '@/api/system/login';
import { OSS_URL } from '@/api/config';
import {cdnHost} from '@/config/env.js'
export default {
    components: {
        modifyInfo
    },
    data() {
        return {
            toShowDia: false,
            srcList: [],
            dialogVisible: false,
            diaTitle: '',
            showId: undefined,
            basicInfos: undefined,
            basicInfo: {},
            creatTax: false,
            extraAttachmentsDialogVisible: false,
            extraAttachments: {
                businessLicenceList: [],
                contractList: [],
                socialSecurityList: [],
                doorPicList: [],
                officePicList: [],
                workingPicList: [],
                basicBankInfo: [],
                legalPersonIdCard: []
            },
            editFileNameDialogVisible: false,
            editFileId: '',
            editFileName: '',
        };
    },
    mounted() {
        this.enterpriseId = this.$route.query.id;
        this.getInfo();
    },
    props: {
        basicInfoId: {
            type: [String, Number],
            required: true
        },
        filteredContracts: {
            type: Array,
            default: () => []
        }
    },
    methods: {
        getInfo() {
            getentErpriseInfo({
                enterpriseId: this.enterpriseId
            }).then((res) => {
                if (res.data.code == 0) {
                    this.basicInfo = res.data.data;
                    this.srcList.push(this.basicInfo.businessLicenseUrl);
                } else {
                    Message.error(res.data.msg);
                }
            });
        },
        toOpenDia(info) {
            this.basicInfos = this.basicInfo;
            this.diaTitle = info.name;
            this.showId = info.id;
            this.dialogVisible = true;
        },
        diaClose() {
            this.dialogVisible = false;
            this.showId = undefined;
        },
        openDia() {
            this.basicInfos = this.basicInfo;
        },
        isAlphanumeric(str) {
            return /^[a-zA-Z0-9\.]+$/.test(str);
        },
        getFileName(name, alias) {
            if (!name) {
                return alias;
            }
            if (this.isAlphanumeric(name)) {
                return this.basicInfo.abbrName + '_' + alias + name.substring(name.lastIndexOf('.'));
            }
            return name;
        },
        saveExtraAttachments() {
            this.$confirm('是否确认修改', '修改')
                .then((res) => {
                    saveEnterpriseExtraAttachments({
                        enterpriseId: this.enterpriseId,
                        businessLicence: this.extraAttachments.businessLicenceList.map(item => ({
                            originalName: item.name,
                            uri: item.uri
                        })),
                        contracts: this.extraAttachments.contractList.map(item => ({
                            originalName: item.name,
                            uri: item.uri
                        })),
                        socialSecurity: this.extraAttachments.socialSecurityList.map(item => ({
                            originalName: item.name,
                            uri: item.uri
                        })),
                        doorPic: this.extraAttachments.doorPicList.map(item => ({
                            originalName: item.name,
                            uri: item.uri
                        })),
                        officePic: this.extraAttachments.officePicList.map(item => ({
                            originalName: item.name,
                            uri: item.uri
                        })),
                        workingPic: this.extraAttachments.workingPicList.map(item => ({
                            originalName: item.name,
                            uri: item.uri
                        })),
                        basicBankInfo: this.extraAttachments.basicBankInfo.map(item => ({
                            originalName: item.name,
                            uri: item.uri
                        })),
                        legalPersonIdCard: this.extraAttachments.legalPersonIdCard.map(item => ({
                            originalName: item.name,
                            uri: item.uri
                        }))
                    }).then((res) => {
                        if (res.data.code == 0) {
                            Message.success('保存成功');
                            this.closeExtraAttachmentsDialog();
                            this.getInfo();
                        } else {
                            // Message.error(res.data.msg);
                        }
                    });
                })

        },
        closeExtraAttachmentsDialog() {
            this.extraAttachments = {
                businessLicenceList: [],
                contractList: [],
                socialSecurityList: [],
                doorPicList: [],
                officePicList: [],
                workingPicList: [],
            };
            this.extraAttachmentsDialogVisible = false;
        },
        modifyInfoDia(e) {
            this.$confirm('是否确认修改', '修改')
                .then((res) => {
                    updateEnterprise({
                        ...e,
                        enterpriseId: this.basicInfo.enterpriseId
                    }).then((res) => {
                        if (res.data.code == 0) {
                            this.getInfo();
                            this.dialogVisible = false;
                        } else {
                            Message.error(ress.data.msg);
                        }
                    });
                })
                .catch(() => {});
        },
        toDownFile(url, name) {
            console.log(url);
            axios
                .get(cdnHost + url.replace(/^\//, ''), { responseType: 'blob' })
                .then((response) => {
                    const blob = new Blob([response.data], { type: response.data.type });
                    const link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    let fileName = name ? name : '营业执照';
                    link.download = fileName;
                    link.click();
                    URL.revokeObjectURL(link.href);
                })
                .catch(console.error);
        },
        deleteFile(id) {
            this.$confirm('是否确认删除', '删除')
                .then((res) => {
                    delFileById(id).then((res) => {
                        if (res.data.code == 0) {
                            Message.success('删除成功');
                            this.getInfo();
                        }
                    });
                }).catch(() => {});
        },
        doEditFileName(id, fileName) {
            this.editFileNameDialogVisible = true;
            this.editFileId = id;
            this.editFileName = fileName;
        },
        editFileNameDialogClose() {
            this.editFileNameDialogVisible = false;
            this.editFileId = '';
            this.editFileName = '';
        },
        saveEditFileName() {
            changeFileName({
                id: this.editFileId,
                fileName: this.editFileName
            }).then((res) => {
                if (res.data.code == 0) {
                    Message.success('修改成功');
                    this.editFileNameDialogVisible = false;
                    this.editFileId = '';
                    this.editFileName = '';
                    this.getInfo();
                }
            });
        },
        async toRemoveCompany() {
            let res = await this.$saveCode();
            if (res) {
                deleteEnterprise({
                    id: this.$route.query.id,
                    password: res.password
                }).then((ress) => {
                    if (ress.data.code != 0) return false;
                    Message.success('删除成功！');
                    setTimeout(() => {
                        this.$router.go(-1);
                    }, 1300);
                });
            }
        },
        toChangeLock(info) {
            lockEnterprise({
                enterpriseId: info.enterpriseId,
                lockFlag: info.lockStatus
            }).then((res) => {
                if (res.data.code == 0) {
                    Message.success('修改成功！');
                    this.getInfo();
                }
            });
        },
        uploadFiles(option) {
            const _this = this;
            const file = option.file;
            let formData = new FormData();
            formData.append('file', file);
            formData.append('path', 'enterprise');
            if (file.size > 10 * 1024 * 1024 ) {
                Message.error('文件大小不能超过10MB');
                return;
            }

            fileUpload(formData).then((res) => {
                if (res != undefined && res.data.code == 0) {
                    let new_file = {
                        name: file.name,
                        response: res,
                        percentage: 0,
                        raw: file,
                        size: file.size,
                        status: 'success',
                        uid: file.uid,
                        url: '/' + res.data.data,
                    }
                    option.onSuccess(res, new_file);
                } else {
                    option.onError(res, file);
                }
            });
        },

        businessLicenceUploadSuccess(res, file, fileList) {
            if (res.status == 200 && res.data.code == 0) {
                Message.success('上传成功');
                file.uri = '/' + res.data.data;
                file.url = cdnHost + res.data.data;
                this.extraAttachments.businessLicenceList = [file];
            } else {
                Message.error('上传失败');
            }
        },
        businessLicenceeRemove(file, fileList) {
            this.extraAttachments.businessLicenceList = fileList;
        },
        businessLicenceExceed(files) {
            this.$refs.businessLicenceUploader.clearFiles();
            this.$refs.businessLicenceUploader.handleStart(files[0]);
            this.uploadFiles({
                file: files[0],
                onSuccess: this.businessLicenceUploadSuccess,
                onError: this.businessLicenceUploadError
            });
        },
        businessLicenceUploadError(err, file, fileList) {

        },

        socialSecurityUploadSuccess(res, file, fileList) {
            if (res.status == 200 && res.data.code == 0) {
                Message.success('上传成功');
                file.uri = '/' + res.data.data;
                file.url = cdnHost + res.data.data;
                this.extraAttachments.socialSecurityList = [...this.extraAttachments.socialSecurityList, file];
            } else {
                Message.error('上传失败');
            }
        },
        socialSecurityUploadError(err, file, fileList) {
            Message.error('上传失败');
        },
        socialSecurityRemove(file, fileList) {
            this.extraAttachments.socialSecurityList = fileList;
        },

        doorPicUploadSuccess(res, file, fileList) {
            if (res.status == 200 && res.data.code == 0) {
                Message.success('上传成功');
                file.uri = '/' + res.data.data;
                file.url = cdnHost + res.data.data;
                this.extraAttachments.doorPicList = [...this.extraAttachments.doorPicList, file];
            } else {
                Message.error('上传失败');
            }
        },
        doorPicUploadError(err, file, fileList) {
            Message.error('上传失败');
        },
        doorPicRemove(file, fileList) {
            this.extraAttachments.doorPicList = fileList;
        },

        officePicUploadSuccess(res, file, fileList) {
            if (res.status == 200 && res.data.code == 0) {
                Message.success('上传成功');
                file.uri = '/' + res.data.data;
                file.url = cdnHost + res.data.data;
                this.extraAttachments.officePicList = [...this.extraAttachments.officePicList, file];
            } else {
                Message.error('上传失败');
            }
        },
        officePicUploadError(err, file, fileList) {
            Message.error('上传失败');
        },
        officePicRemove(file, fileList) {
            this.extraAttachments.officePicList = fileList;
            this.attachmentsChanged = true;
        },

        workingPicUploadSuccess(res, file, fileList) {
            if (res.status == 200 && res.data.code == 0) {
                Message.success('上传成功');
                file.uri = '/' + res.data.data;
                file.url = cdnHost + res.data.data;
                this.extraAttachments.workingPicList = [...this.extraAttachments.workingPicList, file];
            } else {
                Message.error('上传失败');
            }
        },
        workingPicUploadError(err, file, fileList) {
            Message.error('上传失败');
        },
        workingPicRemove(file, fileList) {
            this.extraAttachments.workingPicList = fileList;
        },

        basicBankInfoUploadSuccess(res, file, fileList) {
            if (res.status == 200 && res.data.code == 0) {
                Message.success('上传成功');
                file.uri = '/' + res.data.data;
                file.url = cdnHost + res.data.data;
                this.extraAttachments.basicBankInfo = [...this.extraAttachments.basicBankInfo, file];
            } else {
                Message.error('上传失败');
            }
        },
        basicBankInfoUploadError(err, file, fileList) {
            Message.error('上传失败');
        },
        basicBankInfoRemove(file, fileList) {
            this.extraAttachments.basicBankInfo = fileList;
        },

        legalPersonIdCardUploadSuccess(res, file, fileList) {
            if (res.status == 200 && res.data.code == 0) {
                Message.success('上传成功');
                file.uri = '/' + res.data.data;
                file.url = cdnHost + res.data.data;
                this.extraAttachments.legalPersonIdCard = [...this.extraAttachments.legalPersonIdCard, file];
            } else {
                Message.error('上传失败');
            }
        },
        legalPersonIdCardUploadError(err, file, fileList) {
            Message.error('上传失败');
        },
        legalPersonIdCardRemove(file, fileList) {
            this.extraAttachments.legalPersonIdCard = fileList;
        },

        contractUploadSuccess(res, file, fileList) {
            if (res.status == 200 && res.data.code == 0) {
                // 检查文件名是否包含企业名称
                const fileName = file.name.toLowerCase();
                const companyName = this.basicInfo.name.toLowerCase();
                if (!fileName.includes(companyName)) {
                    this.$confirm('请确认您上传的合同属于"' + this.basicInfo.name + '"企业', '提示', {
                        confirmButtonText: '继续上传',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        Message.success('上传成功');
                        file.uri = '/' + res.data.data;
                        file.url = cdnHost + res.data.data;
                        this.extraAttachments.contractList = [...this.extraAttachments.contractList, file];
                    }).catch(() => {
                        // 用户选择取消上传
                        this.extraAttachments.contractList = this.extraAttachments.contractList.filter(item => item.uid !== file.uid);
                        Message.info('已取消上传');
                    });
                } else {
                    Message.success('上传成功');
                    file.uri = '/' + res.data.data;
                    file.url = cdnHost + res.data.data;
                    this.extraAttachments.contractList = [...this.extraAttachments.contractList, file];
                }
            } else {
                Message.error('上传失败');
            }
        },
        contractUploadError(err, file, fileList) {
            Message.error('上传失败');
        },
        contractRemove(file, fileList) {
            this.extraAttachments.contractList = fileList;
        },
    },
    async created() {
        let res = await getUserInfo();
        this.creatTax = res.data.isService;
    }
};
</script>

<style scoped>
.project_name {
    font-size: 18px;
    font-weight: 550;
}
.blue_box {
    padding: 8px 16px;
    background-color: #f9fafc;
    border-radius: 4px;
    border-left: 5px solid #7ca0e5;
    margin: 20px 0;
    width: 300px;
}
.project_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.tip_member {
    border: 1px solid #000;
    color: #000;
    margin-left: 10px;
}
.money {
    font-size: 20px;
    font-weight: 550;
}
.button-size {
    width: 180px;
    height: 40px;
    font-size: 18px;
    margin-top: 20px;
    margin-bottom: 40px;
}
.modify_btn {
    margin-left: 20px;
}
.red_word {
    color: #ff0000;
}
.file-item {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.file-name {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.delete-btn {
    margin-left: 10px;
    color: red;
}
</style>

<style>
.el-upload--text {
    width: auto;
    height: auto;
    border: none;
}
</style>
