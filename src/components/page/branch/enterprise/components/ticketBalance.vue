<template>
    <el-dialog :visible.sync="dialogVisible" @close="toCloseDia" width="50%" title="票额调整" @open="toChangeWallet">
        <el-form label-width="120px" :model="form" :rules="rules">
            <el-form-item label="当前机构：">
                <span>{{ walletInfo.companyName }}</span>
            </el-form-item>
            <el-form-item label="操作前票额：">
                <span>{{ currentTicket ? currentTicket.toLocaleString('zh-CN', {style: 'currency', currency: 'CNY'}) : '' }}</span>
            </el-form-item>
            <el-form-item label="操作后票额：">
                <span>{{ showMoney != undefined ? showMoney.toLocaleString('zh-CN', {style: 'currency', currency: 'CNY'}) : '' }}</span>
            </el-form-item>
            <el-form-item label="调整票额：" prop="money">
                <el-input placeholder="请输入内容" v-model="form.money" @input="toChangeMoney" min="0" type="number" style="width: 300px">
                    <template slot="prepend">
                        <el-select v-model="changeType" style="width: 80px" @change="changeSelect">
                            <el-option label="增加" :value="1"></el-option>
                            <el-option label="减少" :value="2"></el-option>
                        </el-select>
                    </template>
                </el-input>
            </el-form-item>
            <el-form-item label="操作备注：">
                <el-input
                    v-model="form.remark"
                    type="textarea"
                    style="width: 300px"
                    show-word-limit
                    maxlength="200"
                    :autosize="{ minRows: 4, maxRows: 8 }"
                >
                </el-input>
            </el-form-item>
            <!-- <el-form-item label="审核人：" prop="auditUserId">
                <el-select placeholder="请输入内容" v-model="form.auditUserId" style="width: 300px">
                    <el-option :label="item.name" :value="item.id" v-for="item in personList" :key="item.id"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="审核文件：">
                <el-upload
                    action=""
                    :http-request="uploadURL"
                    :before-upload="beforeAvatarUpload"
                    :on-remove="removePic"
                    :multiple="true"
                    drag
                >
                    <i class="el-icon-upload"></i>
                </el-upload>
            </el-form-item> -->
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="diaClose">取 消</el-button>
            <el-button type="primary" @click="surModifyInfo()">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>
import { ticketChange, getAuditPersonList } from '@/api/branch/enterprise';
import { client, getFileNameUUID, getTimeNow } from '../../../../../utils/oss.js';
import { Message } from 'element-ui';

export default {
    data() {
        return {
            dialogVisible: false,
            form: {
                fileList: [],
                money: undefined
            },
            merchantList: [],
            company: '',
            bankName: '',
            accountNumber: '',
            type: [],
            category: [],
            merchant: undefined,
            changeType: 1,
            showMoney: undefined,
            personList: [],
            currentTicket: 0,
            rules: {
                money: [{ required: true, message: '请输入票额', trigger: 'blur' }],
                auditUserId: [{ required: true, message: '请选择审核人', trigger: 'change' }]
            }
        };
    },
    methods: {
        toCloseDia() {
            this.$emit('closeDia');
        },
        surModifyInfo() {
            let money = 0;
            if (!this.form.money) {
                Message.error('请输入调整票额');
                return false;
            }

            // if (!this.form.auditUserId) {
            //     Message.error('请选择审核人');
            //     return false;
            // }
            if (this.changeType == 1) {
                money = parseFloat(this.form.money);
            } else if (this.changeType == 2) {
                money = money - parseFloat(this.form.money);
            }
            this.$confirm(`确定要调整票额？`, '提示', {
                type: 'warning'
            })
                .then(() => {
                    ticketChange({
                        money: money,
                        // fileList: this.form.fileList,
                        remark: this.form.remark,
                        walletId: this.walletInfo.id,
                        // auditUserId: this.form.auditUserId
                    }).then((res) => {
                        if (res.data.code == 0) {
                            this.$emit('sureMoney');
                        } else {
                            Message.error(res.data.msg);
                        }
                    });
                })
                .catch(() => {});
        },
        toChangeInfo() {
            this.company = this.merchant.company;
            this.bankName = this.merchant.bankName;
            this.accountNumber = this.merchant.accountNumber;
        },
        toChangeWallet() {
            this.showMoney = undefined;
            this.changeType = 1;
            this.form = {
                fileList: [],
                money: undefined
            };
            getAuditPersonList({
                type: 1,
                enterpriseId: '',
                walletId: this.walletInfo.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.personList = res.data.data;
                }
            });
        },
        diaClose() {
            this.$emit('closeDia');
        },
        removePic(file, fileList) {
            console.log(file, fileList);
            this.form.fileList.forEach((v, index) => {
                if (v.name == file.name) {
                    this.form.fileList.splice(index, 1);
                }
            });
        },
        uploadURL(file) {
            //注意哦，这里指定文件夹'image/'，尝试过写在配置文件，但是各种不行，写在这里就可以
            var suffix = file.file.name.substring(file.file.name.lastIndexOf('.'));
            var fileName = '/account/' + getTimeNow() + '/' + getFileNameUUID() + suffix;
            //定义唯一的文件名，打印出来的uid其实就是时间戳
            client()
                .multipartUpload(fileName, file.file, {
                    progress: function (percentage, cpt) {
                        console.log('打印进度', percentage);
                    }
                })
                .then((res) => {
                    this.form.fileList.push({
                        url: fileName,
                        name: file.file.name
                    });
                });
        },
        beforeAvatarUpload(file) {
            const isJPEG = file.name.split('.')[1] === 'jpeg';
            const isJPG = file.name.split('.')[1] === 'jpg';
            const isPNG = file.name.split('.')[1] === 'png';
            const isWEBP = file.name.split('.')[1] === 'webp';
            const isGIF = file.name.split('.')[1] === 'gif';
            if (!isJPG && !isJPEG && !isPNG && !isWEBP && !isGIF) {
                this.$message.error('上传图片只能是 JPEG/JPG/PNG 格式!');
            }

            return isJPEG || isJPG || isPNG || isWEBP || isGIF;
        },
        toChangeMoney() {
            let money = 0;
            if (this.form.money) {
                money = parseFloat(this.form.money);
            }
            this.currentTicket = parseFloat(this.currentTicket.toFixed(2));
            if (this.changeType == 1) {
                this.showMoney = this.currentTicket + money;
            } else {
                if (money > this.currentTicket) {
                    if((Math.abs(money - this.currentTicket)) < Number.EPSILON) {
                        this.showMoney = 0;
                    } else {
                        Message.error('调整票额不能大于当前票额！');
                        return false;
                    }
                } else {
                    this.showMoney = this.currentTicket - money;
                }
            }
        },
        changeSelect() {
            this.showMoney = undefined;
            this.form.money = undefined;
        }
    },
    props: {
        showDia: {
            type: Boolean,
            require: true
        },
        walletInfo: {
            type: Object,
            require: true
        }
    },
    watch: {
        showDia(val) {
            this.dialogVisible = val;
        },
        walletInfo(val) {
            
            getAuditPersonList({
                type: 1,
                enterpriseId: '',
                walletId: val.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.personList = res.data.data;
                }
            });
            this.currentTicket = val.currentTicket - val.frozenTicket;
        }
    },
    created() {}
};
</script>

<style scoped>
</style>