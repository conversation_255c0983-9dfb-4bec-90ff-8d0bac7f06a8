<template>
    <div>
        <!-- <el-form label-width="120px">
            <el-form-item label="关键词搜索：">
                <el-input placeholder="请输入关键词" style="width: 200px" v-model="form.channelName"></el-input>
                <el-button type="primary" style="margin-left: 20px" @click="toSeachList">搜索</el-button>
            </el-form-item>
        </el-form> -->
        <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
            <el-table-column prop="createTime" label="创建时间"></el-table-column>
            <el-table-column prop="createName" label="创建账号"></el-table-column>
            <el-table-column prop="name" label="项目名称"></el-table-column>
            <el-table-column prop="address" label="详细地址"></el-table-column>
            <el-table-column label="操作" prop="phone" fixed="right">
                <template slot-scope="scope">
                    <el-button type="text" @click="toJumpDetail(scope.row)" style="font-size: 15px;">查看详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="pagination">
            <el-pagination
                @current-change="handleCurrentChange"
                :current-page="tableData.current"
                layout="prev, pager, next, jumper"
                :total="tableData.total"
            ></el-pagination>
        </div>
    </div>
</template>

<script>
import { selectProjectList } from '@/api/branch/enterprise.js';
export default {
    props: {
        basicInfo: {
            type: Object,
            require: true
        }
    },
    data() {
        return {
            form: {
                current: 1,
                size: 10
            },
            tableData: {}
        };
    },
    methods: {
        getData() {
            selectProjectList({
                ...this.form,
                enterpriseId: this.$route.query.id
            }).then((res) => {
                if (res.data.code == 0&&res.data.data) {
                    this.tableData = res.data.data;
                }
            });
        },
        handleSizeChange(size) {
            this.form.size = size;
            this.form.current = 1;
            this.getData();
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.form.current = current;
            this.getData();
        },
        toJumpDetail(row) {
            // this.$router.push('/')
            this.$router.push('/project/detail?projectId=' + row.id+'&enterpriseId='+this.$route.query.id);
        },
        toSeachList() {
            this.form.current = 1;
            this.getData(); 
            
        }
    },
    created() {
        this.getData();
    }
};
</script>

<style scoped>
</style>