<template>
    <div>
        <el-card shadow="never">
            <div slot="header" class="clearfix">
                <el-button size="medium" type="text" @click="navigatorBack" style="font-size: 17px;"><i class="el-icon-d-arrow-left"></i> 返回</el-button>
                <el-divider direction="vertical"></el-divider>
                <span>企业详情</span>
            </div>
            <el-tabs type="border-card" style="box-shadow: none;">
                <el-tab-pane label="基本信息">
                    <info  :basicInfoId="enterpriseId" :filteredContracts="filteredContracts"></info>
                </el-tab-pane>
                <el-tab-pane label="钱包管理">
                    <walletList :basicInfo="basicInfo"></walletList>
                </el-tab-pane>
                <el-tab-pane label="项目列表">
                    <channel :basicInfo="basicInfo"></channel>
                </el-tab-pane>
                <el-tab-pane label="管理账号">
                    <account :basicInfo="basicInfo"></account>
                </el-tab-pane>
                <el-tab-pane label="角色管理">
                    <role :basicInfo="basicInfo"></role>
                </el-tab-pane>
            </el-tabs>
        </el-card>
    </div>
</template>

<script>
import { Message } from 'element-ui';
import info from './components/info.vue';
import walletList from './components/walletList.vue';
import channel from './components/channel.vue';
import account from './components/account.vue';
import role from './components/role.vue';
import { getentErpriseInfo } from '@/api/branch/enterprise.js';
import { getUserInfo, getUserEnterprise } from '@/api/system/login';
import { mapState } from 'vuex';
import { filteredByTaxAuth } from '@/store/modules/taxAuth';

export default {
    components: {
        info,
        walletList,
        account,
        channel,
        role
    },
    computed: {
        ...mapState('taxAuth', ['taxBelongId', 'taxBelongName']),
        isTaxReady() {
            return !!this.taxBelongId && !!this.taxBelongName;
        },
        filteredContracts() {
            if (!this.isTaxReady) return [];
            return filteredByTaxAuth({
                list: this.basicInfo.contractUrls,
                taxBelongId: this.taxBelongId,
                taxBelongName: this.taxBelongName,
                field: 'name'
            });
        }
    },
    data() {
        return {
            form: {
                channelName: ''
            },
            tableData: {
                records: []
            },
            toShowDia: false,
            channelOne: [],
            current: 1,
            size: 10,
            basicInfo: {},
            enterpriseId: '',
        };
    },
    created() {
        this.enterpriseId = this.$route.query.id;
        this.getInfo();
    },
    methods: {
        getInfo() {
            getentErpriseInfo({
                enterpriseId: this.enterpriseId
            }).then((res) => {
                if (res.data.code == 0) {
                    this.basicInfo = res.data.data;
                } else {
                    Message.error(res.data.msg);
                }
            });
        },
        navigatorBack() {
            this.$router.replace({
                path: '/branch/enterprise',
                query: {
                    current_page: this.$route.query.list_page
                }
            });
        },
        async fetchTaxAuth() {
            const userRes = await getUserInfo();
            // ...后续同理
        }
    },
    mounted() {
        this.fetchTaxAuth();
    }
};
</script>

<style scoped>
.money_box {
    display: flex;
    justify-content: space-around;
    font-size: 14px;
    color: #989898;
    margin-bottom: 20px;
}
.money_box span {
    font-weight: 550;
    font-size: 18px;
}
.button_box {
    display: flex;
    align-items: center;
}
</style>