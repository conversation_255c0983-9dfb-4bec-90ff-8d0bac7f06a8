<template>
    <div>
        <el-card shadow="never">
            <div slot="header" class="clearfix">
                <span>企业列表</span>
            </div>
            <el-form :inline="true" ref="form" :model="form" class="demo-form-inline button_box">
                <el-form-item label="企业名称：">
                    <el-input v-model="form.enterpriseName" placeholder="请输入企业名称" clearable></el-input>
                </el-form-item>
                <!-- 所有税源地 -->
                <el-form-item label="关联税源地：" v-if="isQyxAdmin">
                    <el-select v-model="form.taxId" placeholder="请选择" clearable style="width: 200px" filterable>
                        <el-option v-for="item in taxesList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                    </el-select>
                </el-form-item>
                <!-- oem税源地 -->
                <el-form-item label="关联税源地：" v-if="creatTax">
                    <el-select v-model="form.taxId" placeholder="请选择" clearable style="width: 200px" filterable>
                        <el-option v-for="item in taxesLists" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="toSearchInfo" size="medium">搜 索</el-button>
                </el-form-item>
                <div style="flex: 1"></div>
                <el-form-item>
                    <el-button type="primary" @click="toShowDiaAdd" v-has="'platform_enterprise_add'" v-if="creatTax">新增企业</el-button>
                </el-form-item>
            </el-form>
            <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                <el-table-column prop="name" fixed label="企业名称" min-width="250" class-name="overflow-column" show-overflow-tooltip></el-table-column>
                <el-table-column label="关联税源地" show-overflow-tooltip min-width="250">
                    <template slot-scope="scope">
                        <span class="overflow-column" v-for="i in scope.row.taxesName" :key="i">{{ i }} </span>
                    </template>
                </el-table-column>
                <el-table-column label="渠道" prop="channelName" show-overflow-tooltip min-width="200">
                    <template slot-scope="scope">
                        <div v-if="scope.row.channelNames">
                            <span class="overflow-column" v-for="i in scope.row.channelNames" :key="i">{{ i }} </span>
                        </div>
                        <el-button type="text" v-else>-</el-button>
                    </template>
                </el-table-column>
                
                <el-table-column label="钱包" min-width="150" header-align="center" align="center">
                    <template slot-scope="scope">
                        <el-button type="text" @click="toLookWalletList(scope.row.id)">{{ scope.row.walletNum }}</el-button>
                    </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" prop="phone" width="200" header-align="center" align="center">
                    <template slot-scope="scope">
                        <el-button type="text" @click="toLookDetail(scope.row.id)" style="font-size: 15px;" v-has="'platform_enterprise_details'"
                            >详情</el-button
                        >
                        <el-button type="text" @click="toLookModify(scope.row)" style="font-size: 15px;" v-has="'platform_enterprise_add'">修改</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    v-if="tableData.total > 0"
                    @current-change="handleCurrentChange"
                    :current-page="tableData.current"
                    layout="prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
        </el-card>
        <addDia
            :toShowDia="toShowDia"
            @closeDia="closeDia"
            :rowInfo="rowInfo"
            @addEnterprise="addEnterprises"
            @modifyEnterprise="modifyEnterprise"
        ></addDia>
        <walletList :toShowDia="walletDia" @closeDia="closeDia" :enterpriseId="enterpriseId"> </walletList>
    </div>
</template>

<script>
import addDia from './components/add.vue';
import walletList from './components/walletListDia.vue';
import { enterpriseList, addEnterprise, lockEnterprise, updateByLists, } from '@/api/branch/enterprise.js';
import { getServerTaxes, selectGetLowTaxes } from '@/api/branch/taxes.js';
import { getTaxesList } from '@/api/account/account.js';
import { getUserInfo } from '@/api/system/login';
import { Message } from 'element-ui';
export default {
    components: {
        addDia,
        walletList
    },
    data() {
        return {
            form: {
                enterpriseName: ''
            },
            tableData: {
                records: []
            },
            toShowDia: false,
            channelOne: [],
            current: 1,
            size: 10,
            rowInfo: undefined,
            enterpriseId: '',
            walletDia: false,
            creatTax: false,
            taxesList: [],
            isQyxAdmin: false,
            taxesLists:[]
        };
    },
    async created() {
        let res = await getUserInfo();
        console.log('用户=================>', res.data);
        if (res.data.belongId != '26ff71ec665b4557b4fe64ff1f7d469d') {
            this.creatTax = res.data.isService;
            if (res.data.isService) {
                selectGetLowTaxes(res.data.belongId).then((res) => {
                    if (res.data.code == 0) {
                        this.taxesLists = res.data.data;
                    }
                });
            }
        } else {
            this.isQyxAdmin = true;
        }
        this.getData();
        getTaxesList().then((res) => {
            if (res.data.code == 0) {
                this.taxesList = res.data.data;
            }
        });
    },
    methods: {
        toLookWalletList(id) {
            this.enterpriseId = id;
            this.walletDia = true;
        },
        handleSizeChange(size) {
            this.size = size;
            this.current = 1;
            this.getData();
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.current = current;
            this.$router.replace({query: {current_page: current}})
            this.getData(current);
        },
        closeDia() {
            this.rowInfo = undefined;
            this.toShowDia = false;
            this.walletDia = false;
            this.enterpriseId = '';
        },
        toShowDiaAdd() {
            this.rowInfo = undefined;
            this.toShowDia = false;
        },
        toShowDiaAdd() {
            this.toShowDia = true;
        },
        addEnterprises(e) {
            addEnterprise(e).then((res) => {
                if (res.data.code == 0) {
                    this.current = 1;
                    Message.success('新增成功！');
                    this.toShowDia = false;
                    this.getData();
                }
            });
        },
        modifyEnterprise(e) {
            updateByLists(e).then((res) => {
                if (res.data.code == 0) {
                    this.current = 1;
                    Message.success('修改成功！');
                    this.toShowDia = false;
                    this.getData();
                }
            });
        },
        getData() {
            if(this.$route.query.current_page) {
                this.current = this.$route.query.current_page;
            }
            enterpriseList({
                ...this.form,
                current: this.current,
                size: this.size
            }).then((res) => {
                if (res.data.code == 0) {
                    this.tableData = res.data.data;
                }
            });
        },
        toChangeLock(info) {
            let lockFlag = undefined;
            if (info.lockStatus) {
                lockFlag = 1;
            } else {
                lockFlag = 0;
            }
            var formFile = new FormData();
            formFile.append('enterpriseId', info.id);
            formFile.append('lockFlag', info.lockStatus);
            lockEnterprise({
                enterpriseId: info.id,
                lockFlag: info.lockStatus
            }).then((res) => {
                if (res.data.code == 0) {
                    Message.success('修改成功！');
                    this.getData();
                }
            });
        },
        toLookDetail(id) {
            this.$router.push({
                path: '/branch/enterpriseDetail',
                query: {
                  id: id,
                  list_page: this.current
                }
            });
        },
        toLookModify(item) {
            this.rowInfo = item;
            this.toShowDia = true;
        },
        toSearchInfo() {
            this.current = 1;
            this.getData();
        }
    }
};
</script>

<style scoped>
.money_box {
    display: flex;
    justify-content: space-around;
    font-size: 14px;
    color: #989898;
    margin-bottom: 20px;
}
.money_box span {
    font-weight: 550;
    font-size: 18px;
}
.button_box {
    display: flex;
    align-items: center;
}

.overflow-column {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>
