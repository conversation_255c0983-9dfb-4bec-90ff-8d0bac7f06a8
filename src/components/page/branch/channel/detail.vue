<template>
    <div>
        <el-card shadow="never">
            <div slot="header" class="clearfix">
                <el-button type="text" @click="navigatorBack" style="font-size: 19px;"><i class="el-icon-d-arrow-left"></i> 返回</el-button>
                <el-divider direction="vertical"></el-divider>
                <span>渠道详情</span>
            </div>
            <el-tabs type="border-card" style="box-shadow: none;">
                <el-tab-pane label="基本信息">
                    <info :basicInfo="basicInfo"></info>
                </el-tab-pane>
                <el-tab-pane label="企业列表">
                    <enterpriseList ></enterpriseList>
                </el-tab-pane>
                <el-tab-pane label="管理账号">
                    <account :basicInfo="basicInfo"></account>
                </el-tab-pane>
                <el-tab-pane label="角色管理">
                    <role :basicInfo="basicInfo"></role>
                </el-tab-pane>
            </el-tabs>
        </el-card>
    </div>
</template>

<script>
import { Message } from 'element-ui';
import info from './components/info.vue';
import enterpriseList from './components/enterpriseList.vue';
import account from './components/account.vue';
import role from './components/role.vue';
export default {
    components: {
        info,
        enterpriseList,
        account,
        role
    },
    data() {
        return {
            form: {
                channelName: ''
            },
            tableData: {
                records: []
            },
            toShowDia: false,
            channelOne: [],
            current: 1,
            size: 10,
            basicInfo: {}
        };
    },
    created() {
        this.basicInfo = this.$route.query.id;
    },
    methods: {
        handleSizeChange(size) {
            this.size = size;
            this.current = 1;
            this.getData();
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.current = current;
            this.getData(current);
        },
        closeDia() {
            this.toShowDia = false;
        },
        toShowDiaAdd() {
            this.toShowDia = true;
        },
        addChannel(e) {
            channelInsert(e).then((res) => {
                if (res.data.code == 0) {
                    this.current = 1;
                    Message.success('新增成功！');
                    this.toShowDia = false;
                    this.getData();
                }
            });
        },
        getData() {
            tableListChannel({
                ...this.form,
                current: this.current,
                size: this.size
            }).then((res) => {
                if (res.data.code == 0) {
                    this.tableData = res.data.data;
                }
            });
        },
        toChangeLock(info) {
            updateChannel(info).then((res) => {
                if (res.data.code == 0) {
                    Message.success('修改成功！');
                    this.getData();
                }
            });
        },
        navigatorBack() {
            this.$router.replace({
                path: '/branch/channel',
                query: {
                    current_page: this.$route.query.list_page
                }
            });
        }
    }
};
</script>

<style scoped>
.money_box {
    display: flex;
    justify-content: space-around;
    font-size: 14px;
    color: #989898;
    margin-bottom: 20px;
}
.money_box span {
    font-weight: 550;
    font-size: 18px;
}
.button_box {
    display: flex;
    align-items: center;
}
</style>