<template>
    <div>
        <el-card shadow="never">
            <div slot="header" class="clearfix">
                <span>渠道列表</span>
            </div>
            <el-form :inline="true" ref="form" :model="form" class="demo-form-inline button_box">
                <el-form-item label="渠道名称：">
                    <el-input v-model="form.channelName" placeholder="请输入渠道名称" clearable></el-input>
                </el-form-item>
                <el-form-item label="所属服务商：" v-if="qyxAdmin">
                    <el-select
                        v-model="form.serviceProvider"
                        placeholder="请选择"
                        clearable
                        style="width: 200px"
                        filterable
                        @change="toChangeTaxes"
                    >
                        <el-option v-for="item in oemList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="归属税源地：" v-if="qyxAdmin || creatTax">
                    <el-select v-model="form.taxJurisdiction" placeholder="请选择" clearable style="width: 200px" filterable>
                        <el-option v-for="item in taxesList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="toSearchList" size="medium">搜 索</el-button>
                </el-form-item>
                <div style="flex: 1"></div>
                <el-form-item>
                    <el-button type="primary" @click="toShowDiaAdd" v-has="'platform_channel_add'" v-if="creatTax" size="medium">新增渠道</el-button>
                </el-form-item>
            </el-form>
            <el-table :data="tableData.channelQueries" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                <el-table-column fixed prop="name" label="渠道名称" width="250" class-name="overflow-column" show-overflow-tooltip></el-table-column>
                <!-- <el-table-column prop="facilitatorName" label="服务商" width="200" class-name="overflow-column" show-overflow-tooltip></el-table-column> -->
                <!-- <el-table-column label="提成百分比">
                    <template slot-scope="scope">
                        <span v-for="(item, index) in scope.row.ratePercent" :key="index"
                            >{{ item }}% <span v-show="index == 0">~</span></span
                        >
                    </template>
                </el-table-column> -->
                <el-table-column prop="enterpriseNumber" label="企业数量" min-width="200">
                    <template slot-scope="scope">
                        <el-button type="text" @click="toShowTaxEnter(scope.row.id)">{{ scope.row.enterpriseNumber }}</el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="parentChannelName" label="上级渠道" min-width="300">
                    <template slot-scope="scope">
                        <span>{{ scope.row.parentChannelName ? scope.row.parentChannelName : '无上级' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="归属税源地" width="550" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span v-for="(i, idx) in scope.row.taxesName" :key="idx" class-name="overflow-column">
                            {{ i }}<span v-if="idx < scope.row.taxesName.length - 1">，</span>
                        </span>
                    </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" prop="phone" width="200" header-align="center" align="center">
                    <template slot-scope="scope">
                        <el-button type="text" @click="toLookDetail(scope.row.id)" v-has="'platform_channel_details'" style="font-size: 15px;">详情</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    v-if="tableData.total > 0"
                    @current-change="handleCurrentChange"
                    :current-page="tableData.current"
                    layout="prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
        </el-card>
        <addDia :toShowDia="toShowDia" @closeDia="closeDia" @addChannel="addChannel"></addDia>
        <taxEnter :toShowDia="channelAndEnter" :taxesId="channelId" @closeDia="closeDia"></taxEnter>
    </div>
</template>

<script>
import addDia from './components/add.vue';
import { channelInsert, tableListChannel, updateChannel } from '@/api/branch/channel.js';
import { getUserInfo } from '@/api/system/login';
import { Message } from 'element-ui';
import { getServerTaxes, selectGetLowTaxes } from '@/api/branch/taxes.js';
import taxEnter from './components/taxEnter.vue';
import { getTaxesList } from '@/api/account/account.js';

export default {
    components: {
        addDia,
        taxEnter
    },
    data() {
        return {
            form: {
                channelName: '',
                taxJurisdiction: '',
                serviceProvider: ''
            },
            tableData: {
                channelQueries: []
            },
            toShowDia: false,
            channelOne: [],
            current: 1,
            size: 10,
            channelAndEnter: false,
            channelId: '',
            creatTax: false,
            oemList: [],
            taxesList: [],
            qyxAdmin: false,
            isService: false
        };
    },
    async created() {
        let res = await getUserInfo();
        if (res.data.belongId != '26ff71ec665b4557b4fe64ff1f7d469d') {
            this.creatTax = res.data.isService;
            // 服务商筛选下面的东西
            if (res.data.isService) {
                selectGetLowTaxes(res.data.belongId).then((res) => {
                    if (res.data.code == 0) {
                        this.taxesList = res.data.data;
                    }
                });
            }
        } else {
            this.qyxAdmin = true;
        }
        this.getData();
        let ress = await getServerTaxes();
        this.oemList = ress.data.data;
    },
    methods: {
        handleSizeChange(size) {
            this.size = size;
            this.current = 1;
            this.getData();
        },
        toSearchList() {
            this.current = 1;
            this.getData();
        },
        toChangeTaxes(val) {
            this.form.taxJurisdiction = '';
            selectGetLowTaxes(val).then((res) => {
                if (res.data.code == 0) {
                    this.taxesList = res.data.data;
                }
            });
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.current = current;
            this.$router.replace({query: {current_page: current}})
            this.getData(current);
        },
        closeDia() {
            this.channelAndEnter = false;
            this.toShowDia = false;
            this.channelId = '';
        },
        toShowDiaAdd() {
            this.form = {};
            this.toShowDia = true;
        },
        addChannel(e) {
            channelInsert(e).then((res) => {
                if (res.data.code == 0) {
                    this.current = 1;
                    Message.success('新增成功！');
                    this.toShowDia = false;
                    this.getData();
                }
            });
        },
        getData() {
            if(this.$route.query.current_page) {
                this.current = this.$route.query.current_page;
            }
            tableListChannel({
                ...this.form,
                current: this.current,
                size: this.size
            }).then((res) => {
                if (res.data.code == 0) {
                    this.tableData = res.data.data;
                }
            });
        },
        toLookDetail(id) {
            this.$router.push({
                path: '/branch/channelDetail',
                query: {
                  id: id,
                  list_page: this.current
                }
            });
        },
        async toShowTaxEnter(channel) {
            this.channelId = channel;
            this.channelAndEnter = true;
        }
    }
};
</script>

<style scoped>
.money_box {
    display: flex;
    justify-content: space-around;
    font-size: 14px;
    color: #989898;
    margin-bottom: 20px;
}
.money_box span {
    font-weight: 550;
    font-size: 18px;
}
.button_box {
    display: flex;
    align-items: center;
}

.overflow-column {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>
