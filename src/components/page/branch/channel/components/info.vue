<template>
    <div>
        <div class="project_top">
            <div class="blue_box" style="font-size: 17px;">
                基本信息
                <el-button type="text" class="modify_btn" @click="toOpenDia" v-has="'platform_channel_edi'" size="medium">修 改</el-button>
            </div>
            <!-- <el-button type="danger" v-has="'platform_channel_del'" @click="toRemoveChannel" v-if="creatTax">移除渠道</el-button> -->
        </div>
        <el-form label-width="120px">
            <el-form-item label="渠道名称："> {{ infoDetail.name }} </el-form-item>
            <el-form-item label="上级渠道：">
                <span :class="[infoDetail.parentChannelName ? '' : 'red_word']">{{
                    infoDetail.parentChannelName ? infoDetail.parentChannelName : '--'
                }}</span>
            </el-form-item>
            <el-form-item label="创建账号">
                <span>{{ infoDetail.createName }}</span>
            </el-form-item>
            <el-form-item label="创建时间">
                <span>{{ infoDetail.createTime }}</span>
            </el-form-item>
            <!-- <el-form-item label="提成百分比：">
                <span>{{ infoDetail.ratePercent[0] }}% ~ {{ infoDetail.ratePercent[1] }}%</span>
            </el-form-item> -->
            <el-form-item label="归属税源地：">
                <span v-for="(item, index) in infoDetail.taxes" :key="index"
                    >{{ item.name }}<span v-if="index != infoDetail.taxes.length - 1">，</span></span
                >
            </el-form-item>
            <el-form-item label="联系人：">
                <span :class="[infoDetail.contacts ? '' : 'red_word']">{{ infoDetail.contacts ? infoDetail.contacts : '未补充' }}</span>
            </el-form-item>
            <el-form-item label="备注：">
                <span :class="[infoDetail.remark ? '' : 'red_word']">{{ infoDetail.remark ? infoDetail.remark : '--' }}</span>
            </el-form-item>
            <el-form-item label="状态：">
                <el-switch v-model="infoDetail.lockStatus" @change="toChangeLock(infoDetail)" active-text="已禁用" inactive-text="正常"></el-switch>
            </el-form-item>
        </el-form>
        <!-- 修改税源地 -->
        <modifyInfo :info="infoDetail" :toShowDia="toShowDia" @closeDia="closeDia" @addChannel="modifyChannel"></modifyInfo>
    </div>
</template>

<script>
import { Message } from 'element-ui';
import modifyInfo from './add.vue';
import { getChannelById, updateChannel, deleteChannel, lockChannel } from '@/api/branch/channel.js';
import { getUserInfo } from '@/api/system/login';
export default {
    components: {
        modifyInfo
    },
    data() {
        return {
            toShowDia: false,
            infoDetail: {
                ratePercent: ['', '']
            },
            creatTax: false
        };
    },
    mounted() {},
    props: {
        basicInfo: {
            type: String,
            require: true
        }
    },
    watch: {
        basicInfo(val) {}
    },
    methods: {
        toOpenDia() {
            getChannelById({
                channelId: this.basicInfo
            }).then((res) => {
                if (res.data.code == 0) {
                    this.infoDetail = res.data.data;
                }
                this.toShowDia = true;
            });
        },
        closeDia() {
            this.toShowDia = false;
        },
        addTax(e) {
            updateTaxes(e).then((res) => {
                if (res.data.code == 0) {
                    getTaxesById({
                        taxesId: this.basicInfo.id
                    }).then((res) => {
                        if (res.data.code == 0) {
                            this.infoDetail = res.data.data;
                            this.toShowDia = false;
                        }
                    });
                } else {
                    Message.error(res.data.msg);
                }
            });
        },
        getDetailInfo() {
            getChannelById({
                channelId: this.basicInfo
            }).then((res) => {
                if (res.data.code == 0) {
                    this.infoDetail = res.data.data;
                }
            });
        },
        modifyChannel(info) {
            updateChannel(info).then((res) => {
                if (res.data.code == 0) {
                    this.toShowDia = false;
                    Message.success(res.data.msg);
                    this.getDetailInfo();
                }
            });
        },
        async toRemoveChannel() {
            let res = await this.$saveCode();
            if (res) {
                deleteChannel({
                    id: this.$route.query.id,
                    password: res.password
                }).then((ress) => {
                    if (ress.data.code != 0) return false;
                    Message.success('删除成功！');
                    setTimeout(() => {
                        this.$router.go(-1);
                    }, 1300);
                });
            }
        },
        toChangeLock(info) {
            lockChannel({
                channelId: info.id,
                lockFlag: info.lockStatus
            }).then((res) => {
                if (res.data.code == 0) {
                    Message.success('修改成功！');
                    this.getDetailInfo();
                }
            });
        },
    },
    async created() {
        let res = await getUserInfo();
        console.log('用户=================>', res.data);
        this.creatTax = res.data.isService;
        this.getDetailInfo();
    }
};
</script>

<style scoped>
.project_name {
    font-size: 18px;
    font-weight: 550;
}
.blue_box {
    padding: 8px 16px;
    background-color: #f9fafc;
    border-radius: 4px;
    border-left: 5px solid #7ca0e5;
    margin: 20px 0;
    width: 300px;
}
.project_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.tip_member {
    border: 1px solid #000;
    color: #000;
    margin-left: 10px;
}
.money {
    font-size: 20px;
    font-weight: 550;
}
.button-size {
    width: 180px;
    height: 40px;
    font-size: 18px;
    margin-top: 20px;
    margin-bottom: 40px;
}
.modify_btn {
    margin-left: 20px;
}
.red_word {
    color: #ff0000;
}
</style>