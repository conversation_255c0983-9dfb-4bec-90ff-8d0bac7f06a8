<template>
    <div>
        <el-dialog :title="form.id ? '修改账号' : '新增账号'" :visible.sync="dialogVisible" width="60%" @open="openDia" @close="closeDia">
            <el-form label-width="150px" :rules="rules" :model="form">
                <el-form-item label="登录账号：" prop="account" v-if="!form.id">
                    <el-input v-model="form.account" clearable style="width: 250px" :disabled="form.id ? true : false"></el-input>
                </el-form-item>
                <el-form-item label="登录密码：" v-if="!form.id" prop="password">
                    <el-input v-model="form.password" type="password" clearable style="width: 250px"></el-input>
                </el-form-item>
                <el-form-item label="重复密码：" v-if="!form.id" prop="passwordAgain">
                    <el-input v-model="form.passwordAgain" type="password" clearable style="width: 250px"></el-input>
                </el-form-item>
                <el-form-item label="姓名：" prop="name">
                    <el-input v-model="form.name" clearable style="width: 250px"></el-input>
                </el-form-item>
                <el-form-item label="选择性别：">
                    <el-select v-model="form.sex" placeholder="请选择性别" clearable style="width: 250px" @change="update">
                        <el-option v-for="item in optin" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="选择角色：" prop="roles">
                    <el-select v-model="form.roles" placeholder="请选择角色" clearable multiple style="width: 250px" @change="update">
                        <el-option v-for="item in channelPtions" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="备注：">
                    <el-input v-model="form.remark" style="width: 250px"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeDia">取 消</el-button>
                <el-button type="primary" @click="form.id ? toModify() : toSubmitInfo()">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { selectTaxesRole } from '@/api/branch/channel.js';
import { Message } from 'element-ui';

export default {
    data() {
        return {
            dialogVisible: false,
            form: {
                roles: []
            },
            channelPtions: [],
            optin: [
                {
                    name: '男',
                    id: 0
                },
                {
                    name: '女',
                    id: 1
                }
            ],
            rules: {
                account: [{ required: true, message: '请输入登录账号', trigger: 'blur' }],
                password: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],
                passwordAgain: [{ required: true, message: '请再次输入密码', trigger: 'blur' }],
                name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
                roles: [{ required: true, message: '选择角色', trigger: 'blur' }],
            }
        };
    },
    props: {
        toShowDia: {
            type: Boolean,
            require: true
        },
        belongId: {
            type: String,
            require: true
        },
        modifyInfo: {
            type: Object,
            require: false
        }
    },
    watch: {
        toShowDia(val) {
            this.dialogVisible = val;
                 selectTaxesRole({
                belongId:this.$route.query.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.channelPtions = res.data.data;
                }
            });
        },
        info(val) {
            if (val) {
                console.log(1);
            }
        },
        modifyInfo(val) {
            if (val) {
                this.form = { ...JSON.parse(JSON.stringify(val)) };
                let roles = [];
                this.form.roleList.forEach((s) => {
                    roles.push(s.id);
                    this.form.roles = roles;
                    console.log(this.form.roles);
                });
            } else {
                this.form = {};
            }
        }
    },
    methods: {
        openDia() {},
        closeDia() {
            // this.form = {};
            this.$emit('closeDia');
        },
        update() {
            this.$forceUpdate();
        },
        toSubmitInfo() {
            if (!this.form.account) {
                Message.error('请输入登录账号！');
                return false;
            }
            if (!this.form.name) {
                Message.error('请输入姓名！');
                return false;
            }
            if (!this.form.password) {
                Message.error('请输入登录密码！');
                return false;
            }
            if (!this.form.passwordAgain) {
                Message.error('请再次确认登录密码！');
                return false;
            }
            if (this.form.passwordAgain != this.form.password) {
                Message.error('两次输入的密码不一致！');
                return false;
            }
            if (!this.form.roles) {
                Message.error('请选择账号角色');
                return false;
            }
            if (this.form.roles.length == 0) {
                Message.error('请选择账号角色');
                return false;
            }
            let data = {
                ...this.form,
                belongId: this.$route.query.id
            };
            this.$confirm('是否确认新增账号', '新增')
                .then((res) => {
                    this.$emit('addTax', data);
                })
                .catch(() => {});
        },
        toModify() {
            if (!this.form.name) {
                Message.error('请输入姓名！');
                return false;
            }
                 if (!this.form.roles) {
                Message.error('请选择账号角色');
                return false;
            }
            if (this.form.roles.length == 0) {
                Message.error('请选择账号角色');
                return false;
            }
            let data = {
                ...this.form,
                belongId: this.$route.query.id
            };
            this.$confirm('是否确认修改账号', '修改')
                .then((res) => {
                    this.$emit('modifyTax', data);
                })
                .catch(() => {});
        }
    },
    created() {
        selectTaxesRole({
            belongId: this.$route.query.id
        }).then((res) => {
            if (res.data.code == 0) {
                this.channelPtions = res.data.data;
            }
        });
    }
};
</script>

<style scoped>
.rate_box {
    align-items: center;
    display: flex;
    margin-bottom: 10px;
}
.rate_box:last-child {
    margin-bottom: 0px;
}
</style>