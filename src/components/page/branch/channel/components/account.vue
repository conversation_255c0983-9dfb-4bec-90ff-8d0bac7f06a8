<template>
    <div>
        <el-form label-width="120px" class="button_box">
            <el-form-item label="关键词搜索：">
                <el-input placeholder="请输入关键词" style="width: 200px" v-model="form.selectValue"></el-input>
                <el-button type="primary" style="margin-left: 20px" @click="toSeachList" size="medium">搜 索</el-button>
            </el-form-item>
            <div style="flex: 1"></div>
            <el-form-item>
                <el-badge :value="peopleNum" :hidden="peopleNum > 0 ? false : true" class="item-number">
                    <el-button type="primary" @click="toShowAddQrcode" v-has="'platform_channel_user_add'" size="medium">新增账号</el-button>
                </el-badge>
                <el-button type="primary" @click="toShowAdds" v-if="backDoor" size="medium">新增账号1</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
            <!-- <el-table-column prop="code" label="账号ID" min-width="120"></el-table-column> -->
            <el-table-column prop="account" label="登录账号" width="150" show-overflow-tooltip></el-table-column>
            <el-table-column label="绑定微信昵称" width="150" show-overflow-tooltip>
                <template slot-scope="scope">
                    <div v-if="scope.row.wechatNickName">
                        {{ scope.row.wechatNickName }}
                        <!-- <el-button type="text" style="margin-left: 6px" @click="toUnbindWechat(scope.row.id)" v-has="'platform_channel_user_add'">解绑</el-button> -->
                    </div>
                    <!-- <div v-if="!scope.row.wechatNumber"><el-button type="text" style="margin-left: 6px">二维码绑定</el-button></div> -->
                    <el-popover width="300" trigger="click" v-if="!scope.row.wechatNumber">
                        <wxlogin
                            href="data:text/css;base64,LmltcG93ZXJCb3ggLnFyY29kZSB7CiAgICB3aWR0aDogMTUwcHg7Cn0KLmltcG93ZXJCb3ggLnRpdGxlIHsKICAgIHRleHQtYWxpZ246IGxlZnQ7CiAgICBmb250LXNpemU6IDE2cHg7CiAgICBmb250LXdlaWdodDogYm9sZDsKfQ=="
                            id="wxcode"
                            theme=""
                            :state="scope.row.id + needUrl"
                            :appid="appid"
                            scope="snsapi_login"
                            :redirect_uri="encodeURIComponent(redirect_uri)"
                        ></wxlogin>
                        <!-- <div slot="reference" v-has="'platform_channel_user_add'">
                            <el-button type="text" style="margin-left: 6px">二维码绑定</el-button>
                        </div> -->
                    </el-popover>
                </template>
            </el-table-column>
            <el-table-column prop="name" label="姓名" width="150" show-overflow-tooltip></el-table-column>
            <el-table-column label="角色" width="250" show-overflow-tooltip>
                <template slot-scope="scope">
                    <span v-for="(item, index) in scope.row.roleList" :key="item.id">{{ item.name }}<span v-if="index < scope.row.roleList.length-1">,</span></span>
                </template>
            </el-table-column>
            <el-table-column prop="sex" label="性别" width="80" show-overflow-tooltip>
                <template slot-scope="scope">
                    <span v-if="scope.row.sex == 0">男</span>
                    <span v-if="scope.row.sex == 1">女</span>
                </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" width="200" show-overflow-tooltip></el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="200" show-overflow-tooltip></el-table-column>
            <el-table-column label="操作" prop="phone" width="150" fixed="right" v-has="'platform_channel_user_add'">
                <template slot-scope="scope">
                    <el-button type="text" @click="toModifyInfo(scope.row)" style="font-size: 15px;">修改</el-button>
                    <!-- <el-button type="text" @click="toLockInfo(scope.row.id, scope.row.lockFlag)">{{
                        scope.row.lockFlag == 0 ? '禁用' : '启用'
                    }}</el-button> -->
                    <el-button type="text" @click="toRmAccount(scope.row)" style="font-size: 15px;">移除</el-button>
                    <el-button type="text" @click="toShowPassword(scope.row)" style="font-size: 15px;" v-if="backDoor">重置密码</el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="pagination">
            <el-pagination
                @current-change="handleCurrentChange"
                :current-page="tableData.current"
                layout="prev, pager, next, jumper"
                :total="tableData.total"
            ></el-pagination>
        </div>
        <addAccount
            :toShowDia="toShowDia"
            :belongId="basicInfo.id"
            :modifyInfo="modifyInfo"
            @closeDia="closeDia"
            @modifyTax="modifyTax"
            @addTax="addTax"
        ></addAccount>
        <el-dialog title="重置密码" :visible.sync="passwordDia" width="60%" @close="cancelDia">
            <el-form>
                <el-form-item label="新密码">
                    <el-input type="password" v-model="password" style="width: 250px"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="cancelDia">取 消</el-button>
                <el-button type="primary" @click="toSureInfo">确 定</el-button>
            </span>
        </el-dialog>
        <qrcodeVue :dialogVisible="dialogVisible" :qrcode="qrcode" @closeDia="closeDias"></qrcodeVue>
    </div>
</template>

<script>
import { listUser, addUser, updateTaxesUser, unbindWx, getUserQrCode, rmEnterprise } from '@/api/branch/channel.js';
import addAccount from './addAccount.vue';
import { Message } from 'element-ui';
import wxlogin from 'vue-wxlogin';
import { wxUrlBindCallback, qrcodeAppid } from '@/api/config.js';
import qrcodeVue from './qrcode.vue';
import { getAuditUserList } from '@/api/branch/taxes.js';
export default {
    components: {
        addAccount,
        wxlogin,
        qrcodeVue
    },
    props: {
        basicInfo: {
            type: String,
            require: true
        }
    },
    data() {
        return {
            form: {
                current: 1,
                size: 10,
                selectValue: ''
            },
            belongId: '',
            tableData: {},
            toShowDia: false,
            modifyInfo: {},
            passwordDia: false,
            password: '',
            qrcodeUrl: '',
            redirect_uri: wxUrlBindCallback,
            needUrl: '',
            passwordId: '',
            appid: qrcodeAppid,
            backDoor: false,
            dialogVisible: false,
            peopleNum: 0
        };
    },
    created() {
        const that = this;
        const url = window.location.href;
        this.needUrl = '_' + url;
        console.log('当前路由', this.needUrl);
        if (this.getQueryString('backDoor') == 'xudong') {
            this.backDoor = true;
        }
        // let script = document.createElement('script');
        // script.type = 'text/javascript';
        // script.src = 'http://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js';
        // document.body.appendChild(script);
        this.getData();
    },
    mounted() {
        this.getPeopleNum();
    },
    methods: {
        closeDia() {
            this.toShowDia = false;
        },
        getData() {
            listUser({
                ...this.form,
                belongId: this.$route.query.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.tableData = res.data.data;
                }
            });
        },
        getQueryString(name) {
            return (
                decodeURIComponent(
                    (new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || [, ''])[1].replace(/\+/g, '%20')
                ) || null
            );
        },
        handleSizeChange(size) {
            this.form.size = size;
            this.form.current = 1;
            this.getData();
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.form.current = current;
            this.getData();
        },
        toUnbindWechat(id) {
            this.$confirm('是否确认解绑用户', '解绑')
                .then(() => {
                    unbindWx({
                        userId: id
                    }).then((res) => {
                        if (res.data.code == 0) {
                            this.getData();
                        }
                    });
                })
                .catch(() => {});
        },
        toSeachList() {
            this.form.current = 1;
            this.getData();
        },
        cancelDia() {
            this.passwordDia = false;
            this.password = '';
        },
        toSureInfo() {
            if (!this.password) return Message.error('请输入新密码');
            updateTaxesUser({
                ...this.passwordId,
                password: this.password,
                id: this.passwordId.id,
                type: 3
            }).then((res) => {
                if (res.data.code == 0) {
                    this.passwordDia = false;
                    Message.success(res.data.msg);
                    this.getData();
                }
            });
        },
        toShowPassword(id) {
            this.passwordId = id;
            this.passwordDia = true;
        },
        toShowAdds() {
            this.modifyInfo = undefined;
            this.toShowDia = true;
        },
        addTax(e) {
            addUser(e).then((res) => {
                if (res.data.code == 0) {
                    this.toShowDia = false;
                    this.form.current = 1;
                    this.getData();
                }
            });
        },
        modifyTax(e) {
            updateTaxesUser({
                ...e,
                type: 3
            }).then((res) => {
                if (res.data.code == 0) {
                    this.toShowDia = false;
                    this.getData();
                }
            });
        },
        toModifyInfo(item) {
            this.modifyInfo = item;
            this.toShowDia = true;
        },
        toLockInfo(id, ids) {
            let lockFlag = undefined;
            if (ids == 0) {
                lockFlag = 1;
            } else {
                lockFlag = 0;
            }
            this.$confirm(`确定要${lockFlag == 1 ? '禁用' : '启用'}吗？`, '提示', {
                type: 'warning'
            })
                .then(() => {
                    updateTaxesUser({
                        id,
                        lockFlag
                    }).then((res) => {
                        if (res.data.code == 0) {
                            this.toShowDia = false;
                            this.getData();
                        }
                    });
                })
                .catch(() => {});
        },
        async toShowAddQrcode() {
            let res = await getUserQrCode({
                sceneId: this.$route.query.id,
                type: 3
            });
            if (res.data.code == 0) {
                this.qrcode = res.data.data;
                this.dialogVisible = true;
            }
        },
        closeDias() {
            this.dialogVisible = false;
            this.getData();
            this.getPeopleNum();
        },
        async toRmAccount(item) {
            let res = await this.$saveCode()
            if (res) {
                let ress = await rmEnterprise({
                    userId: item.id,
                    enterpriseId: item.belongId,
                    password:res.password
                });
                if (ress.data.code == 0) {
                    Message.success(ress.data.msg);
                    this.getData();
                }
            }
        },
        getPeopleNum() {
            getAuditUserList({
                businessId: this.$route.query.id
            }).then((res) => {
                if (res.data.code != 0) return false;
                this.peopleNum = res.data.data.length;
            });
        }
    }
};
</script>

<style scoped>
.button_box {
    display: flex;
    align-items: center;
}
</style>