<template>
    <div>
        <el-dialog :title="form.id ? '修改渠道' : '新增渠道'" :visible.sync="dialogVisible" width="60%" @open="openDia" @close="closeDia">
            <el-form>
                <el-form-item>
                    <div class="form_box"><span style="color: red">*</span>渠道名称：</div>
                    <el-input placeholder="请输入渠道名称" v-model="form.name" style="width: 250px"></el-input>
                </el-form-item>
                <el-form-item>
                    <div class="form_box"><span style="color: red">*</span>选择上级渠道：</div>
                    <el-select v-model="form.parentId" placeholder="请选择" clearable style="width: 250px" @change="updataReady" filterable>
                        <el-option v-for="item in channelPtions" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <div class="form_box"><span style="color: red">*</span>选择归属税源地：</div>
                    <el-select
                        v-model="form.taxesId"
                        placeholder="请选择税源地（可多选）"
                        multiple
                        clearable
                        style="width: 250px"
                        @change="updataReady"
                        filterable
                    >
                        <el-option v-for="item in options" :key="item.taxesId" :label="item.taxesName" :value="item.taxesId"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <div class="form_box">联系人：</div>
                    <el-input placeholder="请输入联系人" v-model="form.contacts" style="width: 250px"></el-input>
                </el-form-item>
                <!-- <el-form-item>
                    <div style="display: flex">
                        <div class="form_box"><span style="color: red">*</span>提成百分比：</div>
                        <div>
                            <div v-for="(item, index) in rateDetails" :key="index" class="rate_box">
                                <el-input placeholder="0.00" style="width: 80px" v-model="item.minMoney"></el-input>
                                <span style="margin: 0 10px">-</span>
                                <el-input placeholder="无上限" style="width: 80px" v-model="item.maxMoney"></el-input>
                                <el-input v-model="item.rate" style="width: 140px; margin: 0 10px">
                                    <template slot="append">%</template>
                                </el-input>
                                <el-button type="danger" icon="el-icon-delete" circle @click="toDetelRate(index)"></el-button>
                            </div>
                        </div>
                    </div>
                    <el-button style="margin: 10px 20px 0 150px" @click="toAddRateRow">增加</el-button>
                </el-form-item> -->
                <el-form-item label="">
                    <div class="form_box">备注：</div>
                    <el-input placeholder="请输入备注" v-model="form.remark" style="width: 250px"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeDia">取 消</el-button>
                <el-button type="primary" @click="form.id ? modifyInfo() : toSubmitInfo()">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { channelList, taxesList, getRateDetails, listTaxesByType, getParentChannelList } from '@/api/branch/channel.js';
export default {
    data() {
        return {
            dialogVisible: false,
            form: {
                taxesId: [],
                parentId: undefined
            },
            channelPtions: [
                {
                    id: 0,
                    name: '无上级'
                }
            ],
            options: [],
            rateDetails: [
                {
                    minMoney: '0',
                    maxMoney: '1',
                    rate: '1'
                }
            ]
        };
    },
    props: {
        toShowDia: {
            type: Boolean,
            require: true
        },
        info: {
            type: Object,
            require: false
        }
    },
    watch: {
        toShowDia(val) {
            this.dialogVisible = val;
        },
        info(val) {
            if (val) {
                this.form = JSON.parse(JSON.stringify(val));
                if (this.form.parentChannelId == 0) {
                    this.form.parentId = parseInt(this.form.parentChannelId);
                } else {
                    this.form.parentId = this.form.parentChannelId;
                }
                let taxesId = [];
                this.form.taxes.map((s) => {
                    taxesId.push(s.id);
                    this.form.taxesId = taxesId;
                });
                getRateDetails({
                    channelId: this.$route.query.id
                }).then((res) => {
                    if (res.data.code == 0) {
                        this.rateDetails = res.data.data;
                    }
                });
            } else {
                this.form = {};
            }
        }
    },
    methods: {
        toAddRateRow() {
            this.rateDetails.push({
                minMoney: '',
                maxMoney: '',
                rate: ''
            });
        },
        toDetelRate(index) {
            this.rateDetails.splice(index, 1);
        },
        openDia() {
            let id = '';
            if (this.$route.query.id) {
                id = this.$route.query.id;
            }
            getParentChannelList(id).then((res) => {
                this.channelPtions = this.channelPtions.concat(res.data.data);
            });
            listTaxesByType({
                id,
                type: 3
            }).then((res) => {
                this.options = res.data.data;
            });
        },
        closeDia() {
            this.$emit('closeDia');
        },
        toSubmitInfo() {
            if (!this.form.name) return this.$message.error('请输入渠道名称');
            if (this.form.parentId === undefined) return this.$message.error('请选择上级渠道');
            if (this.form.taxesId.length == 0) return this.$message.error('请选择归属税源地');
            // for (let [index, element] of this.rateDetails.entries()) {
            //     if (index == this.rateDetails.length - 1) {
            //         if (!element.minMoney || !element.rate) {
            //             this.$message.error('请填写正确提成百分比！');
            //             return false;
            //         } else {
            //             element.sequence = index;
            //         }
            //     } else {
            //         console.log(index, '这个index');
            //         if (!element.maxMoney) {
            //             this.$message.error('请填写正确提成百分比！');
            //             return false;
            //         }
            //     }
            // }
            let data = {
                ...this.form,
                rateDetails: this.rateDetails
            };
            this.$confirm('是否确认新增渠道', '新增')
                .then((res) => {
                    this.$emit('addChannel', data);
                    this.form = {
                        taxesId: [],
                        parentId: undefined
                    };
                    this.channelPtions = [
                        {
                            id: 0,
                            name: '无上级'
                        }
                    ];
                    this.options = [];
                    this.rateDetails = [
                        {
                            minMoney: '0',
                            maxMoney: '1',
                            rate: '1'
                        }
                    ];
                })
                .catch(() => {});
        },
        modifyInfo() {
            if (!this.form.name) return this.$message.error('请输入渠道名称');
            if (this.form.parentId === undefined || this.form.parentId.length == 0) return this.$message.error('请选择上级渠道');
            if (this.form.taxesId.length == 0) return this.$message.error('请选择归属税源地');
            // for (let [index, element] of this.rateDetails.entries()) {
            //     if (index == this.rateDetails.length - 1) {
            //         console.log(element, '啊是多久啊是客户对接哈');
            //         element.minMoneyStr = element.minMoney + '';
            //         element.rateStr = element.minMoney + '';
            //         if (!element.minMoneyStr || !element.rateStr) {
            //             this.$message.error('请填写正确提成百分比！');
            //             return false;
            //         } else {
            //             element.sequence = index;
            //         }
            //     } else {
            //         console.log(index, '这个index');
            //         if (!element.maxMoney) {
            //             this.$message.error('请填写正确提成百分比！');
            //             return false;
            //         }
            //     }
            // }
            let data = {
                ...this.form,
                rateDetails: this.rateDetails
            };
            this.$confirm('是否确认修改渠道', '修改')
                .then((res) => {
                    this.$emit('addChannel', data);
                })
                .catch(() => {});
        },
        updataReady() {
            this.$forceUpdate();
            console.log(this.form);
        }
    },
    created() {
        // channelList().then((res) => {
        //     this.channelPtions = this.channelPtions.concat(res.data.data);
        // });
        taxesList().then((res) => {
            this.options = res.data.data;
        });
    }
};
</script>

<style scoped>
.rate_box {
    align-items: center;
    display: flex;
    margin-bottom: 10px;
}
.rate_box:last-child {
    margin-bottom: 0px;
}
.form_box {
    display: inline-block;
    width: 150px;
    text-align: right;
}
</style>