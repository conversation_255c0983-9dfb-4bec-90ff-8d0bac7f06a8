<template>
    <div>
        <el-card shadow="never">
            <div slot="header" class="clearfix">
                <span>税源地列表</span>
            </div>
            <el-form :inline="true" ref="form" :model="form" class="demo-form-inline button_box">
                <el-form-item label="税源地：">
                    <el-input v-model="form.taxesName" placeholder="请输入税源地名称" clearable></el-input>
                </el-form-item>
                <el-form-item label="上级税源地：" v-if="qyxAdmin">
                    <el-select v-model="form.taxesId" filterable placeholder="请选择上级税源地" clearable>
                        <el-option v-for="item in oemSelectList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="toSeach" size="medium">搜 索</el-button>
                </el-form-item>
                <div style="flex: 1"></div>
                <el-form-item>
                    <el-button type="primary" @click="toShowAddOem" v-if="qyxAdmin" size="medium">新增服务商</el-button>
                    <el-button type="primary" @click="toShowAdds" v-has="'platform_tax_add'" v-if="creatTax" size="medium">新增税源地</el-button>
                </el-form-item>
            </el-form>
            <el-table :data="tableData.taxesInfos" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                <el-table-column fixed prop="name" label="税源地名称" width="380" show-overflow-tooltip></el-table-column>
                <el-table-column prop="parentName" label="上级税源地" width="380"></el-table-column>
                <el-table-column prop="channelNumber" label="渠道" min-width="100" align="center" align-header="center">
                    <template slot-scope="scope">
                        <el-button type="text" @click="toShowTaxChannel(scope.row.id)">
                            {{ scope.row.channelNumber }}
                        </el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="enterpriseNumber" label="企业数量" min-width="100" align="center" align-header="center">
                    <template slot-scope="scope">
                        <el-button type="text" @click="toShowTaxEnter(scope.row.id)">
                            {{ scope.row.enterpriseNumber }}
                        </el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="createName" label="创建账号" min-min-width="120"></el-table-column>
                <el-table-column fixed="right" label="操作" prop="phone" width="180" align="center" align-header="center">
                    <template slot-scope="scope">
                        <el-button type="text" @click="toJumpDetail(scope.row)" v-has="'platform_tax_details'" style="font-size: 15px;">详情</el-button>
                        <el-button type="text" @click="toJumpOem(scope.row.id)" v-if="scope.row.isOEM && qyxAdmin" style="font-size: 15px;">配置</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    v-if="tableData.total > 0"
                    @current-change="handleCurrentChange"
                    :current-page="tableData.current"
                    layout="prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
        </el-card>
        <addDia :toShowDia="toShowDia" @closeDia="closeDia" @addTax="addTaxwithP" :defaultCode="defaultCode"></addDia>
        <taxChannel :toShowDia="channelShow" :taxesId="taxesId" @closeDia="closeDia"></taxChannel>
        <taxEnter :toShowDia="channelAndEnter" :taxesId="taxesId" @closeDia="closeDia"></taxEnter>
        <addOem :toShowDia="toShowOemDia" @closeDia="closeDia" @addTax="addTax" :defaultCode="defaultCode"></addOem>
        <configDia :toShowDia="showConfigDia" @closeDia="closeDia" :oemId="oemId"></configDia>
    </div>
</template>

<script>
import { listTaxes, addTaxes, updateTaxes, getDefaultTemplateCode, addSimpleTaxes,selectTaxesList } from '@/api/branch/taxes.js';
import { getUserInfo } from '@/api/system/login';
import addDia from './components/add.vue';
import taxChannel from './components/taxChannel.vue';
import taxEnter from './components/taxEnter.vue';
import addOem from './components/addOem.vue';
import configDia from './components/configDia.vue';
export default {
    components: {
        addDia,
        taxChannel,
        addOem,
        configDia,
        taxEnter
    },
    data() {
        return {
            form: {
                enterpriseId: undefined,
                walletId: undefined,
                auditStatus: '',
                text: '',
                minMoney: '',
                maxMoney: '',
                type: '',
                startTime: '',
                endTime: ''
            },
            tableData: {
                records: []
            },
            current: 1,
            size: 10,
            toShowDia: false,
            defaultCode: '',
            channelAndEnter: false,
            channelShow: false,
            taxesId: '',
            toShowOemDia: false,
            showConfigDia: false,
            oemId: undefined,
            qyxAdmin: false,
            creatTax: false,
            oemSelectList:[]
        };
    },
    async created() {
        this.getData();
        let res = await getUserInfo();
        console.log('用户=================>', res.data);
        this.creatTax = res.data.isService;
        if (res.data.belongId == '26ff71ec665b4557b4fe64ff1f7d469d') {
            this.qyxAdmin = true;
        } else {
            this.qyxAdmin = false;
        }
        let ress = await selectTaxesList()
        this.oemSelectList = ress.data.data

    },
    methods: {
        addTaxwithP(e) {
            addSimpleTaxes(e).then((res) => {
                if (res.data.code == 0) {
                    this.toShowDia = false;
                    this.current = 1;
                    this.getData();
                }
            });
        },
        toShowAddOem() {
            getDefaultTemplateCode().then((res) => {
                if (res.data.code == 0) {
                    this.defaultCode = res.data.data;
                    this.toShowOemDia = true;
                }
            });
        },
        closeDia() {
            this.toShowDia = false;
            this.channelAndEnter = false;
            this.channelShow = false;
            this.toShowOemDia = false;
            this.showConfigDia = false;
            this.taxesId = '';
            this.oemId = '';
        },
        handleSizeChange(size) {
            this.size = size;
            this.current = 1;
            this.getData();
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.current = current;
            this.$router.replace({query: {current_page: current}})
            this.getData();
        },
        getData() {
            if(this.$route.query.current_page) {
                this.current = this.$route.query.current_page;
            }
            let data = {
                ...this.form,
                current: this.current,
                size: this.size
            };
            listTaxes(data).then((res) => {
                if (res.data.code == 0) {
                    this.tableData = res.data.data;
                }
            });
        },
        toSeach() {
            this.current = 1;
            this.getData();
        },
        toJumpOem(id) {
            this.oemId = id;
            this.showConfigDia = true;
        },
        toChangeLock(info) {
            updateTaxes(info).then((res) => {
                if (res.data.code == 0) {
                    Message.success('修改成功！');
                    this.getData();
                }
            });
        },
        toShowAdds() {
            getDefaultTemplateCode().then((res) => {
                if (res.data.code == 0) {
                    this.defaultCode = res.data.data;
                    this.toShowDia = true;
                }
            });
        },
        addTax(e) {
            addTaxes(e).then((res) => {
                if (res.data.code == 0) {
                    this.toShowOemDia = false;
                    this.current = 1;
                    this.getData();
                }
            });
        },
        toJumpDetail(info) {
            this.$router.push({
                path: '/branch/taxDetail',
                query: {
                    info: JSON.stringify(info),
                    list_page: this.current
                }
            });
        },
        async toShowTaxChannel(taxesId) {
            this.taxesId = taxesId;

            this.channelShow = true;
        },
        async toShowTaxEnter(taxesId) {
            this.taxesId = taxesId;
            this.channelAndEnter = true;
        }
    }
};
</script>

<style scoped>
.money_box {
    display: flex;
    justify-content: space-around;
    font-size: 14px;
    color: #989898;
    margin-bottom: 20px;
}
.money_box span {
    font-weight: 550;
    font-size: 18px;
}
.button_box {
    display: flex;
    align-items: center;
}
</style>