<template>
    <div>
        <div class="project_top">
            <div class="blue_box">
                基本信息
                <el-button type="text" class="modify_btn" @click="toOpenDia" v-has="'platform_tax_edi'">修改</el-button>
            </div>
            <!-- <el-button type="danger" v-has="'platform_tax_del'" @click="toRemoveTax" v-if="creatTax">移除税源地</el-button> -->
        </div>
        <el-form label-width="120px">
            <el-form-item label="税源地名称："> {{ infoDetail.name }} </el-form-item>
            <el-form-item label="联系人：">
                <span :class="[infoDetail.contacts ? '' : 'red_word']">{{ infoDetail.contacts ? infoDetail.contacts : '未补充' }}</span>
            </el-form-item>
            <el-form-item label="状态：">
                <el-switch v-model="infoDetail.lockStatus" @change="toChangeLock(infoDetail)" active-text="已禁用" inactive-text="正常"></el-switch>
            </el-form-item>
            <el-form-item label="创建时间">
                <span>{{ infoDetail.createTime }}</span>
            </el-form-item>
        </el-form>
        <!-- 修改税源地 -->
        <modifyInfo :info="infoDetail" :toShowDia="toShowDia" @closeDia="closeDia" @addTax="addTax"></modifyInfo>
    </div>
</template>

<script>
import { Message } from 'element-ui';
import modifyInfo from './add.vue';
import { getTaxesById, updateTaxes, deleteTaxes } from '@/api/branch/taxes.js';
import { getUserInfo } from '@/api/system/login';
export default {
    components: {
        modifyInfo
    },
    data() {
        return {
            toShowDia: false,
            infoDetail: {},
            showSaveCode: false,
            creatTax:false
        };
    },
    mounted() {},
    props: {
        basicInfo: {
            type: Object,
            require: true
        }
    },
    watch: {
        basicInfo(val) {}
    },
    methods: {
        toOpenDia() {
            this.toShowDia = true;
        },
        closeDia() {
            this.toShowDia = false;
        },
        addTax(e) {
            updateTaxes(e).then((res) => {
                if (res.data.code == 0) {
                    getTaxesById({
                        taxesId: this.basicInfo.id
                    }).then((res) => {
                        if (res.data.code == 0) {
                            this.infoDetail = res.data.data;
                            this.toShowDia = false;
                        }
                    });
                } else {
                    Message.error(res.data.msg);
                }
            });
        },
        toRemoveTax() {
            this.$saveCode()
                .then((res) => {
                    deleteTaxes({
                        id: this.basicInfo.id,
                        password: res.password
                    }).then((ress) => {
                        if (ress.data.code != 0) return false;
                        Message.success('删除成功！');
                        setTimeout(() => {
                            this.$router.go(-1);
                        }, 1300);
                    });
                })
                .catch(() => {});
        },
        toChangeLock(info) {
            updateTaxes(info).then((res) => {
                if (res.data.code == 0) {
                    Message.success('修改成功！');
                    this.getData();
                }
            });
        },
    },
    async created() {
        let res = await getUserInfo();
        console.log('用户=================>', res.data);
        this.creatTax = res.data.isService;
        getTaxesById({
            taxesId: this.basicInfo.id
        }).then((res) => {
            if (res.data.code == 0) {
                this.infoDetail = res.data.data;
            }
        });
    }
};
</script>

<style scoped>
.project_name {
    font-size: 18px;
    font-weight: 550;
}
.blue_box {
    padding: 8px 16px;
    background-color: #f9fafc;
    border-radius: 4px;
    border-left: 5px solid #7ca0e5;
    margin: 20px 0;
    width: 300px;
}
.project_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.tip_member {
    border: 1px solid #000;
    color: #000;
    margin-left: 10px;
}
.money {
    font-size: 20px;
    font-weight: 550;
}
.button-size {
    width: 180px;
    height: 40px;
    font-size: 18px;
    margin-top: 20px;
    margin-bottom: 40px;
}
.modify_btn {
    margin-left: 20px;
}
.red_word {
    color: #ff0000;
}
</style>