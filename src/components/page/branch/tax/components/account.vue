<template>
    <div>
        <el-form label-width="120px" class="button_box">
            <el-form-item label="关键词搜索：">
                <el-input placeholder="请输入关键词" style="width: 200px" v-model="form.selectValue"></el-input>
                <el-button type="primary" style="margin-left: 20px" @click="toSeachList" size="medium">搜 索</el-button>
            </el-form-item>
            <div style="flex: 1"></div>
            <el-form-item>
                <el-badge :value="peopleNum" :hidden="peopleNum > 0 ? false : true" class="item-number">
                    <el-button type="primary" @click="toShowAddQrcode" v-has="'platform_tax_user_add'" size="medium">新增账号</el-button>
                </el-badge>
                <el-button type="primary" @click="toShowAdds" v-if="backDoor" size="medium">新增账号1</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
            <el-table-column prop="name" label="姓名" width="120" fixed></el-table-column>
            <el-table-column label="角色" show-overflow-tooltip width="250">
                <template slot-scope="scope">
                    <span v-for="(i, index) in scope.row.roleList" :key="i.id">{{ i.name }}<span v-if="index < scope.row.roleList.length - 1">,</span></span>
                </template>
            </el-table-column>
            <el-table-column label="绑定微信昵称" width="150" show-overflow-tooltip>
                <template slot-scope="scope">
                    <div v-if="scope.row.wechatNumber">
                        {{ scope.row.wechatNickName }}
                    </div>
                    <el-popover width="300" trigger="click" v-else>
                        <wxlogin
                            href="data:text/css;base64,LmltcG93ZXJCb3ggLnFyY29kZSB7CiAgICB3aWR0aDogMTUwcHg7Cn0KLmltcG93ZXJCb3ggLnRpdGxlIHsKICAgIHRleHQtYWxpZ246IGxlZnQ7CiAgICBmb250LXNpemU6IDE2cHg7CiAgICBmb250LXdlaWdodDogYm9sZDsKfQ=="
                            id="wxcode"
                            theme=""
                            :state="scope.row.id + needUrl"
                            :appid="appid"
                            scope="snsapi_login"
                            :redirect_uri="encodeURIComponent(redirect_uri)"
                        ></wxlogin>
                    </el-popover>
                </template>
            </el-table-column>
            <el-table-column prop="sex" label="性别" width="80">
                <template slot-scope="scope">
                    <span v-if="scope.row.sex == 0">男</span>
                    <span v-if="scope.row.sex == 1">女</span>
                </template>
            </el-table-column>
            <el-table-column prop="account" label="登录账号" width="200" show-overflow-tooltip></el-table-column>
            <el-table-column prop="remark" label="备注" show-overflow-tooltip width="200"></el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="180"></el-table-column>
            <el-table-column label="操作" prop="phone" width="200" fixed="right" v-has="'platform_tax_user_add'">
                <template slot-scope="scope">
                    <el-button type="text" @click="toModifyInfo(scope.row)" style="font-size: 15px;">修改</el-button>
                    <el-button type="text" @click="toRmAccount(scope.row)" style="font-size: 15px;">移除</el-button>
                    <!-- <el-button type="text" @click="toLockInfo(scope.row.id, scope.row.lockFlag)">{{
                        scope.row.lockFlag == 0 ? '禁用' : '启用'
                    }}</el-button> -->
                    <el-button type="text" @click="toShowSavePassword(scope.row.id)" style="font-size: 15px;">重置安全码</el-button>
                    <el-button type="text" @click="toShowPassword(scope.row)" v-if="backDoor" style="font-size: 15px;">重置密码</el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="pagination">
            <el-pagination
                @current-change="handleCurrentChange"
                :current-page="tableData.current"
                layout="prev, pager, next, jumper"
                :total="tableData.total"
            ></el-pagination>
        </div>
        <addAccount
            :toShowDia="toShowDia"
            :belongId="basicInfo.id"
            :modifyInfo="modifyInfo"
            @closeDia="closeDia"
            @modifyTax="modifyTax"
            @addTax="addTax"
        ></addAccount>
        <el-dialog title="重置密码" :visible.sync="passwordDia" width="60%" @close="cancelDia">
            <el-form>
                <el-form-item label="新密码">
                    <el-input type="password" v-model="password" style="width: 250px"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="cancelDia">取 消</el-button>
                <el-button type="primary" @click="toSureInfo">确 定</el-button>
            </span>
        </el-dialog>
        <el-dialog title="重置安全码" :visible.sync="passwordSaveDia" width="60%" @close="cancelDia">
            <el-form
                :model="ruleForm"
                status-icon
                :rules="rules"
                label-position="right"
                ref="ruleForm"
                label-width="100px"
                class="demo-ruleForm"
            >
                <el-form-item prop="newPwd" label="新密码：">
                    <el-input
                        type="password"
                        v-model="ruleForm.newPwd"
                        auto-complete="off"
                        placeholder="请输入新密码"
                        clearable
                        show-password
                        style="width: 300px"
                    ></el-input>
                </el-form-item>
                <el-form-item prop="checkPass" label="确认密码">
                    <el-input
                        type="password"
                        v-model="ruleForm.checkPass"
                        auto-complete="off"
                        placeholder="请确认密码"
                        clearable
                        show-password
                        style="width: 300px"
                    ></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="cancelDia">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')">确 定</el-button>
            </span>
        </el-dialog>
        <qrcodeVue :dialogVisible="dialogVisible" :qrcode="qrcode" @closeDia="closeDias" :businessId="basicInfo.id"></qrcodeVue>
    </div>
</template>

<script>
import {
    listUser,
    addUser,
    updateTaxesUser,
    unbindWx,
    getUserQrCode,
    rmEnterprise,
    getAuditUserList,
    setSaveUserPwd
} from '@/api/branch/taxes.js';
import qrcodeVue from './qrcode.vue';
import addAccount from './addAccount.vue';
import { Message } from 'element-ui';
import { wxUrlBindCallback, qrcodeAppid } from '@/api/config.js';
import wxlogin from 'vue-wxlogin';
export default {
    components: {
        addAccount,
        wxlogin,
        qrcodeVue
    },
    props: {
        basicInfo: {
            type: String,
            require: true
        },
        aginRowList: {
            type: Boolean,
            require: false
        }
    },
    watch: {},
    data() {
        let validatePass1 = (rule, value, callback) => {
            if (!value) {
                callback(new Error('请输入新密码'));
            } else if (value.toString().length < 6 || value.toString().length > 18) {
                callback(new Error('密码长度为6-18位'));
            } else if (value === this.ruleForm.oldPwd) {
                callback(new Error('新密码与旧密码一致！'));
            } else {
                callback();
            }
        };
        let validatePass2 = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('请再次输入密码'));
            } else if (value !== this.ruleForm.newPwd) {
                callback(new Error('两次输入的密码不一致'));
            } else {
                callback();
            }
        };
        return {
            form: {
                current: 1,
                size: 10,
                selectValue: ''
            },
            belongId: '',
            tableData: {},
            toShowDia: false,
            modifyInfo: {},
            passwordDia: false,
            password: '',
            qrcodeUrl: '',
            redirect_uri: wxUrlBindCallback,
            needUrl: '',
            passwordId: '',
            appid: qrcodeAppid,
            backDoor: false,
            dialogVisible: false,
            qrcode: {},
            peopleNum: 0,
            passwordSaveDia: false,
            ruleForm: {
                oldPwd: '',
                newPwd: '',
                checkPass: ''
            },
            rules: {
                oldPwd: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
                newPwd: [{ required: true, validator: validatePass1, trigger: 'blur' }],
                checkPass: [{ required: true, validator: validatePass2, trigger: 'blur' }]
            },
            userId: ''
        };
    },
    created() {
        const that = this;
        const url = window.location.href;
        this.needUrl = '_' + url;
        console.log('当前路由', this.needUrl);
        // let script = document.createElement('script');
        // script.type = 'text/javascript';
        // script.src = 'http://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js';
        // document.body.appendChild(script);
        this.getData();
        if (this.getQueryString('backDoor') == 'xudong') {
            this.backDoor = true;
        }
    },
    mounted() {
        this.getPeopleNum();
    },
    methods: {
        closeDia() {
            this.toShowDia = false;
        },
        getData() {
            listUser({
                ...this.form,
                belongId: this.basicInfo.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.tableData = res.data.data;
                }
            });
        },
        getQueryString(name) {
            return (
                decodeURIComponent(
                    (new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || [, ''])[1].replace(/\+/g, '%20')
                ) || null
            );
        },
        handleSizeChange(size) {
            this.form.size = size;
            this.form.current = 1;
            this.getData();
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.form.current = current;
            this.getData();
        },
        toSeachList() {
            this.form.current = 1;
            this.getData();
        },
        cancelDia() {
            this.passwordDia = false;
            this.password = '';
            this.passwordSaveDia = false;
            this.ruleForm = {
                oldPwd: '',
                newPwd: '',
                checkPass: ''
            };
        },
        toSureInfo() {
            if (!this.password) return Message.error('请输入新密码');
            updateTaxesUser({
                ...this.passwordId,
                password: this.password,
                id: this.passwordId.id,
                type: 1
            }).then((res) => {
                if (res.data.code == 0) {
                    this.passwordDia = false;
                    Message.success(res.data.msg);
                    this.getData();
                }
            });
        },
        toShowPassword(id) {
            this.passwordId = id;
            this.passwordDia = true;
        },
        toShowAdds() {
            this.modifyInfo = undefined;
            this.toShowDia = true;
        },
        addTax(e) {
            addUser(e).then((res) => {
                if (res.data.code == 0) {
                    this.toShowDia = false;
                    this.form.current = 1;
                    this.getData();
                }
            });
        },
        modifyTax(e) {
            updateTaxesUser({
                ...e,
                type: 1
            }).then((res) => {
                if (res.data.code == 0) {
                    this.toShowDia = false;
                    this.getData();
                }
            });
        },
        toUnbindWechat(id) {
            this.$confirm('是否确认解绑用户', '解绑')
                .then(() => {
                    unbindWx({
                        userId: id
                    }).then((res) => {
                        if (res.data.code == 0) {
                            this.getData();
                        }
                    });
                })
                .catch(() => {});
        },
        toModifyInfo(item) {
            this.modifyInfo = item;
            this.toShowDia = true;
        },
        toLockInfo(id, ids) {
            let lockFlag = undefined;
            if (ids == 0) {
                lockFlag = 1;
            } else {
                lockFlag = 0;
            }
            this.$confirm(`确定要${lockFlag == 1 ? '禁用' : '启用'}吗？`, '提示', {
                type: 'warning'
            })
                .then(() => {
                    updateTaxesUser({
                        id,
                        lockFlag
                    }).then((res) => {
                        if (res.data.code == 0) {
                            this.toShowDia = false;
                            this.getData();
                        }
                    });
                })
                .catch(() => {});
        },
        async toShowAddQrcode() {
            let res = await getUserQrCode({
                sceneId: this.basicInfo.id,
                type: 1
            });
            if (res.data.code == 0) {
                this.qrcode = res.data.data;
                this.dialogVisible = true;
            }
        },
        closeDias() {
            this.dialogVisible = false;
            this.getData();
            this.getPeopleNum();
        },
        //解绑用户
        async toRmAccount(item) {
            let res = await this.$saveCode();
            if (res) {
                let ress = await rmEnterprise({
                    userId: item.id,
                    enterpriseId: item.belongId,
                    password: res.password
                });
                if (ress.data.code == 0) {
                    Message.success(ress.data.msg);
                    this.getData();
                }
            }
        },
        //获取当前人数量
        getPeopleNum() {
            getAuditUserList({
                businessId: this.basicInfo.id
            }).then((res) => {
                if (res.data.code != 0) return false;
                this.peopleNum = res.data.data.length;
            });
        },
        toShowSavePassword(id) {
            this.userId = id;
            this.passwordSaveDia = true;
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    /**新设置安全密码逻辑**/
                    let values = Object.create(null);
                    values.oldPassword = this.ruleForm.oldPwd;
                    values.newPassword = this.ruleForm.newPwd;
                    values.userId = this.userId;
                    setSaveUserPwd(values).then((res) => {
                        if (res.data.code == 0) {
                            this.$message.success('安全码重置成功！');
                            this.passwordSaveDia = false;
                            this.ruleForm = {
                                oldPwd: '',
                                newPwd: '',
                                checkPass: ''
                            };
                        }
                    });
                } else {
                }
            });
        }
    }
};
</script>

<style scoped>
.button_box {
    display: flex;
    align-items: center;
}
.qrcode_img {
    width: 150px;
    height: 150px;
    display: block;
    margin: 0 auto;
}
</style>