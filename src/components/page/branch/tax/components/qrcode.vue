<template>
    <div>
        <el-dialog title="机构管理员" :visible.sync="toshowDia" @close="closeDia" @open="getPeopleList">
            <div class="qrcode_box">
                <div class="top_title">
                    微信扫一扫，成为<span style="color: #2697ff">机构管理员</span>
                    <img :src="qrcodeCom.url" alt="" />
                    <div class="time_or_name">有效期至{{ qrcodeCom.overTime }}</div>
                    <div class="time_or_name">「{{ qrcodeCom.name }}」</div>
                    <i class="el-icon-refresh-right" @click="toNewQrcode"></i>
                </div>
                <div style="flex: 1">
                    <div class="demo-input-suffix">
                        <el-input placeholder="请输入微信昵称" v-model="input4" @input="toSearchPeopleList">
                            <i slot="prefix" class="el-input__icon el-icon-search"></i>
                        </el-input>
                        <el-button type="primary" style="margin-left: 20px" @click="toSearchPeopleList" size="medium">搜 索</el-button>
                    </div>
                    <div class="people_box" v-for="item in peopleShowList" :key="item.workerId">
                        <div class="people_item">
                            <img :src="item.avatar" alt="" v-if="item.avatar" />
                            <el-avatar v-if="!item.avatar"> {{ item.wechatNickName }} </el-avatar>
                            <div class="name">{{ item.wechatNickName }}</div>
                            <el-button type="danger" icon="el-icon-close" circle @click="toCancelInGroup(item)"></el-button>
                            <el-button type="primary" icon="el-icon-check" circle @click="toSureInGroup(item)"></el-button>
                        </div>
                    </div>
                </div>
            </div>
            <addAccountQrcode
                :toShowDia="addInfoQrcode"
                :belongId="belongIdQrcode"
                @closeDia="closeDiaAdd"
                :peopleInfoAuid="peopleInfoAuid"
                @addTax="addPeople"
            ></addAccountQrcode>
            <el-dialog width="30%" title="驳回" :visible.sync="innerVisible" append-to-body @close="toCloseinnerVisible">
                <el-input placeholder="请输入驳回理由" v-model="remarkAudi"></el-input>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="toCloseinnerVisible">取 消</el-button>
                    <el-button type="primary" @click="toRemarkInfo">确认</el-button>
                </div>
            </el-dialog>
        </el-dialog>
    </div>
</template>

<script>
import { getAuditUserList, userAuditReadly } from '@/api/branch/taxes.js';
import { Message } from 'element-ui';
import addAccountQrcode from './addAccountQrcode.vue';
export default {
    data() {
        return {
            toshowDia: false,
            input4: '',
            groupId: '',
            peopleList: [],
            qrcodeCom: {},
            peopleListSearch: [],
            peopleShowList: [],
            addInfoQrcode: false,
            belongIdQrcode: '',
            peopleInfoAuid: {},
            innerVisible: false,
            remarkAudi: ''
        };
    },
    components: {
        addAccountQrcode
    },
    props: {
        dialogVisible: {
            type: Boolean,
            require: true
        },
        qrcode: {
            type: Object,
            require: true
        },
        businessId: {
            type: String,
            require: true
        }
    },
    watch: {
        dialogVisible: {
            handler(newVal) {
                this.toshowDia = newVal;
            }
        },

        qrcode: {
            handler(val) {
                this.qrcodeCom = val;
            }
        }
    },
    methods: {
        closeDia() {
            this.$emit('closeDia');
        },
        closeDiaAdd() {
            this.addInfoQrcode = false;
        },
        toCloseinnerVisible(){
            this.innerVisible = false;
            this.remarkAudi = ''
        },
     
        toNewQrcode() {
            groupQrcode({
                sceneId: this.groupId
            }).then((res) => {
                console.log(res);
                if (res.data.code === 0) {
                    Message.success('更新成功');
                    this.qrcodeCom = res.data.data;
                } else {
                    Message({
                        message: res.data.msg,
                        type: 'error'
                    });
                }
            });
        },
        toSureInGroup(item) {
            this.$confirm(`确认要同意吗？`, '提示', {
                type: 'warning'
            })
                .then(() => {
                    this.peopleInfoAuid = item;
                    this.addInfoQrcode = true;
                })
                .catch(() => {});
        },
        async addPeople(e) {
            let res = await userAuditReadly({
                audit: 1,
                userId: this.peopleInfoAuid.id,
                ...e
            });
            if (res.data.code != 0) return false;
            Message.success(res.data.msg);
            this.getPeopleList();
        },
        toCancelInGroup(item) {
            this.$confirm(`确认要拒绝吗？`, '提示', {
                type: 'warning'
            })
                .then(() => {
                    this.peopleInfoAuid = item;
                    this.innerVisible = true;
                })
                .catch(() => {});
        },
        getPeopleList() {
            // setInterval(() => {
            getAuditUserList({
                businessId: this.businessId
            }).then((res) => {
                if (res.data.code != 0) return false;
                this.peopleList = res.data.data;
                this.peopleShowList = res.data.data;
            });
            // }, 1000);
        },
        toRemarkInfo() {
            if (!this.remarkAudi) return this.$message.error('请输入驳回理由！');
            userAuditReadly({
                audit: 2,
                businessId: this.peopleInfoAuid.belongId,
                userId: this.peopleInfoAuid.id,
                remark: this.remarkAudi
            }).then((res) => {
                if (res.data.code == 0) {
                    Message.success(res.data.msg);
                    this.innerVisible = false;
                    this.remarkAudi = '';
                    this.getPeopleList();
                }
            });
        },
        toSearchPeopleList() {
            let array = this.peopleList;
            let newArray = array.filter((x) => {
                if (x.wechatNickName.includes(this.input4)) return x;
            });
            if (this.input4) {
                this.peopleShowList = newArray;
            } else {
                this.peopleShowList = this.peopleList;
            }
            console.log(this.peopleListSearch);
        }
    }
};
</script>

<style scoped>
.qrcode_box {
    display: flex;
}
.top_title img {
    width: 200px;
    height: 200px;
    display: block;
    margin: 0 auto;
    margin-top: 10px;
}
.top_title {
    text-align: center;
    margin-right: 30px;
}
.time_or_name {
    margin: 5px 0;
}
.demo-input-suffix {
    display: flex;
    margin-bottom: 20px;
}
.people_item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}
.people_item img {
    width: 40px;
    height: 40px;
    margin-right: 15px;
    border-radius: 50%;
}
.people_item .name {
    flex: 1;
}
</style>
