<template>
    <div>
        <el-dialog title="关联企业" :visible.sync="dialogVisible" width="70%" top="4vh" @close="closeDia">
            <el-form>
                <el-form-item label="企业名称：">
                    <el-input placeholder="请输入企业名称" style="width: 250px" v-model="formDia.name"></el-input>
                    <el-button type="primary" style="margin-left: 10px" @click="toSearchTable()" size="medium">搜 索</el-button>
                </el-form-item>
            </el-form>
            <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                <el-table-column prop="name" label="企业名称" min-width="120"></el-table-column>
                <el-table-column prop="contacts" label="负责人"></el-table-column>
                <el-table-column prop="createName" label="创建账号"></el-table-column>
                <el-table-column label="创建时间" prop="createTime"></el-table-column>
                <el-table-column label="操作" prop="phone">
                    <template slot-scope="scope">
                        <el-button type="text" @click="toLookDetail(scope.row.id)" style="font-size: 15px;" v-has="'platform_enterprise_details'"
                            >查看详情</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page="tableData.current"
                    :page-size="tableData.size"
                    :page-sizes="[10]"
                    layout="total,sizes, prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { showEnter } from '@/api/branch/taxes.js';
export default {
    data() {
        return {
            dialogVisible: false,
            formDia: {
                size: 10,
                current: 1,
                name: ''
            },
            tableData: {}
        };
    },
    props: {
        toShowDia: {
            type: Boolean,
            require: true
        },
        taxesId: {
            type: String,
            require: true
        }
    },
    watch: {
        toShowDia(val) {
            this.dialogVisible = val;
        },
        async taxesId(val) {
            if (val) {
                let res = await showEnter({
                    ...this.formDia,
                    taxesId: val
                });
                if (res.data.code != 0) return false;
                this.tableData = res.data.data;
            }
        }
    },
    methods: {
        async toSearchTable() {
            this.formDia.current = 1;
            let res = await showEnter({
                ...this.formDia,
                taxesId: this.taxesId
            });
            if (res.data.code != 0) return false;
            this.tableData = res.data.data;
        },
        closeDia() {
            this.formDia.current = 1;
            this.formDia.name = '';
            this.$emit('closeDia');
        },
        async handleCurrentChange(current) {
            this.formDia.current = current;
            let res = await showEnter({
                ...this.formDia,
                taxesId: this.taxesId
            });
            if (res.data.code != 0) return false;
            this.tableData = res.data.data;
        },
        toLookDetail(id) {
            this.$router.push('/branch/enterpriseDetail?id=' + id);
        }
    },
    created() {}
};
</script>

<style scoped>
.rate_box {
    align-items: center;
    display: flex;
    margin-bottom: 10px;
}
.rate_box:last-child {
    margin-bottom: 0px;
}

.button_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.upload_box {
    width: 80%;
    display: flex;
    flex-wrap: wrap;
}
.icon_size {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    color: #fff;
    font-size: 18px;
    opacity: 0;
    transition: 0.4s;
}
.pic_list {
    position: relative;
    width: 146px;
    height: 146px;
    margin-right: 15px;
    margin-bottom: 15px;
}
.pic_list:hover .icon_size {
    opacity: 1;
}
</style>