<template>
    <div>
        <el-form label-width="120px" class="button_box">
            <el-form-item label="关键词搜索：">
                <el-input placeholder="请输入关键词" style="width: 200px" v-model="form.selectValue"></el-input>
                <el-button type="primary" style="margin-left: 20px;" size="medium" @click="toSeachList">搜 索</el-button>
            </el-form-item>
            <div style="flex: 1"></div>
            <el-form-item>
                <el-button type="primary" @click="toShowAdds" v-has="'platform_tax_role_add'" size="medium">新增角色</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
            <el-table-column prop="name" label="角色名称" min-width="120"></el-table-column>
            <el-table-column prop="userNumber" label="关联账号"></el-table-column>
            <el-table-column prop="remark" label="备注"></el-table-column>
            <el-table-column prop="createTime" label="创建时间"></el-table-column>
            <el-table-column label="操作" v-has="'platform_tax_role_add'">
                <template slot-scope="scope">
                    <el-button type="text" @click="toModifyRole(scope.row.id)" style="font-size: 15px;">修改</el-button>
                    <!-- <el-button type="text" @click="toJumpDetail(scope.row.id)">禁用</el-button> -->
                </template>
            </el-table-column>
        </el-table>
        <div class="pagination">
            <el-pagination
                @current-change="handleCurrentChange"
                :current-page="tableData.current"
                layout="prev, pager, next, jumper"
                :total="tableData.total"
            ></el-pagination>
        </div>
        <addRole :toShowDia="toShowDia" @addRole="addRoles" :roleId="roleId" @closeDia="closeDia" @updateRole="updateRoles"></addRole>
    </div>
</template>

<script>
import { listRole, selectTaxesRole, addRoleSure, updateRole } from '@/api/branch/taxes.js';
import addRole from './addRole.vue';
export default {
    components: {
        addRole
    },
    props: {
        basicInfo: {
            type: Object,
            require: true
        },
        aginRowList: {
            type: Boolean,
            require: false
        }
    },
    watch: {
        
    },
    data() {
        return {
            form: {
                current: 1,
                size: 10,
                selectValue: ''
            },
            belongId: '',
            tableData: {},
            toShowDia: false,
            roleId: '',
            belongIdIn:''
        };
    },
    created() {
        this.belongIdIn = this.basicInfo.id
        this.getData();
    },
    methods: {
        closeDia() {
            this.toShowDia = false;
        },
        getData() {
            listRole({
                ...this.form,
                belongId: this.basicInfo.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.tableData = res.data.data;
                }
            });
        },
        handleSizeChange(size) {
            this.form.size = size;
            this.form.current = 1;
            this.getData();
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.form.current = current;
            this.getData();
        },
        toSeachList() {
            this.form.current = 1;
            this.getData();
        },
        toShowAdds() {
            this.roleId = undefined;
            this.toShowDia = true;
        },
        addRoles(e) {
            addRoleSure({
                ...e,
                belongId: this.basicInfo.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.toShowDia = false;
                    this.form.current = 1;
                    this.getData();
                }
            });
        },
        toModifyRole(id) {
            this.roleId = id;
            this.toShowDia = true;
        },
        updateRoles(e) {
            updateRole(e).then((res) => {
                if (res.data.code == 0) {
                    this.toShowDia = false;
                    this.getData();
                }
            });
        }
    }
};
</script>

<style scoped>
.button_box {
    display: flex;
    align-items: center;
}
</style>