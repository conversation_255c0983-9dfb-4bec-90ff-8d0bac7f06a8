<template>
    <div>
        <el-dialog title="配置中心" :visible.sync="dialogVisible" width="50%" top="4%" @open="openDia" @close="closeDia">
            <el-tabs type="border-card" style="box-shadow: none;">
                <el-tab-pane label="微信配置">
                    <div class="project_top">
                        <div class="blue_box">企业公众号配置</div>
                    </div>
                    <el-form label-width="100px">
                        <el-form-item label="app_id:">
                            <el-input
                                placeholder="请输入app_id"
                                v-model="form.wxInfoService.appId"
                                style="width: 60%"
                                :disabled="disableInfo"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="app_key:">
                            <el-input
                                placeholder="请输入app_key"
                                v-model="form.wxInfoService.appKey"
                                style="width: 60%"
                                :disabled="disableInfo"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="secret:">
                            <el-input
                                placeholder="请输入secret"
                                v-model="form.wxInfoService.secret"
                                style="width: 60%"
                                :disabled="disableInfo"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="wx_token:">
                            <el-input
                                placeholder="请输入wx_token"
                                v-model="form.wxInfoService.wxToken"
                                style="width: 60%"
                                :disabled="disableInfo"
                            ></el-input>
                        </el-form-item>
                    </el-form>
                    <div class="project_top">
                        <div class="blue_box">员工公众号配置</div>
                    </div>
                    <el-form label-width="100px">
                        <el-form-item label="app_id:">
                            <el-input
                                placeholder="请输入app_id"
                                v-model="form.wxInfoEnterprise.appId"
                                style="width: 60%"
                                :disabled="disableInfo"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="app_key:">
                            <el-input
                                placeholder="请输入app_key"
                                v-model="form.wxInfoEnterprise.appKey"
                                style="width: 60%"
                                :disabled="disableInfo"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="secret:">
                            <el-input
                                placeholder="请输入secret"
                                v-model="form.wxInfoEnterprise.secret"
                                style="width: 60%"
                                :disabled="disableInfo"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="wx_token:">
                            <el-input
                                placeholder="请输入wx_token"
                                v-model="form.wxInfoEnterprise.wxToken"
                                style="width: 60%"
                                :disabled="disableInfo"
                            ></el-input>
                        </el-form-item>
                    </el-form>
                </el-tab-pane>
                <el-tab-pane label="域名配置">
                    <el-form label-width="120px">
                        <el-form-item label="税源地域名配置:">
                            <el-input placeholder="请输入..." v-model="form.dns.tax" style="width: 60%" :disabled="disableInfo"></el-input>
                        </el-form-item>
                        <el-form-item label="企业域名配置:">
                            <el-input
                                placeholder="请输入..."
                                v-model="form.dns.enterprise"
                                style="width: 60%"
                                :disabled="disableInfo"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="渠道域名配置:">
                            <el-input
                                placeholder="请输入..."
                                v-model="form.dns.channel"
                                style="width: 60%"
                                :disabled="disableInfo"
                            ></el-input>
                        </el-form-item>
                    </el-form>
                </el-tab-pane>
                <el-tab-pane label="模板ID配置">
                    <div class="project_top">
                        <div class="blue_box">企业公众号配置</div>
                    </div>
                    <el-form label-width="150px">
                        <el-form-item v-for="(item, index) in enterpriseAppIdList" :key="index" :label="item.remark">
                            <el-input
                                placeholder="请输入..."
                                v-model="item.templateCode"
                                style="width: 60%"
                                :disabled="disableInfo"
                            ></el-input>
                        </el-form-item>
                    </el-form>
                    <div class="project_top">
                        <div class="blue_box">员工公众号配置</div>
                    </div>
                    <el-form label-width="150px">
                        <el-form-item v-for="(item, index) in workAppIdList" :key="index" :label="item.remark">
                            <el-input
                                placeholder="请输入..."
                                v-model="item.templateCode"
                                style="width: 60%"
                                :disabled="disableInfo"
                            ></el-input>
                        </el-form-item>
                    </el-form>
                </el-tab-pane>
                <el-tab-pane label="logo配置">
                    <el-form label-width="120px">
                        <el-form-item label="logo上传：" prop="tp">
                            <div class="upload_box">
                                <div class="pic_list" v-for="item in fileLists" :key="item.url" v-show="fileLists[0].url">
                                    <el-image
                                        style="width: 146px; height: 146px; margin-right: 15px; border-radius: 5px; display: block"
                                        :src="ossUrl + item.url"
                                    >
                                    </el-image>
                                    <i class="el-icon-delete icon_size" @click="toRemovePic(item.url)"></i>
                                </div>
                                <el-upload
                                    action=""
                                    list-type="picture-card"
                                    :http-request="uploadURL"
                                    :on-success="uploadSuccess"
                                    :on-error="uploadError"
                                    :multiple="true"
                                    :show-file-list="false"
                                    :before-upload="handleBeforeUpload"
                                >
                                    <i class="el-icon-plus"></i>
                                </el-upload>
                            </div>
                        </el-form-item>
                    </el-form>
                    <el-form label-width="120px">
                        <el-form-item label="网站名称:">
                            <el-input
                                placeholder="请输入..."
                                v-model="logoData.label"
                                style="width: 60%"
                                :disabled="disableInfo"
                            ></el-input>
                        </el-form-item>
                    </el-form>
                </el-tab-pane>
            </el-tabs>
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeDia" size="medium">取 消</el-button>
                <!-- <el-button type="success" :disabled="disableInfo" @click="toSaveData" size="medium">保 存</el-button> -->
                <el-button type="primary" @click="toSureInfo" :disabled="disableInfo" size="medium">立 即 生 效</el-button>
                <el-button type="warning" @click="toAddShowDia" :disabled="disableInfo" size="medium">启 用 默 认 配 置</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { Message } from 'element-ui';
import { OSS_URL } from '@/api/config';
import {
    getOemTaxesBase,
    updateOrAddWXInfoById,
    startOrCloseWXInfoById,
    getWXInfoById,
    addOrUpdateTaxesLogo,
    addOrUpdateWXTemplate,
    getWXTemplateById,
    getTaxesLogoById
} from '@/api/branch/taxes.js';
import { client, getFileNameUUID, getTimeNow } from '../../../../../utils/oss.js';

export default {
    data() {
        return {
            dialogVisible: false,
            form: {
                wxInfoService: {
                    wxToken: '',
                    appId: '',
                    appKey: '',
                    secret: ''
                },
                wxInfoEnterprise: {
                    wxToken: '',
                    appId: '',
                    appKey: '',
                    secret: ''
                },
                dns: {
                    channel: '',
                    enterprise: '',
                    tax: ''
                }
            },
            wxConfigInfo: {},
            disableInfo: false,
            businessId: '26ff71ec665b4557b4fe64ff1f7d469d',
            wxConfigList: [],
            wxTemplateList: [],
            wxDnsList: [],
            state: null,
            fileLists: [{ url: '' }],
            ossUrl: OSS_URL,
            logoData: {
                label: ''
            },
            enterpriseAppIdList: [
                {
                    remark: '打款到账成功提醒：',
                    type: 4,
                    templateCode: ''
                },
                {
                    remark: '流程状态提醒：',
                    type: 7,
                    templateCode: ''
                },
                {
                    remark: '充值到账通知：',
                    type: 6,
                    templateCode: ''
                },
                {
                    remark: '流程审批提醒：',
                    type: 5,
                    templateCode: ''
                }
            ],
            workAppIdList: [
                {
                    remark: '电子合同签署提醒：',
                    type: 2,
                    templateCode: ''
                },
                {
                    remark: '团队加入成功：',
                    type: 1,
                    templateCode: ''
                }
            ]
        };
    },
    props: {
        toShowDia: {
            type: Boolean,
            require: true
        },
        oemId: {
            type: String,
            require: true
        }
    },
    watch: {
        toShowDia(val) {
            this.dialogVisible = val;
        },
        async oemId(val) {
            if (!val) return false;
            let res = await getWXInfoById({
                id: val
            });
            if (res.data.code != 0) return false;
            this.form = JSON.parse(JSON.stringify(res.data.data));
            // this.disableInfo = res.data.data.state == 1 ? true : false;
            let resTemp = await getWXTemplateById({
                enterpriseAppId: res.data.data.wxInfoService.appId,
                workerAppId: res.data.data.wxInfoEnterprise.appId
            });
            console.log(resTemp.data);
            resTemp.data.data.enterpriseAppId.forEach((s) => {
                this.enterpriseAppIdList.forEach((v) => {
                    if (s.type == v.type) {
                        v.templateCode = s.templateCode;
                    }
                });
            });
            resTemp.data.data.workerAppId.forEach((s) => {
                this.workAppIdList.forEach((v) => {
                    if (s.type == v.type) {
                        v.templateCode = s.templateCode;
                    }
                });
            });
            let resLogo = await getTaxesLogoById(val);
            if (resLogo.data.data) {
                this.fileLists[0].url = resLogo.data.data.logoUrl;
                this.logoData.label = resLogo.data.data.label;
            } else {
                this.fileLists = [{ url: '' }];
                this.logoData = { label: '' };
            }
        }
    },
    methods: {
        openDia() {},
        closeDia() {
            this.enterpriseAppIdList = [
                {
                    remark: '打款到账成功提醒：',
                    type: 4,
                    templateCode: ''
                },
                {
                    remark: '流程状态提醒：',
                    type: 7,
                    templateCode: ''
                },
                {
                    remark: '充值到账通知：',
                    type: 6,
                    templateCode: ''
                },
                {
                    remark: '流程审批提醒：',
                    type: 5,
                    templateCode: ''
                }
            ];
            this.workAppIdList = [
                {
                    remark: '电子合同签署提醒：',
                    type: 2,
                    templateCode: ''
                },
                {
                    remark: '团队加入成功：',
                    type: 1,
                    templateCode: ''
                }
            ];
            this.$emit('closeDia');
        },
        update() {
            console.log(this.form);
            this.$forceUpdate();
        },
        async toSureInfo() {
            for (var key in this.form.wxInfoService) {
                if (!Boolean(this.form.wxInfoService[key])) return this.$message.error('企业公众号' + key + '不能为空');
            }
            for (var key in this.form.wxInfoEnterprise) {
                if (!Boolean(this.form.wxInfoEnterprise[key])) return this.$message.error('员工公众号' + key + '不能为空');
            }
            for (var key in this.form.dns) {
                if (!Boolean(this.form.dns[key])) {
                    let keyStr = key == 'enterprise' ? '企业域名' : key == 'tax' ? '税源地域名' : '渠道域名';
                    return this.$message.error(keyStr + '不能为空');
                }
            }

            const h = this.$createElement;
            let res = await this.$msgbox({
                title: '提示',
                message: h('p', null, [
                    h('span', null, '请确认所有配置信息填写准确无误， 确认'),
                    h('span', { style: 'color: red' }, '生效后'),
                    h('span', null, '，配置信息'),
                    h('span', { style: 'color: red' }, '不可改动'),
                    h('span', null, '！')
                ]),
                showCancelButton: true,
                confirmButtonText: '确认生效',
                cancelButtonText: '取消'
            });
            if (res) {
                /**保存配置信息 */
                let resSaveWxConfig = await updateOrAddWXInfoById({
                    ...this.form,
                    state: 0,
                    businesId: this.oemId
                });
                /**保存消息推送配置 */
                let newObj = {
                    enterpriseAppId: this.form.wxInfoService.appId,
                    workerAppId: this.form.wxInfoEnterprise.appId
                };
                let enterKey = this.form.wxInfoService.appId;
                let workKey = this.form.wxInfoEnterprise.appId;
                this.$set(newObj, enterKey, this.enterpriseAppIdList);
                this.$set(newObj, workKey, this.workAppIdList);
                await addOrUpdateWXTemplate(newObj);
                await addOrUpdateTaxesLogo({
                    taxesId: this.oemId,
                    label: this.logoData.label,
                    logoUrl: this.fileLists[0].url
                });
                /**保存Logo配置 */
                let ress = await startOrCloseWXInfoById({ id: this.oemId, type: 1 });
                if (ress.data.code != 0) return false;
                Message.success('配置成功！');
                this.$emit('closeDia');
            }
        },
        async toAddShowDia() {
            const h = this.$createElement;
            let res = await this.$msgbox({
                title: '提示',
                message: h('p', null, [
                    h('span', null, '启用默认配置后，配置信息'),
                    h('span', { style: 'color: red' }, '不可改动'),
                    h('span', null, '！')
                ]),
                showCancelButton: true,
                confirmButtonText: '启用默认配置',
                cancelButtonText: '取消'
            });
            if (res) {
                let ress = await getOemTaxesBase();
                if (ress.data.code != 0) return false;
                let resss = await updateOrAddWXInfoById({
                    ...ress.data.data,
                    businesId: this.oemId
                });
                if (resss.data.code != 0) return false;
                let ressss = await startOrCloseWXInfoById({
                    id: this.oemId,
                    type: 1
                });
                if (ressss.data.code != 0) return false;
                Message.success('配置成功！');
                this.$emit('closeDia');
            }
        },
        async toSaveData() {
            let res = await updateOrAddWXInfoById({
                ...this.form,
                state: 0,
                businesId: this.oemId
            });
            if (res.data.code == 0) {
                Message.success('保存成功！');
            }
        },
        uploadSuccess(res) {
            console.log('success: ', res);
        },
        uploadError(err) {
            console.log('error: ', err);
            this.$message.error('上传失败！请重试');
        },
        uploadURL(option) {
            //注意哦，这里指定文件夹'image/'，尝试过写在配置文件，但是各种不行，写在这里就可以
            var suffix = option.file.name.substring(foptionile.file.name.lastIndexOf('.'));
            var fileName = '/oemLogo/' + getTimeNow() + '/' + getFileNameUUID() + suffix;
            //定义唯一的文件名，打印出来的uid其实就是时间戳
            client()
                .multipartUpload(fileName, option.file, {
                    headers: {
                        'Content-Type': 'image/jpg'
                    },
                    progress: function (percentage, cpt) {
                        option.onProgress({ percent: Math.floor(percentage * 100)});
                    }
                })
                .then((res) => {
                    if(res.res.status === 200) {
                        this.fileLists[0] = {
                            name: option.file.name,
                            url: fileName
                        };
                        this.$forceUpdate();
                        option.onSuccess(res);
                    }else {
                        option.onError(res);
                    }
                }).catch((err) => {
                    option.onError(err);
                });
        },
        toRemovePic(url) {
            this.fileLists.forEach((v, index) => {
                if (url == v.url) {
                    this.fileLists.splice(index, 1);
                }
            });
        },
        handleBeforeUpload(file) {
            const isJPEG = file.name.split('.')[1] === 'jpeg';
            const isJPG = file.name.split('.')[1] === 'jpg';
            const isPNG = file.name.split('.')[1] === 'png';
            const isWEBP = file.name.split('.')[1] === 'webp';
            const isGIF = file.name.split('.')[1] === 'gif';
            if (!isJPG && !isJPEG && !isPNG && !isWEBP && !isGIF) {
                this.$message.error('上传图片只能是 JPEG/JPG/PNG 格式!');
            }
            return isJPEG || isJPG || isPNG || isWEBP || isGIF;
        }
    },
    created() {}
};
</script>

<style scoped>
.rate_box {
    align-items: center;
    display: flex;
    margin-bottom: 10px;
}
.rate_box:last-child {
    margin-bottom: 0px;
}

.blue_box {
    padding: 8px 16px;
    background-color: #f9fafc;
    border-radius: 4px;
    border-left: 5px solid #7ca0e5;
    margin: 20px 0;
    width: 300px;
    color: #333;
    font-size: 16px;
}
.project_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.rate_box {
    align-items: center;
    display: flex;
    margin-bottom: 10px;
}
.rate_box:last-child {
    margin-bottom: 0px;
}

.button_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.upload_box {
    width: 80%;
    display: flex;
    flex-wrap: wrap;
}
.icon_size {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    color: #fff;
    font-size: 18px;
    opacity: 0;
    transition: 0.4s;
}
.pic_list {
    position: relative;
    width: 146px;
    height: 146px;
    margin-right: 15px;
    margin-bottom: 15px;
}
.pic_list:hover .icon_size {
    opacity: 1;
}
</style>
