<template>
    <div>
        <el-dialog :title="info ? '修改税源地' : '新增税源地'" :visible.sync="dialogVisible" width="60%" @open="openDia" @close="closeDia">
            <el-form label-width="200px" :rules="rules" :show-message="false" ref="form" :model="form">
                <el-form-item label="创建类型：" prop="type" >
                    <el-radio v-model="form.type" label="1" @change="toChangInfo">服务商</el-radio>
                    <el-radio v-model="form.type" label="2" @change="toChangInfo">税源地</el-radio>
                </el-form-item>
                <el-form-item label="税源地名称：" prop="name">
                    <el-input placeholder="请输入税源地名称" v-model="form.name" style="width: 250px"></el-input>
                </el-form-item>
                <el-form-item label="税源地简称（4-6字）：" prop="shortname">
                    <el-input placeholder="请输入税源地简称" v-model="form.shortname" style="width: 250px"></el-input>
                </el-form-item>
                <el-form-item label="联系人名称：">
                    <el-input placeholder="请输入税源地名称" v-model="form.contacts" style="width: 250px"></el-input>
                </el-form-item>
                <el-form-item label="详细地址：">
                    <el-input placeholder="详细地址" v-model="form.address" style="width: 250px"></el-input>
                </el-form-item>
                <el-form-item label="是否使用默认配置：" v-if="form.type == 1">
                    <el-radio v-model="form.isNone" label="0" @change="sureInfo">否</el-radio>
                    <el-radio v-model="form.isNone" label="1" @change="sureInfo">是</el-radio>
                </el-form-item>
                <el-form-item label="选择上级服务商：" v-show="form.type == 2">
                    <el-select v-model="form.parentId" style="width: 250px" filterable clearable @change="sureInfo">
                        <el-option v-for="item in listTaxesInfo" :key="item.id" :label="item.name" :value="item.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="印章编码：" prop="sealCode">
                    <el-input placeholder="请输入印章编码" v-model="form.sealCode" style="width: 250px"></el-input>
                </el-form-item>
                <el-form-item label="签署模板编码：" prop="templateCode">
                    <el-input
                        placeholder="请输入签署模板编码"
                        v-model="form.templateCode"
                        @input="toShowRight"
                        style="width: 250px"
                    ></el-input>
                </el-form-item>
                <el-form-item label="印章图片：" prop="tp">
                    <div class="upload_box">
                        <div class="pic_list" v-for="item in fileLists" :key="item.url" v-show="fileLists[0].url">
                            <el-image
                                style="width: 146px; height: 146px; margin-right: 15px; border-radius: 5px; display: block"
                                :src="ossUrl + item.url"
                            >
                            </el-image>
                            <i class="el-icon-delete icon_size" @click="toRemovePic(item.url)"></i>
                        </div>
                        <el-upload
                            action=""
                            list-type="picture-card"
                            :http-request="uploadURL"
                            :on-success="uploadSuccess"
                            :on-error="uploadError"
                            :multiple="true"
                            :show-file-list="false"
                            :before-upload="handleBeforeUpload"
                        >
                            <i class="el-icon-plus"></i>
                        </el-upload>
                    </div>
                </el-form-item>
                <el-alert title="印章编码可以去E签宝模板内复制" type="warning" show-icon :closable="false"> </el-alert>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeDia" size="medium">取 消</el-button>
                <el-button type="primary" @click="form.type == 1 ? toSubmitUpdata() : toSubmitInfo()" size="medium">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { channelList, taxesList } from '@/api/branch/channel.js';
import { listTaxes } from '@/api/charts/charts.js';
import { client, getFileNameUUID, getTimeNow } from '../../../../../utils/oss.js';
import { Message } from 'element-ui';
import { OSS_URL } from '@/api/config';
export default {
    data() {
        return {
            dialogVisible: false,
            form: {},
            channelPtions: [
                {
                    channelId: 0,
                    channelName: '无上级'
                }
            ],
            options: [],
            rateDetails: [
                {
                    minMoney: '',
                    maxMoney: '',
                    rate: ''
                }
            ],
            fileLists: [{}],
            ossUrl: OSS_URL,
            rules: {
                type: [{ required: true, trigger: 'blur' }],
                name: [{ required: true, trigger: 'blur' }],
                sealCode: [{ required: true, trigger: 'blur' }],
                templateCode: [{ required: true, trigger: 'blur' }],
                tp: [{ required: true }]
            },
            radio: '',
            listTaxesInfo: []
        };
    },
    props: {
        toShowDia: {
            type: Boolean,
            require: true
        },
        info: {
            type: Object,
            require: false
        },
        defaultCode: {
            type: String,
            require: false
        }
    },
    watch: {
        toShowDia(val) {
            this.dialogVisible = val;
        },
        info(val) {
            if (val) {
                this.form = JSON.parse(JSON.stringify(val));
                this.fileLists[0] = {
                    name: '图片',
                    url: val.sealUrl
                };
            }
        },
        defaultCode(val) {
            this.form.templateCode = val;
        }
    },
    methods: {
        toAddRateRow() {
            this.rateDetails.push({
                minMoney: '',
                maxMoney: '',
                rate: ''
            });
        },
        toShowRight() {
            this.$forceUpdate();
        },
        toDetelRate(index) {
            this.rateDetails.splice(index, 1);
        },
        openDia() {},
        closeDia() {
            this.$emit('closeDia');
        },
        toSubmitInfo() {
            if (!this.form.type) return Message.error('请选择创建类型！');
            if (!this.form.name) return Message.error('请输入税源地名称！');
            if (!this.form.sealCode) return Message.error('请输入印章编码！');
            if (!this.form.templateCode) return Message.error('请输入签署模板！');
            if (!this.fileLists[0].url) return Message.error('请上传印章图片！');
            let data = {
                ...this.form,
                sealUrl: this.fileLists[0].url
            };
            this.$confirm('是否确认新增税源地', '新增')
                .then((res) => {
                    this.$emit('addTax', data);
                    this.form = {}

                })
                .catch(() => {});
        },
        uploadSuccess(res) {
            console.log('success: ', res);
        },
        uploadError(err) {
            console.log('error: ', err);
            this.$message.error('上传失败！请重试');
        },
        uploadURL(option) {
            //注意哦，这里指定文件夹'image/'，尝试过写在配置文件，但是各种不行，写在这里就可以
            var suffix = option.file.name.substring(option.file.name.lastIndexOf('.'));
            var fileName = '/project/' + getTimeNow() + '/' + getFileNameUUID() + suffix;
            //定义唯一的文件名，打印出来的uid其实就是时间戳
            client()
                .multipartUpload(fileName, option.file, {
                    headers: {
                        'Content-Type': 'image/jpg'
                    },
                    progress: function (percentage, cpt) {
                        option.onProgress({ percent: Math.floor(percentage * 100)});
                    }
                })
                .then((res) => {
                    if(res.res.status === 200) {
                        this.fileLists[0] = {
                            name: option.file.name,
                            url: fileName
                        };
                        this.$forceUpdate();
                        option.onSuccess(res);
                    }else {
                        option.onError(res);
                    }
                }).catch((err) => {
                    option.onError(err);
                });
        },
        handleBeforeUpload(file) {
            const isJPEG = file.name.split('.')[1] === 'jpeg';
            const isJPG = file.name.split('.')[1] === 'jpg';
            const isPNG = file.name.split('.')[1] === 'png';
            const isWEBP = file.name.split('.')[1] === 'webp';
            const isGIF = file.name.split('.')[1] === 'gif';
            const isLt500K = file.size / 1024 / 1024 / 1024 / 1024 < 4;
            if (!isJPG && !isJPEG && !isPNG && !isWEBP && !isGIF) {
                this.$message.error('上传图片只能是 JPEG/JPG/PNG 格式!');
            }
            if (!isLt500K) {
                this.$message.error('单张图片大小不能超过 4mb!');
            }
            return (isJPEG || isJPG || isPNG || isWEBP || isGIF) && isLt500K;
        },
        toChangInfo() {
            this.form.parentId = undefined
            this.form.isNone = undefined
        },
        sureInfo(){
            this.$forceUpdate()
        },
        toSubmitUpdata() {
            if (!this.form.name) return Message.error('请输入税源地名称！');
            if (!this.form.sealCode) return Message.error('请输入印章编码！');
            if (!this.form.templateCode) return Message.error('请输入签署模板！');
            if (!this.form.isNone) return Message.error('请选择配置信息！');
            if (!this.fileLists[0].url) return Message.error('请上传印章图片！');
            let data = {
                ...this.form,
                sealUrl: this.fileLists[0].url
            };
            this.$confirm('是否确认创建服务商？', '修改')
                .then((res) => {
                    this.$emit('addTaxOem', data);
                    this.form = {}
                })
                .catch(() => {});
        }
    },
    created() {
        channelList().then((res) => {
            this.channelPtions = this.channelPtions.concat(res.data.data);
        });
        taxesList().then((res) => {
            this.options = res.data.data;
        });
        listTaxes().then((res) => {
            this.listTaxesInfo = res.data.data;
        });
    }
};
</script>

<style scoped>
.rate_box {
    align-items: center;
    display: flex;
    margin-bottom: 10px;
}
.rate_box:last-child {
    margin-bottom: 0px;
}

.button_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.upload_box {
    width: 80%;
    display: flex;
    flex-wrap: wrap;
}
.icon_size {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    color: #fff;
    font-size: 18px;
    opacity: 0;
    transition: 0.4s;
}
.pic_list {
    position: relative;
    width: 146px;
    height: 146px;
    margin-right: 15px;
    margin-bottom: 15px;
}
.pic_list:hover .icon_size {
    opacity: 1;
}
</style>