<template>
    <div>
        <el-form :inline="true" :model="form" class="demo-form-inline">
            <el-row>
                <el-col>
                    <el-form-item label="税地: ">
                        <el-select v-model="form.taxesId" placeholder="请选择" clearable style="width: 200px" filterable>
                            <el-option v-for="item in taxesList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="企业">
                        <el-select v-model="form.enterpriseId" placeholder="请选择" clearable filterable>
                            <el-option
                                :label="i.enterpriseName"
                                :value="i.enterpriseId"
                                v-for="i in listEnterprise"
                                :key="i.enterpriseId"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label='统计月份: '>
                        <el-date-picker
                            v-model="statMonth"
                            type="monthrange"
                            range-separator="至"
                            start-placeholder="开始月份"
                            end-placeholder="结束月份"
                            value-format="yyyy-MM"
                            clearable
                            @change="toChangeTime"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label='归档状态: '>
                        <el-select v-model="form.archiveStatus" placeholder="请选择" clearable style="width: 200px" filterable>
                            <el-option label="全部" value="-1"></el-option>
                            <el-option label="未归档" value="0"></el-option>
                            <el-option label="已归档" value="1"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" icon="el-icon-search" size="medium" @click="toSearch">查 询</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div>
            <el-table
                :data="tableData.records"
                style="width: 100%; color: rgba(0, 0, 0, 0.65)" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell"
                v-loading="loading"
            >
                <el-table-column prop="name" min-width="200" label="企业" show-overflow-tooltip fixed="left">
                    <template slot-scope="scope">
                        <div>{{ scope.row.enterpriseName }}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="account" min-width="200" label="税地">
                    <template slot-scope="scope">
                        <span>{{ scope.row.taxesName }}</span>
                    </template>
                </el-table-column>
                <el-table-column width="120" label="月份" align="center">
                    <template slot-scope="scope">
                        <span>{{ scope.row.archiveMonth }}</span>
                    </template>
                </el-table-column>
                <el-table-column min-width="250" label="所属行业/类目" align="center">
                    <template slot-scope="scope">
                        <div v-for="(category, index) in scope.row.invoiceCategories" :key="index">
                            <span>{{ category }}</span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column width="150" label="服务确认单已归档" align="center">
                    <template slot-scope="scope">
                        <span>{{ scope.row.uri ? '是' : '否'}}</span>
                    </template>
                </el-table-column>
                <el-table-column width="120" label="归档时间" align="center">
                    <template slot-scope="scope">
                        <div v-if="scope.row.uploadTime == '' || scope.row.uploadTime == null">无</div>
                        <div v-else v-for="(item, index) in formatDate(scope.row.uploadTime)" :key="index">
                            <span>{{ item }}</span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed='right' width="250" align="center">
                    <template slot-scope='scope'>
                        <el-button v-if="scope.row.uri" type="text" style="font-size: 14px;" @click="viewFile(scope.row.uri)">查看确认单</el-button>
                        <el-button type="text" style="font-size: 14px;" @click="showUploadDialog(scope.row)">{{ scope.row.uri ? '更新确认单' : '上传确认单' }}</el-button>
                        <el-button v-if="scope.row.acceptanceConfirmationUrl" type="text" style="font-size: 14px" @click="viewFile(scope.row.acceptanceConfirmationUrl)">待盖章确认单下载</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page="tableData.current"
                    :page-size="tableData.size"
                    layout="prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
        </div>
        <el-dialog title="上传服务确认单" :visible.sync="uploadDialogVisible">
            <el-form :model="serviceConfirmationFiles">
                <el-form-item label="服务确认单：" prop="files">
                    <el-upload
                        class="file-upload"
                        action=""
                        :limit="1"
                        :http-request="uploadFiles"
                        :before-upload="beforeUpload"
                        :on-success="uploadSuccess"
                        :on-error="uploadError"
                        :on-remove="fileRemove"
                        :on-cancel="fileRemove"
                        :file-list="serviceConfirmationFiles.files"
                        accept=".doc,.docx,.pdf,.jpg,.jpeg,.png,.zip,.rar,.7z"
                        list-type="">
                        <el-button slot="trigger" size="small" type="primary">点击上传</el-button>
                        <div slot="tip" class="el-upload__tip">可上传jpg、png、pdf、Word、Excel、zip和rar等文件格式, 且不超过10MB</div>
                    </el-upload>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="uploadDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="saveFile">保 存</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { getTaxesList } from '@/api/personal/personal.js';
import { listEnterpriseList } from '@/api/grant/grant';
import { pageEnterpriseServiceConfirmationArchive, updateEnterpriseServiceConfirmationRecords } from '@/api/account/account';
import { fileUpload } from '@/api/system/user.js';
import { cdnHost } from '@/config/env.js'
import { Message } from 'element-ui'

export default {
    data() {
        return {
            form: {
                enterpriseId: "",
                taxesId: ""
            },
            statMonth: [],
            listEnterprise: [],
            taxesList: [],
            tableData: {
                current: 1,
                size: 10,
                total: 0,
                records: []
            },
            loading: false,
            currentRow: {},
            uploadDialogVisible: false,
            serviceConfirmationFiles: {
                files: []
            }
        };
    },
    async created() {
        listEnterpriseList().then((res) => {
            if (res.data.code === 0) {
                this.listEnterprise = res.data.data;
            }
        });
        getTaxesList().then((res) => {
            if (res.data.code == 0) {
                this.taxesList = res.data.data;
            }
        });
        this.getData();
    },
    methods: {
        getData() {
            this.loading = true;
            pageEnterpriseServiceConfirmationArchive({
                ...this.form,
                current: this.tableData.current,
                size: this.tableData.size,
            }).then((res) => {
                if (res.data.code === 0) {
                    this.tableData = res.data.data;
                }
                this.loading = false;
            }).catch(() => {
                this.loading = false;
            });
        },
        toSearch() {
            this.tableData.current = 1;
            this.getData();
        },
        toChangeTime(date) {
            if (date) {
                this.form.startMonth = date[0];
                this.form.endMonth = date[1];
            } else {
                this.form.startMonth = '';
                this.form.endMonth = '';
            }
        },
        formatDate(dateTimeString) {
            if(!dateTimeString) return '';
            // 假设日期格式为 "YYYY-MM-DD HH:mm:ss"
            const [date, time] = dateTimeString.split(' ');
            const timeWithoutSeconds = time.slice(0, -3); // 去掉最后的 ":ss"
            return [date, timeWithoutSeconds];
        },
        handleCurrentChange(val) {
            this.tableData.current = val;
            this.getData();
        },
        beforeUpload(file) {
            if(file.size > 10 * 1024 * 1024 ) {
                Message.error('文件大小不能超过10MB');
                return false;
            }
        },
        uploadFiles(option) {
            const _this = this;
            const file = option.file;
            let formData = new FormData();
            formData.append('file', file);
            formData.append('path', 'enterpriseServiceConfirmationFile');

            fileUpload(formData).then((res) => {
                if (res != undefined && res.data.code == 0) {
                    let new_file = {
                        name: file.name,
                        response: res,
                        percentage: 0,
                        raw: file,
                        size: file.size,
                        status: 'success',
                        uid: file.uid,
                        url: res.data.data
                    }
                    option.onSuccess(res, new_file);
                } else {
                    option.onError(res, file);
                }
            });
        },
        uploadSuccess(res, file, fileList) {
            if (res.status == 200 && res.data.code == 0) {
                Message.success('上传成功');
                file.url = res.data.data;
                this.serviceConfirmationFiles.files = [file];
            } else {
                Message.error('上传失败');
            }
        },
        uploadError(err, file, fileList) {
            Message.error('上传失败');
        },
        fileRemove(file, fileList) {
            this.serviceConfirmationFiles.files = fileList;
        },
        viewFile(uri) {
            window.open(cdnHost + uri, '_blank');
        },
        showUploadDialog(row) {
            this.uploadDialogVisible = true;
            this.currentRow = row;
        },
        saveFile() {
            console.log(this.currentRow);
            updateEnterpriseServiceConfirmationRecords(
                this.currentRow.id,
                this.serviceConfirmationFiles.files[0].url
            ).then((res) => {
                if (res.data.code === 0) {
                    this.$message.success('保存成功');
                    this.uploadDialogVisible = false;
                    this.getData();
                } else {
                    this.$message.error('保存失败');
                }
            });
        }
    },
    watch: {
        uploadDialogVisible(val) {
            if (!val) {
                this.serviceConfirmationFiles.files = [];
                this.currentRow = {};
            }
        }
    }
};
</script>
