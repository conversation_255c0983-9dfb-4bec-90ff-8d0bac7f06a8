<template>
    <div>
        <el-form :inline="true" :model="form" class="demo-form-inline">
            <el-row>
                <el-form-item label="税地: ">
                    <el-select v-model="form.taxesId" placeholder="请选择" clearable style="width: 200px" filterable>
                        <el-option v-for="item in taxesList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="企业: ">
                    <el-select v-model="form.enterpriseId" placeholder="请选择" clearable filterable>
                        <el-option
                            :label="i.enterpriseName"
                            :value="i.enterpriseId"
                            v-for="i in listEnterprise"
                            :key="i.enterpriseId"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label='统计月份: '>
                    <el-date-picker
                        v-model="statMonth"
                        type="monthrange"
                        range-separator="至"
                        start-placeholder="开始月份"
                        end-placeholder="结束月份"
                        value-format="yyyy-MM"
                        clearable
                        @change="toChangeTime"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item label='签约状态: '>
                    <el-select v-model="form.signFlag" placeholder="请选择" style="width: 200px" filterable>
                        <el-option label="全部" :value="-1"></el-option>
                        <el-option label="未签约" :value="0"></el-option>
                        <el-option label="已签约" :value="1"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label='外呼状态: '>
                    <el-select v-model="form.recallStatus" placeholder="请选择" style="width: 200px" filterable>
                        <el-option label="全部" :value="-2"></el-option>
                        <el-option label="没有外呼记录" :value="-1"></el-option>
                        <el-option label="未接通" :value="0"></el-option>
                        <el-option label="接通，需复核" :value="1"></el-option>
                        <el-option label="接通，抽检合格" :value="2"></el-option>
                    </el-select>
                </el-form-item>
            </el-row>
            <el-row>
                <el-form-item label="姓名: ">
                    <el-input v-model="form.workerName" placeholder="姓名" style="width: 200px"></el-input>
                </el-form-item>
                <el-form-item label="手机号: ">
                    <el-input v-model="form.phone" placeholder="手机号" style="width: 200px"></el-input>
                </el-form-item>
                <el-form-item label="身份证号: ">
                    <el-input v-model="form.idCardNumber" placeholder="身份证号" style="width: 200px"></el-input>
                </el-form-item>
                <el-form-item label="发放金额: ">
                    <el-select v-model="form.payMoneySymbol" placeholder="请选择" style="width: 100px;">
                        <el-option label="大于" :value="1"></el-option>
                        <el-option label="小于" :value="2"></el-option>
                    </el-select>
                    <el-input v-model="form.payMoney" placeholder="请输入数字金额" style="width: 200px"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="medium" @click="toSearch">查 询</el-button>
                    <el-button type="info" icon="el-icon-receiving" size="medium" @click="toExportExcel">导出</el-button>
                </el-form-item>
            </el-row>
        </el-form>
        <div>
            <el-table
                :data="tableData.records"
                style="width: 100%; color: rgba(0, 0, 0, 0.65)" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell"
                v-loading="loading"
            >
                <el-table-column prop="name" min-width="200" label="企业" show-overflow-tooltip fixed="left">
                    <template slot-scope="scope">
                        <div>{{ scope.row.enterpriseName }}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="account" min-width="200" label="税源地">
                    <template slot-scope="scope">
                        <span>{{ scope.row.taxesName }}</span>
                    </template>
                </el-table-column>
                <el-table-column width="100" label="姓名" align="center">
                    <template slot-scope="scope">
                        <span>{{ scope.row.workerName }}</span>
                    </template>
                </el-table-column>
                <el-table-column width="130" label="手机号" align="center">
                    <template slot-scope="scope">
                        <span>{{ scope.row.phone }}</span>
                    </template>
                </el-table-column>
                <el-table-column width="200" label="身份证号" align="center">
                    <template slot-scope="scope">
                        <span>{{ scope.row.idCardNumber }}</span>
                    </template>
                </el-table-column>
                <el-table-column width="130" label="当月发放总额" align="center">
                    <template slot-scope="scope">
                        <span>{{ scope.row.payMoney }}</span>
                    </template>
                </el-table-column>
                <el-table-column width="130" label="签约状态" align="center">
                    <template slot-scope="scope">
                        <span>{{ scope.row.signFlag ? '已签约' : '待签约'}}</span>
                    </template>
                </el-table-column>
                <el-table-column width="130" label="外呼状态" align="center">
                    <template slot-scope="scope">
                        <span>{{ scope.row.recallStatus == null ? '没有外呼记录' : scope.row.recallStatus == 0 ? '未接通' : scope.row.recallStatus == 1 ? '接通，需复核' : '接通，抽检合格' }}</span>
                    </template>
                </el-table-column>
                <el-table-column width="120" label="统计月份" align="center">
                    <template slot-scope="scope">
                        <span>{{ scope.row.statMonth }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed='right' width="200" align="center">
                    <template slot-scope='scope'>
                        <el-button v-if="scope.row.jobConfirmVoucherUrl" type="text" style="font-size: 15px" @click="viewFile(scope.row.jobConfirmVoucherUrl)">下载任务接受确认单</el-button>
                        <el-button v-if="scope.row.serviceSettlementVoucherUrl" type="text" style="font-size: 15px" @click="viewFile(scope.row.serviceSettlementVoucherUrl)">下载服务结算确认单</el-button>
                        <el-button type="text" style="font-size: 15px;" @click="showCallbackDialog(scope.row)">{{ scope.row.logId == null ? '新增外呼记录' : '更新外呼记录' }}</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page="tableData.current"
                    :page-size="tableData.size"
                    layout="prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
        </div>
        <el-dialog title="外呼记录" :visible.sync="callbackDialogVisible" width="30%">
            <el-form :model="callbackLog">
                <el-form-item label="外呼日期：" prop="callDay">
                    <el-date-picker
                        v-model="callbackLog.callDay"
                        type="date"
                        placeholder="选择日期"
                        style="width: 100%;"
                        value-format="yyyy-MM-dd"
                    ></el-date-picker>
                </el-form-item>
                <el-form-item label="是否接通: ">
                    <el-select v-model="callbackLog.isConnect" placeholder="请选择" style="width: 100%;">
                        <el-option label="未接通" :value="0"></el-option>
                        <el-option label="接通，需复核" :value="1"></el-option>
                        <el-option label="接通，抽检合格" :value="2"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="是否明确服务内容: " prop="serviceCheck">
                    <el-switch
                        v-model="callbackLog.serviceCheck"
                        active-text="明确"
                        inactive-text="不了解">
                    </el-switch>
                </el-form-item>
                <el-form-item label="是否获得服务费: " prop="feeGet">
                    <el-switch
                        v-model="callbackLog.feeGet"
                        active-text="已获得"
                        inactive-text="未获得">
                    </el-switch>
                </el-form-item>
                <el-form-item label="金额是否与应得一致: " prop="feeCheck">
                    <el-switch
                        v-model="callbackLog.feeCheck"
                        active-text="一直"
                        inactive-text="不一致">
                    </el-switch>
                </el-form-item>
                <el-form-item label="是否存在劳动关系: " prop="hasLaborRelation">
                    <el-switch
                        v-model="callbackLog.hasLaborRelation"
                        active-text="有"
                        inactive-text="无">
                    </el-switch>
                </el-form-item>
                <el-form-item label="备注: " prop="remark">
                    <el-input v-model="callbackLog.remark" type="textarea" placeholder="请输入备注" style="width: 100%;" rows="5"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="callbackDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="saveCallbackLog">保 存</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { getTaxesList } from '@/api/personal/personal.js';
import { listEnterpriseList, getWorkerSalaryMonthlyPage, saveWorkerCallbackLog, getWorkerCallbackLog, exportWorkerSalaryMonthlyList } from '@/api/grant/grant';
import { cdnHost } from '@/config/env';

export default {
    data() {
        return {
            form: {
                enterpriseId: "",
                taxesId: "",
                payMoneySymbol: 1,
                signFlag: -1,
                recallStatus: -2,
            },
            statMonth: [],
            listEnterprise: [],
            taxesList: [],
            tableData: {
                current: 1,
                size: 10,
                total: 0,
                records: []
            },
            loading: false,
            currentRow: {},
            callbackLog: {
                id: '',
                callDay: '',
                isConnect: 0,
                serviceCheck: 0,
                feeGet: 0,
                feeCheck: 0,
                hasLaborRelation: 0,
                remark: ''
            },
            callbackDialogVisible: false,
        };
    },
    created() {
        listEnterpriseList().then((res) => {
            if (res.data.code === 0) {
                this.listEnterprise = res.data.data;
            }
        });
        getTaxesList().then((res) => {
            if (res.data.code == 0) {
                this.taxesList = res.data.data;
            }
        });
        this.getData();
    },
    methods: {
        getData() {
            this.loading = true;
            getWorkerSalaryMonthlyPage({
                ...this.form,
                current: this.tableData.current,
                size: this.tableData.size,
            }).then((res) => {
                if (res.data.code === 0) {
                    this.tableData = res.data.data;
                }
                this.loading = false;
            }).catch(() => {
                this.loading = false;
            });
        },
        toChangeTime(date) {
            if (date) {
                this.form.startMonth = date[0];
                this.form.endMonth = date[1];
            } else {
                this.form.startMonth = '';
                this.form.endMonth = '';
            }
        },
        toSearch() {
            this.tableData.current = 1;
            this.getData();
        },
        handleCurrentChange(val) {
            this.tableData.current = val;
            this.getData();
        },
        showCallbackDialog(row) {
            if(row.logId) {
                getWorkerCallbackLog(row.logId).then((res) => {
                    if (res.data.code === 0) {
                        this.callbackLog = res.data.data;
                        this.callbackDialogVisible = true;
                        this.currentRow = row;
                    }
                }).catch(() => {
                });
            } else {
                this.callbackDialogVisible = true;
                this.currentRow = row;
            }
        },
        saveCallbackLog() {
            saveWorkerCallbackLog({
                ...this.callbackLog,
                workerSalaryStatId: this.currentRow.id
            }).then((res) => {
                if (res.data.code === 0) {
                    this.$message.success('保存成功');
                    this.callbackDialogVisible = false;
                    this.getData();
                }
            }).catch(() => {
                this.$message.error('保存失败，请重试');
            });
        },
        async toExportExcel() {
            this.$confirm('确认按照当前查询条件导出所有记录吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                let res = await exportWorkerSalaryMonthlyList(this.form);
                console.log(res);
                if (res.data.code !== 0) {
                    console.log(res.data.msg);
                    this.$message.error(res.data.msg);
                    return false;
                }
                this.$confirm('已加入下载队列，是否前往下载中心查看?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'success'
                }).then(() => {
                    this.$router.push('/system/downloadCenter');
                });
            }).catch(() => {
                // 取消导出
            });
        },
        viewFile(uri) {
            window.open(cdnHost + uri, '_blank');
        },
    },
    watch: {
        callbackDialogVisible(val) {
            if (!val) {
                this.callbackLog = {
                    id: '',
                    callDay: '',
                    isConnect: 0,
                    serviceCheck: false,
                    feeGet: false,
                    feeCheck: false,
                    hasLaborRelation: false,
                    remark: ''
                };
                this.currentRow = {};
            }
        }
    }
};
</script>
