<template>
    <div>
        <el-form :inline="true" :model="form" class="demo-form-inline">
            <el-row>
                <el-form-item label="税地：">
                    <el-select v-model="form.taxesId" placeholder="请选择" clearable style="width: 200px" filterable>
                        <el-option v-for="item in taxesList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="企业：">
                    <el-select v-model="form.enterpriseId" placeholder="选择机构" clearable filterable>
                        <el-option
                            :label="i.enterpriseName"
                            :value="i.enterpriseId"
                            v-for="i in listEnterprise"
                            :key="i.enterpriseId"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="缺失材料：">
                    <el-select v-model="form.missingAttachmentType" clearable placeholder="请选择">
                        <el-option :value="0" label="全部"> </el-option>
                        <el-option :value="10" label="合同缺失"> </el-option>
                        <el-option :value="11" label="营业执照缺失"> </el-option>
                        <el-option :value="12" label="社保、完税证明"> </el-option>
                        <el-option :value="13" label="门头照缺失"> </el-option>
                        <el-option :value="14" label="办公室场景照"> </el-option>
                        <el-option :value="15" label="用工场景照"> </el-option>
                    </el-select>
                </el-form-item>
            </el-row>
            <el-row>
                <el-form-item label='月份：'>
                    <el-date-picker
                        v-model="form.date"
                        type="daterange"
                        range-separator="~"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd"
                        clearable
                        @change="toChangeTime"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="medium" @click="toSearch">查 询</el-button>
                </el-form-item>
            </el-row>
        </el-form>
        <div>
            <el-table
                :data="tableData.records"
                style="width: 100%; color: rgba(0, 0, 0, 0.65)" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell"
                v-loading="loading"
            >
                <el-table-column
                    prop="enterpriseName"
                    label="企业"
                    min-width="200"
                >
                </el-table-column>
                <el-table-column
                    prop="hasBusinessLicence"
                    label="营业执照缺失"
                    width="120"
                    align="center"
                >
                    <template slot-scope="scope">
                        <span :style="{color: scope.row.hasBusinessLicence ? '' : 'red' }">{{ scope.row.hasBusinessLicence ? '否' : '是' }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="hasContract"
                    label="合同缺失"
                    width="100"
                    align="center"
                >
                    <template slot-scope="scope">
                        <span :style="{color: scope.row.hasContract ? '' : 'red' }">{{ scope.row.hasContract ? '否' : '是' }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="hasDoorPic"
                    label="门头照缺失"
                    width="100"
                    align="center"
                >
                    <template slot-scope="scope">
                        <span :style="{color: scope.row.hasDoorPic ? '' : 'red' }">{{ scope.row.hasDoorPic ? '否' : '是' }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="hasSocialSecurity"
                    label="完税证明缺失"
                    width="120"
                    align="center"
                >
                    <template slot-scope="scope">
                        <span :style="{color: scope.row.hasSocialSecurity ? '' : 'red' }">{{ scope.row.hasSocialSecurity ? '否' : '是' }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="others"
                    label="其它材料缺失"
                    width="120"
                    align="center"
                >
                    <template slot-scope="scope">
                        <span :style="{color: (scope.row.hasOfficeScenePhoto && scope.row.hasEmploymentScenePhoto) ? '' : 'red' }">{{ 2 - (scope.row.hasOfficeScenePhoto ? 1 : 0) - (scope.row.hasEmploymentScenePhoto ? 1 : 0) }} 项</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="enterpriseCreateTime"
                    label="开户日期"
                    width="170"
                    align="center"
                >
                </el-table-column>
                <el-table-column label="操作" fixed='right' width="100" align="center">
                    <template slot-scope='scope'>
                        <el-button type="text" style="font-size: 15px;" @click="viewDetail(scope.row.enterpriseId)">企业详情</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page="tableData.current"
                    :page-size="tableData.size"
                    layout="prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
        </div>
    </div>
</template>


<script>
import {
    getTaxesList
} from '@/api/personal/personal.js';
import { listEnterpriseList } from '@/api/grant/grant';
import { getAttachmentsSituations } from '@/api/branch/enterprise';

export default {
    data() {
        return {
            form: {
                taxesId: '',
                enterpriseId: '',
                startDate: '',
                endDate: '',
                missingAttachmentType: 0
            },
            listEnterprise: [],
            taxesList: [],
            tableData: {
                current: 1,
                size: 10,
                total: 0,
                records: []
            },
            loading: false
        };
    },
    async created() {
        getTaxesList().then((res) => {
            if (res.data.code == 0) {
                this.taxesList = res.data.data;
            }
        });
        listEnterpriseList().then((res) => {
            if (res.data.code === 0) {
                this.listEnterprise = res.data.data;
            }
        });
        this.getAttachmentsSituations();
    },
    methods: {
        toSearch() {
            this.tableData.current = 1;
            this.getAttachmentsSituations();
        },
        getAttachmentsSituations() {
            getAttachmentsSituations({
                ...this.form,
                current: this.tableData.current,
                size: this.tableData.size
            }).then((res) => {
                if (res.data.code === 0) {
                    this.tableData = res.data.data;
                }
            });
        },
        handleCurrentChange(val) {
            this.tableData.current = val;
            this.getAttachmentsSituations();
        },
        viewDetail(enterpriseId) {
            this.$router.push({
                path: '/branch/enterpriseDetail',
                query: {
                    id: enterpriseId
                }
            });
        },
        toChangeTime(date) {
            if (date) {
                this.form.startDate = date[0] + ' ' + '00:00:00';
                this.form.endDate = date[1] + ' ' + '23:59:59';
            } else {
                this.form.startDate = '';
                this.form.endDate = '';
            }
        },
    }
}
</script>
