<template>
    <div>
        <el-form :inline="true" :model="form" class="demo-form-inline">
            <el-row>
                <el-col>
                    <el-form-item label="税地：">
                        <el-select v-model="form.taxesId" placeholder="请选择" clearable style="width: 200px" filterable>
                            <el-option v-for="item in taxesList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label='月份：'>
                        <el-select v-model="form.yearMonth" placeholder="请选择" clearable style="width: 200px" filterable>
                            <el-option v-for="item in last12Month" :key="item" :label="item" :value="item"> </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" icon="el-icon-search" size="medium" @click="toSearch">查 询</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div>
            <el-table
                :data="tableData.records"
                style="width: 100%; color: rgba(0, 0, 0, 0.65)" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell"
                v-loading="loading">
                <el-table-column
                    prop="taxesName"
                    label="税地"
                    min-width="200"
                >
                </el-table-column>
                <el-table-column
                    prop="enterpriseCount"
                    label="企业总数"
                    width="120"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="businessLicenseMissingCount"
                    label="营业执照缺失"
                    width="140"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="contractMissingCount"
                    label="合同缺失"
                    width="120"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="doorPhotoMissingCount"
                    label="门头照缺失"
                    width="120"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="taxProofMissingCount"
                    label="社保、完税证明"
                    width="180"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="employmentSensePhotoMissingCount"
                    label="用工场景照"
                    width="120"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="officePhotoMissingCount"
                    label="办公场景照"
                    width="120"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="statMonth"
                    label="统计月"
                    width="120"
                    align="center"
                >
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page="tableData.current"
                    :page-size="tableData.size"
                    layout="prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
        </div>
    </div>
</template>


<script>
import {
    getTaxesList
} from '@/api/personal/personal.js';

import { listAttachmentStatistics } from '@/api/branch/enterprise.js'

export default {
    data() {
        return {
            form: {
                taxesId: '',
                yearMonth: '',
            },
            taxesList: [],
            tableData: {
                records: [],
                current: 1,
                size: 10,
                total: 0
            },
            loading: false,
            last12Month: []
        };
    },
    async created() {
        getTaxesList().then((res) => {
            if (res.data.code == 0) {
                this.taxesList = res.data.data;
            }
        });
        this.getAttachmentsStatistics();
        this.getLast12Month();
    },
    methods: {
        toChangeTime(date) {
            if (date) {
                this.form.startDate = date[0] + ' ' + '00:00:00';
                this.form.endDate = date[1] + ' ' + '23:59:59';
            } else {
                this.form.startDate = '';
                this.form.endDate = '';
            }
        },
        toSearch() {
            this.tableData.current = 1;
            this.getAttachmentsStatistics();
        },
        getAttachmentsStatistics() {
            this.loading = true;
            listAttachmentStatistics({
            ...this.form,
            current: this.tableData.current,
            size: this.tableData.size
        }).then((res) => {
            if (res.data.code == 0) {
                this.tableData = res.data.data
            }
            this.loading = false;
        })
        },
        handleCurrentChange(val) {
            this.tableData.current = val;
            this.getAttachmentsStatistics();
        },
        getLast12Month() {
            // get last 12 month, format each as 'YYYY-mm'
            const date = new Date();
            let year = date.getFullYear();
            let month = date.getMonth() + 1;
            for (let i = 0; i < 12; i++) {
                this.last12Month.push(`${year}-${month < 10 ? '0' + month : month}`);
                if (month === 1) {
                    month = 12;
                    year--;
                } else {
                    month--;
                }
            }
        }
    }
}
</script>