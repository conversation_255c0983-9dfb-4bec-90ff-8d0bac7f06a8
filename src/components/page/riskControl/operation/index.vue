<template>
    <div>
        <div>
            <el-card shadow="never">
                <el-tabs style="box-shadow: none;" class="tab-title-lg">
                    <el-tab-pane label="企业材料统计">
                        <statistics></statistics>
                    </el-tab-pane>
                    <el-tab-pane label="企业材料缺失查询">
                        <list></list>
                    </el-tab-pane>
                </el-tabs>
                
            </el-card>
        </div>
    </div>
</template>

<script>
import list from './components/attachment/list.vue';
import statistics from './components/attachment/statistics.vue';
export default {
    components: {
        list,
        statistics
    }
}
</script>