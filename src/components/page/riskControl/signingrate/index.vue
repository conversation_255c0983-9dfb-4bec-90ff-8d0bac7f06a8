<template>
    <div>
        <div>
            <el-card shadow="never">
                <el-tabs @tab-click="handleClick" style="box-shadow: none;" class="tab-title-lg">
                    <el-tab-pane label="签约率统计">
                        <statistics></statistics>
                    </el-tab-pane>
                    <el-tab-pane label="签约率查询">
                        <list></list>
                    </el-tab-pane>
                </el-tabs>
                
            </el-card>
        </div>
    </div>
</template>

<script>
import list from './components/list.vue';
import statistics from './components/statistics.vue';
export default {
    components: {
        list,
        statistics
    }
}
</script>