<template>
    <div>
        <el-form :inline="true" :model="Form" class="demo-form-inline">
            <el-row>
                <el-col :span='14'>
                    <el-form-item label="签约税地：">
                        <el-select v-model="Form.taxesId" placeholder="请选择" clearable style="width: 200px" filterable>
                            <el-option v-for="item in taxesList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="合作机构：">
                        <el-select v-model="Form.enterpriseId" placeholder="选择机构" clearable filterable>
                            <el-option
                                :label="i.enterpriseName"
                                :value="i.enterpriseId"
                                v-for="i in listEnterprise"
                                :key="i.enterpriseId"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label='统计月份：'>
                        <el-date-picker
                            v-model="statMonth"
                            type="monthrange"
                            range-separator="至"
                            start-placeholder="开始月份"
                            end-placeholder="结束月份"
                            value-format="yyyy-MM"
                            clearable
                            @change="toChangeTime"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="签约率：">
                        <el-select v-model="Form.signRateRange" clearable placeholder="请选择" @change="toChangeRateRange">
                            <el-option :value="1" label="0%"> </el-option>
                            <el-option :value="2" label="0到30%"> </el-option>
                            <el-option :value="3" label="30%至50%"> </el-option>
                            <el-option :value="4" label="50%至80%"> </el-option>
                            <el-option :value="5" label="大于80%"> </el-option>
                            <el-option :value="6" label="100%"> </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" icon="el-icon-search" size="medium" @click="toSearch">查 询</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div>
            <el-table
                :data="tableData.results"
                style="width: 100%; color: rgba(0, 0, 0, 0.65)" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell"
                v-loading="loading"
                @selection-change="handleSelectionChange"
            >
                <el-table-column prop="name" min-width="220" label="企业" show-overflow-tooltip fixed="left">
                    <template slot-scope="scope">
                        <div style='cursor: pointer'>{{ scope.row.enterpriseName }}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="account" min-width="220" label="税地">
                    <template slot-scope="scope">
                        <span>{{ scope.row.taxesName }}</span>
                    </template>
                </el-table-column>
                <el-table-column width="120" label="月份" align="center">
                    <template slot-scope="scope">
                        <span>{{ scope.row.statMonth }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="发放数" align="center">
                    <template slot-scope="scope">
                        <span>{{ scope.row.workerBillCount}}</span>
                    </template>
                </el-table-column>
                <el-table-column width="120" label="发放签约数" align="center">
                    <template slot-scope="scope">
                        <span>{{ scope.row.workerSignCount}}</span>
                    </template>
                </el-table-column>
                <el-table-column width="120" label="发放签约率" align="center">
                    <template slot-scope="scope">
                        <span>{{ scope.row.signRate}}%</span>
                    </template>
                </el-table-column>
                <el-table-column width="150" label="涉及人数（去重）" align="center">
                    <template slot-scope="scope">
                        <span>{{ scope.row.workerCount}}</span>
                    </template>
                </el-table-column>
                <el-table-column width="120" label="当月签约数" align="center">
                    <template slot-scope="scope">
                        <span>{{ scope.row.workerSignCountCurrent}}</span>
                    </template>
                </el-table-column>
                <el-table-column width="120" label="当月签约率" align="center">
                    <template slot-scope="scope">
                        <span>{{ scope.row.signRateCurrent}}%</span>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page="tableData.current"
                    :page-size="size"
                    layout="prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
        </div>
    </div>
</template>

<script>
import {
    querySignStatList,
    getTaxesList
} from '@/api/personal/personal.js';
import { listEnterpriseList } from '@/api/grant/grant';

export default {
    data() {
        return {
            Form: {
                enterpriseId: undefined,
                taxesId: undefined
            },
            statMonth: [],
            tableData: {},
            current: 1,
            size: 10,
            loading: false,
            listEnterprise: [],
            taxesList: [],
        };
    },
    methods: {
        toChangeTime(date) {
            if (date) {
                this.Form.startMonth = date[0];
                this.Form.endMonth = date[1];
            } else {
                this.Form.startMonth = '';
                this.Form.endMonth = '';
            }
        },
        toChangeRateRange(type) {
            if (type) {
                switch (type) {
                    case 1:
                        this.Form.startRate = 0;
                        this.Form.endRate = 0;
                        break;
                    case 2:
                        this.Form.startRate = 0;
                        this.Form.endRate = 30;
                        break;
                    case 3:
                        this.Form.startRate = 30;
                        this.Form.endRate = 50;
                        break;
                    case 4:
                        this.Form.startRate = 50;
                        this.Form.endRate = 80;
                        break;
                    case 5:
                        this.Form.startRate = 80;
                        this.Form.endRate = 100;
                        break;
                    case 6:
                        this.Form.startRate = 100;
                        this.Form.endRate = 100;
                        break;
                }
            } else {
                this.Form.startRate = undefined;
                this.Form.endRate = undefined;
            }
        },
        getData() {
            querySignStatList({
                ...this.Form,
                current: this.current,
                size: this.size,
                sortType: 1
            }).then((res) => {
                if (res.data.code == 0) {
                    this.tableData = res.data.data;
                }
            });
        },
        toSearch() {
            this.current = 1;
            this.getData();
        },
        handleSizeChange(size) {
            this.size = size;
            this.current = 1;
            this.getData();
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.current = current;
            this.getData();
        },
        handleSelectionChange(val) {
            let list = [];
            val.forEach((item) => {
                list.push({
                    taxesId: item.company.id,
                    enterpriseId: item.signInfo.id,
                    workerId: item.single.id,
                    id: item.id
                });
            });
            this.multipleSelection = list;
        },
    },
    async created() {
        listEnterpriseList().then((res) => {
            if (res.data.code === 0) {
                this.listEnterprise = res.data.data;
            }
        });
        getTaxesList().then((res) => {
            if (res.data.code == 0) {
                this.taxesList = res.data.data;
            }
        });
        this.toSearch();
    }
};
</script>