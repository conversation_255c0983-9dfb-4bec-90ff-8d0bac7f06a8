<template>
    <div>
        <el-form :inline="true" class="demo-form-inline">
            <el-form-item label="签约税地：">
                <el-select v-model="taxesId" placeholder="请选择" clearable style="width: 200px" filterable @change="changeTaxes">
                    <el-option v-for="item in taxesList" :key="item.id" :label="item.name" :value="item.id"/>
                </el-select>
            </el-form-item>
        </el-form>
        <div ref="chart" style="width: 90%; height: 443px"></div>
        <div ref="chart2" style="width: 90%; height: 443px"></div>

        <el-form :inline="true" class="demo-form-inline">
            <el-form-item label="选择月份：">
                <el-select v-model="statMonth" placeholder="请选择" style="width: 200px" @change="changeMonth">
                    <el-option v-for="item in monthList" :key="item" :label="item" :value="item"/>
                </el-select>
            </el-form-item>
        </el-form>
        <div ref="chart3" style="width: 100%"></div>
        <div ref="chart4" style="width: 100%"></div>
    </div>
</template>

<script>
import {
    getSignStatChart,
    getMonthSignStat,
    getTaxesListForSignStat
} from '@/api/personal/personal.js';

export default {
    data() {
        return {
            taxesList: [],
            taxesId: '',
            monthList: [],
            statMonth: '',
        }
    },
    methods: {
        getData() {
            getSignStatChart(this.taxesId).then((res) => {
                if (res.data.code == 0) {
                    console.log(res.data.data);
                    this.drawChart(res.data.data);
                    this.drawChart2(res.data.data);
                }
            });
        },
        drawChart(monthData) {
            let data = monthData.map(item => {
                return {
                    statMonth: item.statMonth,
                    billCountTotal: item.billCountTotal,
                    signCountTotal: item.signCountTotal,
                    signRateTotal: item.signRateTotal
                }
            });
            const chart = this.$refs.chart;
            if (chart) {
                const myChart = this.$echarts.init(chart);
                const option = {
                    legend: {
                        data: ['发放总人次', '签约总人次', '签约率(人次)']
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function (params) {
                            let result = params[0].name + '<br/>';
                            params.forEach((item, index) => {
                                let indexName = item.dimensionNames[index + 1]
                                let value = item.value[indexName]
                                if (indexName == 'signRateTotal') {
                                    value = value + '%'
                                }
                                result += item.marker + item.seriesName + ': ' + value + '<br/>';
                            });
                            return result;
                        }
                    },
                    dataset: {
                        source: data
                    },
                    xAxis: { type: 'category' },
                    yAxis: [
                        {
                            type: 'value',
                            min: 0,
                            position: 'left',
                            axisLabel: {
                                formatter: '{value}'
                            }
                        },
                        {
                            type: 'value',
                            name: '签约率(人次)',
                            min: 0,
                            max: 100,
                            position: 'right',
                            axisLabel: {
                                formatter: '{value} %'
                            },
                            splitLine: {
                                show: false
                            }
                        }
                    ],
                    series: [
                        {
                            type: 'bar',
                            name: '发放总人次',
                        },
                        {
                            type: 'bar',
                            name: '签约总人次',
                        },
                        {
                            type: 'line',
                            name: '签约率(人次)',
                            yAxisIndex: 1,
                            smooth: true
                        }
                    ]
                };
                myChart.setOption(option);
                window.addEventListener('resize', function () {
                    myChart.resize();
                });
            }
            this.$on('hook:destroyed', () => {
                window.removeEventListener('resize', function () {
                    myChart.resize();
                });
            });
        },
        drawChart2(monthData) {
            let data = monthData.map(item => {
                return {
                    statMonth: item.statMonth,
                    workerCountTotal: item.workerCountTotal,
                    signCountTotalCurrent: item.signCountTotalCurrent,
                    signRateTotalCurrent: item.signRateTotalCurrent
                }
            });
            const chart2 = this.$refs.chart2;
            if (chart2) {
                const myChart2 = this.$echarts.init(chart2);
                const option = {
                    legend: {
                        data: ['发放总人数', '签约总人数', '当月签约率']
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function (params) {
                            let result = params[0].name + '<br/>';
                            params.forEach((item, index) => {
                                let indexName = item.dimensionNames[index + 1]
                                let value = item.value[indexName]
                                if (indexName == 'signRateTotalCurrent') {
                                    value = value + '%'
                                }
                                result += item.marker + item.seriesName + ': ' + value + '<br/>';
                            });
                            return result;
                        }
                    },
                    dataset: {
                        source: data
                    },
                    xAxis: { type: 'category' },
                    yAxis: [
                        {
                            type: 'value',
                            min: 0,
                            position: 'left',
                            axisLabel: {
                                formatter: '{value}'
                            }
                        },
                        {
                            type: 'value',
                            name: '当月签约率',
                            min: 0,
                            max: 100,
                            position: 'right',
                            axisLabel: {
                                formatter: '{value} %'
                            },
                            splitLine: {
                                show: false
                            }
                        }
                    ],
                    series: [
                        {
                            type: 'bar',
                            name: '发放总人数',
                        },
                        {
                            type: 'bar',
                            name: '签约总人数',
                        },
                        {
                            type: 'line',
                            name: '当月签约率',
                            yAxisIndex: 1,
                            smooth: true
                        }
                    ]
                }
                myChart2.setOption(option);
                window.addEventListener('resize', function () {
                    myChart2.resize();
                });
            }
            this.$on('hook:destroyed', () => {
                window.removeEventListener('resize', function () {
                    myChart2.resize();
                });
            });
        },
        drawChart3(taxesData) {
            let data = taxesData.map(item => {
                return {
                    taxesName: item.taxesName,
                    billCountTotal: item.billCountTotal,
                    signCountTotal: item.signCountTotal,
                    signRateTotal: item.signRateTotal
                }
            });
            const chart3 = this.$refs.chart3;
            if (chart3) {
                const myChart3 = this.$echarts.init(chart3);
                const option = {
                    legend: {
                        data: ['发放总人次', '签约总人次', '签约率(人次)']
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function (params) {
                            let result = params[0].name + '<br/>';
                            params.forEach((item, index) => {
                                let indexName = item.dimensionNames[index + 1]
                                let value = item.value[indexName]
                                if (indexName == 'signRateTotal') {
                                    value = value + '%'
                                }
                                result += item.marker + item.seriesName + ': ' + value + '<br/>';
                            });
                            return result;
                        }
                    },
                    dataset: {
                        source: data
                    },
                    xAxis: [
                        {
                            type: 'value',
                            min: 0,
                            position: 'left',
                            axisLabel: {
                                formatter: '{value}'
                            }
                        },
                        {
                            type: 'value',
                            name: '签约率(人次)',
                            min: 0,
                            max: 100,
                            position: 'right',
                            axisLabel: {
                                formatter: '{value} %'
                            },
                            splitLine: {
                                show: false
                            }
                        }
                    ],
                    yAxis: { 
                        type: 'category',
                        axisLabel: {
                            formatter: function (value) {
                                return value.replace('有限公司', '')
                                    .replace('信息科技', '')
                                    .replace('数字科技', '')
                                    .replace('信息咨询', '')
                                    .replace('管理咨询', '');
                            }
                        }
                    },
                    series: [
                        {
                            type: 'bar',
                            name: '发放总人次',
                            encode: {
                                x: 'billCountTotal',
                                y: 'taxesName'
                            }
                        },
                        {
                            type: 'bar',
                            name: '签约总人次',
                            encode: {
                                x: 'signCountTotal',
                                y: 'taxesName'
                            }
                        },
                        {
                            type: 'line',
                            name: '签约率(人次)',
                            smooth: true,
                            encode: {
                                x: 'signRateTotal',
                                y: 'taxesName'
                            }
                        }
                    ]
                };
                myChart3.setOption(option);
                window.addEventListener('resize', function () {
                    myChart3.resize();
                });
                
                let chartName = this.$echarts.init(chart3);
                this.autoHeight = taxesData.length * 70 + 80; // counst.length为柱状图的条数，即数据长度。35为我给每个柱状图的高度，50为柱状图x轴内容的高度(大概的)。
                chartName.resize({ height: this.autoHeight });

                this.$on('hook:destroyed', () => {
                    window.removeEventListener('resize', function () {
                        myChart3.resize();
                    });
                });
            }
        },
        drawChart4(taxesData) {
            let data = taxesData.map(item => {
                return {
                    taxesName: item.taxesName,
                    workerCountTotal: item.workerCountTotal,
                    signCountTotalCurrent: item.signCountTotalCurrent,
                    signRateTotalCurrent: item.signRateTotalCurrent
                }
            });
            const chart4 = this.$refs.chart4;
            if (chart4) {
                const myChart4 = this.$echarts.init(chart4);
                const option = {
                    legend: {
                        data: ['发放总人数', '签约总人数', '当月签约率']
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function (params) {
                            let result = params[0].name + '<br/>';
                            params.forEach((item, index) => {
                                let indexName = item.dimensionNames[index + 1]
                                let value = item.value[indexName]
                                if (indexName == 'signRateTotalCurrent') {
                                    value = value + '%'
                                }
                                result += item.marker + item.seriesName + ': ' + value + '<br/>';
                            });
                            return result;
                        }
                    },
                    dataset: {
                        source: data
                    },
                    xAxis: [
                        {
                            type: 'value',
                            min: 0,
                            position: 'left',
                            axisLabel: {
                                formatter: '{value}'
                            }
                        },
                        {
                            type: 'value',
                            name: '当月签约率',
                            min: 0,
                            max: 100,
                            position: 'right',
                            axisLabel: {
                                formatter: '{value} %'
                            },
                            splitLine: {
                                show: false
                            }
                        }
                    ],
                    yAxis: { 
                        type: 'category',
                        axisLabel: {
                            formatter: function (value) {
                                return value.replace('有限公司', '')
                                    .replace('信息科技', '')
                                    .replace('数字科技', '')
                                    .replace('信息咨询', '')
                                    .replace('管理咨询', '');
                            }
                        }
                    },
                    series: [
                        {
                            type: 'bar',
                            name: '发放总人数',
                            encode: {
                                x: 'workerCountTotal',
                                y: 'taxesName'
                            }
                        },
                        {
                            type: 'bar',
                            name: '签约总人数',
                            encode: {
                                x: 'signCountTotalCurrent',
                                y: 'taxesName'
                            }
                        },
                        {
                            type: 'line',
                            name: '当月签约率',
                            smooth: true,
                            encode: {
                                x: 'signRateTotalCurrent',
                                y: 'taxesName'
                            }
                        }
                    ]
                };
                myChart4.setOption(option);
                window.addEventListener('resize', function () {
                    myChart4.resize();
                });
                let chartName = this.$echarts.init(chart4);
                this.autoHeight = taxesData.length * 70 + 80; // counst.length为柱状图的条数，即数据长度。35为我给每个柱状图的高度，50为柱状图x轴内容的高度(大概的)。
                chartName.resize({ height: this.autoHeight });

                this.$on('hook:destroyed', () => {
                    window.removeEventListener('resize', function () {
                        myChart4.resize();
                    });
                });
            }
        },
        changeTaxes(taxesId) {
            this.taxesId = taxesId;
            this.getData();
        },
        changeMonth(statMonth) {
            this.statMonth = statMonth;
            this.getMonthSignStatData();
        },
        getMonthSignStatData() {
            getMonthSignStat(this.statMonth).then((res) => {
                if (res.data.code == 0) {
                    this.drawChart3(res.data.data);
                    this.drawChart4(res.data.data);
                }
            });
        },
        getLatest12Month() {
            let date = new Date();
            let year = date.getFullYear();
            let month = date.getMonth() + 1;
            let result = [];
            for (let i = 0; i < 12; i++) {
                if (month == 0) {
                    month = 12;
                    year--;
                }
                result.push(year + '-' + (month < 10 ? '0' + month : month));
                month--;
            }
            this.monthList = result;
            this.statMonth = result[0];
        }
    },
    created() {
        this.getLatest12Month();
        getTaxesListForSignStat().then((res) => {
            if (res.data.code == 0) {
                this.taxesList = res.data.data;
            }
        });
        this.getData();
        this.getMonthSignStatData();
    }
}
</script>