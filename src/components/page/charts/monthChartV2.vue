<template>
    <div>
        <el-row :gutter="20">
            <el-col :span="24">
                <el-card shadow="never" class="mgb20">
                    <div slot="header" class="clearfix">
                        <div>
                            <span>充值/发放月统计</span>
                            <el-date-picker
                                v-model="value"
                                type="monthrange"
                                range-separator="~"
                                start-placeholder="开始月份"
                                end-placeholder="结束月份"
                                style="float: right"
                                @change="toChoseTime"
                                value-format="yyyy-MM"
                            >
                            </el-date-picker>
                        </div>
                    </div>
                    <div ref="chart" style="width: 100%; height: 376px"></div>
                </el-card>
                <el-card shadow="never" class="mgb20">
                    <div slot="header" class="clearfix">
                        <span>充值/发放每日统计</span>
                    </div>
                    <div>
                        <ul style="display: flex;flex-wrap: wrap;list-style-type: none;">
                            <li v-for="(issueRechargeItem, index) in issueRechargePast31DaysList" style="width: 230px;padding: 10px; border: 1px solid #DCDFE6;">
                                <p>{{ issueRechargeItem.time }}</p>
                                <p>发放：<span class="money" style="color: #479FFF;">￥{{ parseFloat(issueRechargeItem.money).toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</span></p>
                                <p>充值：<span class="money" style="color: #FF9898;">￥{{ parseFloat(issueRechargeItem.rechargeMoney).toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</span></p>
                            </li>
                        </ul>
                    </div>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<script>
import theme from '../../../utils/echartsMacarons.json'
import {
    moneyIssue, issueRechargePast31Days
} from '@/api/charts/charts.js';
import { currency } from '@/utils/currency.js';
const date = new Date();
const year = date.getFullYear();
const month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
const beforeYear = date.getFullYear() - 1;
export default {
    name: 'dashboard',
    data() {
        return {
            name: localStorage.getItem('ms_username'),
            value: [beforeYear + '-' + month, year + '-' + month],
            form: {
                taxesId: '',
                enterpriseId: '',
                startTime: year + '-' + month,
                endTime: year + '-' + month
            },
            forms: {
                taxesId: '',
                enterpriseId: '',
                startTime: beforeYear + '-' + month,
                endTime: year + '-' + month
            },
            issueRechargePast31DaysList: []
        };
    },
    filters: {
        currency: currency
    },
    components: {
    },
    created() {
    },
    computed: {
        role() {
            return this.name === 'admin' ? '超级管理员' : '普通用户';
        },
    },
    mounted() {
        this.$echarts.registerTheme('macarons', theme)
        this.moneyIssueData();
    },
    methods: {
        moneyIssueData() {
            moneyIssue({ ...this.form, startTime: this.forms.startTime, endTime: this.forms.endTime }).then((res) => {
                if (res.data.code == 0) {
                    this.getEchartData(res.data.data);
                }
            });

            issueRechargePast31Days().then((res) => {
                console.log(res)
                this.issueRechargePast31DaysList = res.data.data;
            })
        },
        //月度分析
        getEchartData(dataTable) {
            let dataTableW = dataTable.map((s) => {
                s.money = (parseFloat(s.money) / 10000).toFixed(2);
                s.rechargeMoney = (parseFloat(s.rechargeMoney) / 10000).toFixed(2);
                return s;
            });
            const chart = this.$refs.chart;
            if (chart) {
                const myChart = this.$echarts.init(chart);
                const option = {
                    color: ['#479FFF', "#FF9898",],
                    legend: { },
                    tooltip: {
                        formatter: function (params) {
                            console.log(params);
                            return '<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:#479FFF;"></span>发放: ' + params.value.money + '万元' + '<br/>'
                                + '<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:#FF9898;"></span>充值: ' + params.value.rechargeMoney + '万元';
                        }
                    },
                    dataset: {
                        source: dataTableW
                    },
                    xAxis: { type: 'category' },
                    yAxis: {
                        axisLabel: {
                            formatter: `{value}万元`
                        }
                    },
                    grid: {
                        x: 100,
                        y: 45,
                        x2: 5,
                        y2: 25,
                        borderWidth: 1
                    },
                    series: [
                        {
                            type: 'bar',
                            encode: {
                                x: 'time',
                                y: 'money'
                            },
                            stack: 'x'
                        },
                        {
                            type: 'bar',
                            encode: {
                                x: 'time',
                                y: 'rechargeMoney'
                            },
                            stack: 'x'
                        }
                    ]
                };
                myChart.setOption(option);
                window.addEventListener('resize', function () {
                    myChart.resize();
                });
            }
            this.$on('hook:destroyed', () => {
                window.removeEventListener('resize', function () {
                    myChart.resize();
                });
            });
        },
        toChoseTime(val) {
            if (val) {
                this.forms.startTime = val[0];
                this.forms.endTime = val[1];
            } else {
                this.forms.startTime = beforeYear + '-' + month;
                this.forms.endTime = year + '-' + month;
                this.value = [beforeYear + '-' + month, year + '-' + month];
                console.log('当前月份', this.value);
                this.$forceUpdate();
            }
            this.moneyIssueData();
        },
    }
};
</script>

<style scoped>
.el-row {
    margin-bottom: 20px;
}

.mgb20 {
    margin-bottom: 20px;
}

.schart {
    width: 100%;
    height: 300px;
}
</style>
