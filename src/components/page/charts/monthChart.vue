<template>
    <div>
        <el-row :gutter="20">
            <el-col :span="24">
                <el-card shadow="never" class="mgb20">
                    <div class="box-info">
                        <div></div>
                        <div>
                            <el-select
                                placeholder="请选择税源地"
                                class="select-box"
                                v-model="form.taxesId"
                                filterable
                                clearable
                                @change="toChoseSelect"
                            >
                                <el-option v-for="item in taxesList" :key="item.taxesId" :label="item.taxesName" :value="item.taxesId"> </el-option>
                            </el-select>
                            <el-select
                                placeholder="请选择企业"
                                class="select-box"
                                v-model="form.enterpriseId"
                                filterable
                                clearable
                                @change="toChoseSelect"
                            >
                                <el-option
                                    v-for="item in enterpriseList"
                                    :key="item.enterpriseId"
                                    :label="item.name"
                                    :value="item.enterpriseId"
                                >
                                </el-option>
                            </el-select>
                        </div>
                    </div>
                </el-card>
                <el-card shadow="never" class="mgb20">
                    <div slot="header" class="clearfix">
                        <div>
                            <span>发放记录</span>
                            <el-date-picker
                                v-model="paymentDateRange"
                                type="monthrange"
                                range-separator="~"
                                start-placeholder="开始月份"
                                end-placeholder="结束月份"
                                style="float: right"
                                @change="toChoseTime"
                                value-format="yyyy-MM"
                            >
                            </el-date-picker>
                        </div>
                    </div>
                    <div ref="chart" style="width: 100%; height: 376px"></div>
                </el-card>
                <el-card shadow="never" class="mgb20">
                    <div slot="header" class="clearfix">
                        <span>交易分析</span>
                        <el-date-picker
                            v-model="transDateRange"
                            type="monthrange"
                            range-separator="~"
                            start-placeholder="开始月份"
                            end-placeholder="结束月份"
                            style="float: right"
                            @change="toChoseTimes"
                            value-format="yyyy-MM"
                        >
                        </el-date-picker>
                    </div>
                    <el-row>
                        <el-col :span="12">
                            <div>充值概况</div>
                            <div ref="chartPay" style="width: 100%; height: 375px"></div>
                        </el-col>
                        <el-col :span="12">
                            <div>付款概况</div>
                            <div ref="charts" style="width: 100%; height: 375px"></div>
                        </el-col>
                    </el-row>
                    <el-row :gutter="12">
                        <el-col :span="5" v-for="(i, index) in moneyList" :key="index">
                            <div @click="toChangeToday(index)">
                                <el-card shadow="never" :class="[todayChose == index ? 'color-blue' : '']">
                                    <div class="name_title">{{ i.name }}</div>
                                    <div class="name_money money">{{ i.money | currency('￥') }}</div>
                                </el-card>
                            </div>
                        </el-col>
                    </el-row>
                    <div ref="chartLine" style="width: 100%"></div>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<script>
import Schart from 'vue-schart';
import theme from '../../../utils/echartsMacarons.json'
import {
    moneyIssue,
    listTaxes,
    listEnterprises,
    topUpOverview,
    getTransactionAnalysis,
    getEnterpriseTransaction
} from '@/api/charts/charts.js';
import { currency, timeCurrent } from '@/utils/currency.js';
import { Message } from 'element-ui';
const date = new Date();
const year = date.getFullYear();
const month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
const beforeYear = date.getFullYear() - 1;
export default {
    name: 'dashboard',
    data() {
        return {
            name: localStorage.getItem('ms_username'),
            paymentDateRange: [beforeYear + '-' + month, year + '-' + month],
            value1: '',
            transDateRange: [year + '-' + month, year + '-' + month],
            todayChose: 0,
            activeIndex: 0,
            scrollBox: [],
            config: {
                data: [58.91],
                shape: 'round',
                colors: ['#1890ff', '#1890ff'],
                waveOpacity: 0.75,
                waveHeight: 20
            },
            form: {
                taxesId: '',
                enterpriseId: '',
                startTime: year + '-' + month,
                endTime: year + '-' + month,
                limit: 10
            },
            forms: {
                taxesId: '',
                enterpriseId: '',
                startTime: beforeYear + '-' + month,
                endTime: year + '-' + month
            },
            showLineList: [],
            taxesList: [],
            enterpriseList: [],
            moneyList: [
                {
                    money: undefined,
                    name: '充值金额(元)'
                },
                {
                    money: undefined,
                    name: '付款金额(元)'
                },
                {
                    money: undefined,
                    name: '服务费金额(元)'
                }
            ]
        };
    },
    filters: {
        currency: currency
    },
    components: {
        Schart
    },
    created() {
        listTaxes().then((res) => {
            if (res.data.code == 0) {
                this.taxesList = res.data.data;
            }
        });
        listEnterprises().then((res) => {
            if (res.data.code == 0) {
                this.enterpriseList = res.data.data;
            }
        });
    },
    computed: {
        role() {
            return this.name === 'admin' ? '超级管理员' : '普通用户';
        },
        top() {
            return -this.activeIndex * 90 + 'px';
        }
    },
    mounted() {
        this.$echarts.registerTheme('macarons', theme)
        this.moneyIssueData();
        this.getPayAndIn();
        this.getAnalysis();
        this.getTransaction();
        this.ScrollUp();
    },
    methods: {
        toChoseSelect() {
            this.moneyIssueData();
            this.getPayAndIn();
            this.getAnalysis();
            this.getTransaction();
        },
        getPayAndIn() {
            topUpOverview(this.form).then((res) => {
                if (res.data.code == 0) {
                    this.getEchartNew(res.data.data);
                    this.getEchartPay(res.data.data);
                }
            });
        },
        getAnalysis() {
            getTransactionAnalysis(this.form).then((res) => {
                if (res.data.code == 0) {
                    this.moneyList[0].money = res.data.data.payMoney;
                    this.moneyList[1].money = res.data.data.salaryMoney;
                    this.moneyList[2].money = res.data.data.billTotalMoney;
                }
            });
        },
        moneyIssueData() {
            moneyIssue({ ...this.form, startTime: this.forms.startTime, endTime: this.forms.endTime }).then((res) => {
                if (res.data.code == 0) {
                    this.getEchartData(res.data.data);
                }
            });
        },
        changeDate() {
            const now = new Date().getTime();
            this.data.forEach((item, index) => {
                const date = new Date(now - (6 - index) * 86400000);
                item.name = `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;
            });
        },
        toChangeToday(idx) {
            this.todayChose = idx;
            this.getTransaction();
        },
        //滚动播报方法
        ScrollUp() {
            this.intnum = setInterval((_) => {
                if (this.activeIndex < this.scrollBox.length && this.scrollBox.length > 4) {
                    this.activeIndex += 1;
                } else {
                    this.activeIndex = 0;
                    // clearInterval(this.intnum);
                    // let data = [
                    //     {
                    //         bank: '湖北靖平云链 - 招商银行1',
                    //         company: '杰弗里（上海）咨询管理有限公司',
                    //         name: '收款人：盛景成'
                    //     },

                    // ];
                    // this.scrollBox = this.scrollBox.concat(data);
                    // console.log(this.scrollBox);
                    // this.ScrollUp();
                }
            }, 1000);
        },
        Stop() {
            // clearInterval(this.intnum);
        },
        Up() {
            // this.ScrollUp();
        },
        getTransaction() {
            const chart = this.$refs.chartLine;
            const myChart = this.$echarts.init(chart);
            myChart.clear(this.option); //清空当前画布所有数据
            getEnterpriseTransaction({ ...this.form, type: this.todayChose + 1 }).then((res) => {
                if (res.data.code == 0) {
                    let enterpriseList = [];
                    let enterpriseIdList = [];
                    let merchantList = [];
                    res.data.data.forEach((v, i) => {
                        if((this.todayChose == 0 && Math.abs(v.payMoney) > 0) || (this.todayChose == 1 && v.salaryMoney > 0) || (this.todayChose == 2 && v.billTotalMoney > 0)) {
                            enterpriseList.push(v.enterpriseName);
                            enterpriseIdList.push(v.enterpriseId);
                            v.voList.forEach((s) => {
                                if((this.todayChose == 0 && Math.abs(s.payMoney) > 0) || (this.todayChose == 1 && s.salaryMoney > 0) || (this.todayChose == 2 && s.billTotalMoney > 0)) {
                                    merchantList.push(s);
                                }
                            });
                        }
                    });
                    this.getEchartLine(enterpriseList, this.mapArray(enterpriseIdList, merchantList));
                }
            });
        },
        //月度分析
        getEchartData(dataTable) {
            let dataTableW = dataTable.map((s) => {
                s.money = (parseFloat(s.money) / 10000).toFixed(2);
                return s;
            });
            const chart = this.$refs.chart;
            if (chart) {
                const myChart = this.$echarts.init(chart);
                const option = {
                    color: ['#479FFF'],
                    legend: {},
                    tooltip: {
                        formatter: function (params) {
                            return params.value.money + '万元';
                        }
                    },
                    dataset: {
                        source: dataTableW
                    },
                    xAxis: { type: 'category' },
                    yAxis: {
                        axisLabel: {
                            formatter: `{value}万元`
                        }
                    },
                    grid: {
                        x: 100,
                        y: 45,
                        x2: 5,
                        y2: 25,
                        borderWidth: 1
                    },
                    series: [
                        {
                            type: 'bar',
                            encode: {
                                x: 'time',
                                // 将 "product" 列映射到 Y 轴。
                                y: 'money'
                            },
                            label: {
                                show: true,
                                position: 'top', // 设置标签显示在顶部
                                formatter: function (params) {
                                    return params.value.money + '万元';
                                },
                                color: 'black' // 可选：设置标签文字颜色
                            }
                        }
                    ]
                };
                myChart.setOption(option);
                window.addEventListener('resize', function () {
                    myChart.resize();
                });
            }
            this.$on('hook:destroyed', () => {
                window.removeEventListener('resize', function () {
                    myChart.resize();
                });
            });
        },
        //处理数组方法
        mapArray(enterpriseIdList, target) {
            let newArr = [];
            let teamArr = [];
            let listData = target;
            for (let i = 0; i < listData.length; i++) {
                listData[i].money = undefined;
                if (this.todayChose == 0) {
                    listData[i].money = listData[i].payMoney;
                } else if (this.todayChose == 1) {
                    listData[i].money = listData[i].salaryMoney;
                } else if (this.todayChose == 2) {
                    listData[i].money = listData[i].billTotalMoney;
                }
                if (!teamArr.includes(listData[i]['merchantId'])) {
                    let moneyIndex = enterpriseIdList.indexOf(listData[i]['enterpriseId'])
                    let data = []
                    data[moneyIndex] = listData[i]['money'].toFixed(2);
                    newArr.push({
                        id: listData[i]['merchantId'],
                        name: listData[i]['merchantName'],
                        data: data
                    });
                    teamArr.push(listData[i]['merchantId']);
                } else {
                    let moneyIndex = enterpriseIdList.indexOf(listData[i]['enterpriseId'])
                    for (let n = 0; n < newArr.length; n++) {
                        if (newArr[n]['id'] == listData[i]['merchantId']) {
                            newArr[n].data[moneyIndex] = listData[i]['money'].toFixed(2);
                            break;
                        }
                    }
                }
            }
            return newArr;
        },
        //付款概况饼图
        getEchartNew(dataTable) {
            let chartData = [];
            dataTable.forEach((s) => {
                if(s.salaryMoney > 0) {
                    chartData.push({
                        name: `${s.merchantName} 「${s.bankName}」`,
                        value: s.salaryMoney
                    });
                }
            });
            const chart = this.$refs.charts;
            if (chart) {
                const myChart = this.$echarts.init(chart, 'macarons');
                const option = {
                    tooltip: {
                        trigger: 'item'
                    },
                    legend: {
                        data: [],
                        orient: 'vertical',
                        top: 'bottom',
                        x: 'right', //可设定图例在左、右、居中
                        y: 'bottom', //可设定图例在上、下、居中
                    },
                    grid: {
                        x: 45,
                        y: 45,
                        x2: 5,
                        y2: 25,
                        borderWidth: 1
                    },
                    series: [
                        {
                            type: 'pie',
                            radius: ['40%', '70%'],
                            data: chartData,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            },
                            "color": [
                                "#FFFFCC",
                                "#469DFB",
                                "#CCFFFF",
                                "#FFFF99",
                                "#66CCFF",
                                "#FFFF66",
                                "#99CCFF",
                                "#FFFF33",
                                "#6699CC"
                                // "#D8D9FF",
                                // "#f2cbcb",
                                // "#f2ceee",
                                // "#FFEDE7",
                                // "#FFE0F1",
                                // "#FADCDA",
                                // "#f2dada",
                                // "#cef2e3",
                                // "#f2e1cb",
                                // "#C0FFD0",
                                // "#FF9898",
                            ],
                        }
                    ]
                };

                myChart.setOption(option);
                window.addEventListener('resize', function () {
                    myChart.resize();
                });
            }
            this.$on('hook:destroyed', () => {
                window.removeEventListener('resize', function () {
                    myChart.resize();
                });
            });
        },
        //充值概况饼图
        getEchartPay(dataTable) {
            let chartData = [];
            dataTable.forEach((s) => {
                let absRechargeMoney = Math.abs(Number.parseFloat(s.payMoney));
                if (absRechargeMoney > 0) {
                    chartData.push({
                        name: `${s.merchantName} 「${s.bankName}」 ${s.payMoney}`,
                        value: absRechargeMoney
                    });
                }
            });
            const chart = this.$refs.chartPay;
            if (chart) {
                const myChart = this.$echarts.init(chart, 'macarons');
                const option = {
                    tooltip: {
                        trigger: 'item',
                        formatter: function(param) {
                            return param.marker + ' ' + param.name;
                        }
                    },
                    legend: {
                        data: [],
                        display: false,
                        x: 'right', //可设定图例在左、右、居中
                        y: 'bottom', //可设定图例在上、下、居中
                        orient: 'vertical'
                    },
                    grid: {
                        x: 45,
                        y: 45,
                        x2: 5,
                        y2: 25,
                        borderWidth: 1
                    },
                    series: [
                        {
                            type: 'pie',
                            radius: ['40%', '70%'],
                            data: chartData,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            },
                            label: {
                                show: true
                            },
                            labelLine: {
                                show: true
                            }
                        }
                    ]
                };

                myChart.setOption(option);
                window.addEventListener('resize', function () {
                    myChart.resize();
                });
            }
            this.$on('hook:destroyed', () => {
                window.removeEventListener('resize', function () {
                    myChart.resize();
                });
            });
        },
        toChoseTime(val) {
            if (val) {
                if (!this.isDateRangeValid(val[0], val[1], 12)) {
                    Message.error('起止月份跨度不能超过12个月');
                    this.paymentDateRange[0] = this.adjustMonth(val[1], -12);
                }
                this.forms.startTime = this.paymentDateRange[0];
                this.forms.endTime = this.paymentDateRange[1];
            } else {
                this.forms.startTime = beforeYear + '-' + month;
                this.forms.endTime = year + '-' + month;
                this.paymentDateRange = [beforeYear + '-' + month, year + '-' + month];
                this.$forceUpdate();
            }
            this.moneyIssueData();
        },
        isDateRangeValid(startMonth, endMonth, maxSpan) {
            // 将月份字符串转换为 Date 对象
            function parseMonth(monthStr) {
                const [year, month] = monthStr.split('-').map(Number);
                return new Date(year, month - 1);
            }

            const startDate = parseMonth(startMonth);
            const endDate = parseMonth(endMonth);

            // 计算月份差
            const yearDiff = endDate.getFullYear() - startDate.getFullYear();
            const monthDiff = endDate.getMonth() - startDate.getMonth();
            const totalMonths = yearDiff * 12 + monthDiff;

            // 判断是否超过最大跨度
            return totalMonths <= maxSpan;
        },
        adjustMonth(month, deltaMonths) {
            // 将月份字符串转换为 Date 对象
            function parseMonth(monthStr) {
                const [year, month] = monthStr.split('-').map(Number);
                return new Date(year, month - 1);
            }

            // 将 Date 对象转换回月份字符串
            function formatMonth(date) {
                const year = date.getFullYear();
                const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 补齐两位
                return `${year}-${month}`;
            }

            const date = parseMonth(month);
            date.setMonth(date.getMonth() + deltaMonths); // 调整月份

            return formatMonth(date);
        },
        toChoseTimes(val) {
            if (this.transDateRange) {
                if (!this.isDateRangeValid(val[0], val[1], 6)) {
                    Message.error('起止月份跨度不能超过6个月');
                    this.transDateRange[0] = this.adjustMonth(val[1], -6);
                }
                this.form.startTime = this.transDateRange[0];
                this.form.endTime = this.transDateRange[1];
            } else {
                this.form.startTime = year + '-' + month;
                this.form.endTime = year + '-' + month;
            }
            //柱状图
            this.getTransaction();
            //钱
            this.getAnalysis();

            this.getPayAndIn();
            this.getAnalysis();
        },
        getEchartLine(enterpriseList, merchantList) {
            merchantList.forEach((s, index) => {
                s.type = 'bar';
                s.stack = 'total';
                s.label = {
                    show: false,
                    position: 'right', // 标签位置，可以根据需要调整
                    formatter: function(params) {
                        return parseFloat(params.value).toLocaleString('zh-CN', {style: 'currency', currency: 'CNY'})
                    }
                };
                s.emphasis = {
                    focus: 'none'
                };
            });

            const chart = this.$refs.chartLine;
            if (chart) {
                const myChart = this.$echarts.init(chart, 'macarons');
                const option = {
                    title: {
                        show: enterpriseList.length > 0,
                        text: `前${this.form.limit}企业`
                    },
                    color: [
                        "#9FE4AE",
                        "#FADCDA",
                        "#B9F5F6",
                        "#f2ceee",
                        "#cef2e3",
                        "#89E7E7",
                        "#ffb980",
                        "#C4EAFF",
                        "#e5cf0d",
                        "#93D1FF",
                        "#d87a80",
                        "#b6a2de",
                        "#2ec7c9",
                        "#b6a2de",
                        "#5ab1ef",
                        "#8d98b3",
                        "#97b552",
                        "#95706d",
                        "#dc69aa",
                        "#07a2a4",
                        "#9a7fd1",
                        "#588dd5",
                        "#f5994e",
                        "#c05050",
                        "#59678c",
                        "#c9ab00",
                        "#7eb00a",
                        "#6f5553",
                        "#c14089",
                    ],
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            // Use axis to trigger tooltip
                            type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
                        },
                        formatter: function(params) {
                            let showTooltip = params.some(param => param.value !== 0 && param.value !== undefined);
                            if (showTooltip) {
                                return params.map(param => {
                                    if (param.value !== 0 && param.value !== undefined) {
                                        return param.marker + ' ' + param.seriesName + ': ￥' + param.value + '<br>';
                                    } else {
                                        return; // 对于值为0或者不存在的，返回空字符串
                                    }
                                }).join('');
                            } else {
                                return ''; // 如果全部值为0或者不存在，则返回空字符串
                            }
                        }
                    },
                    legend: {},
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'value'
                    },
                    yAxis: {
                        type: 'category',
                        data: enterpriseList
                    },
                    series: [...merchantList, {
                        name: '',
                        type: 'bar',
                        stack: 'total',
                        label: {
                            show: true,
                            position: 'right',
                            formatter: function (params) {
                                let total = 0;
                                for (var i = 0; i < merchantList.length; i++) {
                                    let dataVal = merchantList[i].data[params.dataIndex];
                                    total += Number.parseFloat(dataVal ? dataVal : 0);
                                }
                                return total.toLocaleString('zh-CN', {style: 'currency', currency: 'CNY'});
                            }
                        },
                        data: new Array(enterpriseList.length).fill(0)
                    }]
                };

                myChart.setOption(option);
                window.addEventListener('resize', function () {
                    myChart.resize();
                });
                let chartName = this.$echarts.init(chart);
                this.autoHeight = enterpriseList.length * 70 + 80; // counst.length为柱状图的条数，即数据长度。35为我给每个柱状图的高度，50为柱状图x轴内容的高度(大概的)。
                chartName.resize({ height: this.autoHeight });
            }
            this.$on('hook:destroyed', () => {
                window.removeEventListener('resize', function () {
                    myChart.resize();
                });
            });
        }
    }
};
</script>

<style scoped>
.el-row {
    margin-bottom: 20px;
}

.grid-content {
    display: flex;
    align-items: center;
    height: 100px;
}
.select-box {
    margin-left: 10px;
}
.scroll-wrap {
    height: 360px;
    overflow: hidden;
}
.scroll-content {
    position: relative;
    transition: top 0.5s;
}
.number-box {
    font-size: 14px;
}
.number-box .num {
    font-size: 25px;
    margin: 10px 0 20px 0;
}
.grid-cont-right {
    flex: 1;
    text-align: center;
    font-size: 14px;
    color: #999;
}
.status-business {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.grid-num {
    font-size: 30px;
    font-weight: bold;
}
.box-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.grid-con-icon {
    font-size: 50px;
    width: 100px;
    height: 100px;
    text-align: center;
    line-height: 100px;
    color: #fff;
}

.grid-con-1 .grid-con-icon {
    background: rgb(45, 140, 240);
}

.grid-con-1 .grid-num {
    color: rgb(45, 140, 240);
}

.grid-con-2 .grid-con-icon {
    background: rgb(100, 213, 114);
}

.grid-con-2 .grid-num {
    color: rgb(45, 140, 240);
}

.grid-con-3 .grid-con-icon {
    background: rgb(242, 94, 67);
}

.grid-con-3 .grid-num {
    color: rgb(242, 94, 67);
}
.color-blue {
    border-color: #0099cc;
}
.user-info {
    display: flex;
    align-items: center;
    padding-bottom: 20px;
    border-bottom: 2px solid #ccc;
    margin-bottom: 20px;
}

.user-avator {
    width: 120px;
    height: 120px;
    border-radius: 50%;
}

.user-info-cont {
    padding-left: 50px;
    flex: 1;
    font-size: 14px;
    color: #999;
}

.user-info-cont div:first-child {
    font-size: 30px;
    color: #222;
}

.user-info-list {
    font-size: 14px;
    color: #999;
    line-height: 25px;
}

.user-info-list span {
    margin-left: 50px;
}

.mgb20 {
    margin-bottom: 20px;
}

.todo-item {
    font-size: 14px;
}

.todo-item-del {
    text-decoration: line-through;
    color: #999;
}

.schart {
    width: 100%;
    height: 300px;
}
.name_title {
    font-size: 14px;
    margin-bottom: 10px;
    font-weight: bold;
}
.name_money {
    font-size: 20px;
    font-weight: bold;
}
.company_name {
    display: inline-block;
    flex: 1;
}
.topinfo {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.bank_name {
    font-size: 14px;
    border: 1px solid #999;
    padding: 5px;
    border-radius: 5px;
    color: #999;
}
.num_money {
    font-size: 40px;
    text-align: center;
    margin-top: 40px;
}
.word_info {
    text-align: center;
    margin-top: 10px;
    margin-bottom: 60px;
}
.scroll-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 89px;
    border-bottom: 1px solid #f2f2f2;
}
.scroll-row-bank {
    font-size: 14px;
    border: 1px solid #cccccc;
    border-radius: 4px;
    display: inline-block;
    padding: 2px 4px;
    margin-bottom: 10px;
}
.scroll-row-company {
    font-size: 14px;
}
.scroll-row-money {
    font-size: 26px;
    text-align: right;
    margin-bottom: 10px;
}
.scroll-row-time {
    font-size: 12px;
    text-align: right;
}
</style>
