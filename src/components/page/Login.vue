<template>
    <div class="login-wrap">
        <!--头部-->
        <div class="login-content">
            <div class="ms-login">
                <div style="flex: 1; position: relative" v-if="!showEnterpriseDia">
                    <div style="height: 14px"></div>
                    <div class="ms-title">
                        <div class="top-chose" v-if="loginByUserName">
                            <span @click="toChoseType(1)" :class="[status == 1 ? 'chose-style' : '']">账号密码登录</span>
                            <span @click="toChoseType(2)" :class="[status == 2 ? 'chose-style' : '']">二维码登录</span>
                        </div>
                    </div>
                    <img v-if="logoUrl" :src="logoUrl" class="logo-size" alt="" />
                    <el-form v-if="status == 1" :model="param" :rules="rules" ref="login" label-width="0px" class="ms-content">
                        <el-form-item prop="username">
                            <el-input autofocus v-model.trim="param.username" placeholder="请输入账号">
                                <i slot="prefix" class="el-icon-lx-people iconFont" />
                            </el-input>
                        </el-form-item>
                        <el-form-item prop="password">
                            <el-input type="password" placeholder="请输入密码" v-model="param.password" @keyup.enter.native="submitForm()">
                                <i slot="prefix" class="el-icon-lx-lock iconFont" />
                            </el-input>
                        </el-form-item>
                        <el-form-item prop="code">
                            <el-row :span="24">
                                <el-col :span="15">
                                    <el-input
                                        :maxlength="code.len"
                                        v-model.trim="param.code"
                                        auto-complete="off"
                                        placeholder="请输入验证码"
                                        @keyup.enter.native="submitForm()"
                                    >
                                        <i slot="prefix" class="el-icon-key iconFont" />
                                    </el-input>
                                </el-col>
                                <!--验证码-->
                                <el-col :span="9">
                                    <div class="code">
                                        <img :src="code.src" class="code-img" @click="refreshCode" />
                                    </div>
                                </el-col>
                            </el-row>
                        </el-form-item>
                        <div class="login-btn">
                            <el-button type="primary" :loading="submitLoading" @click="submitForm()">登录</el-button>
                        </div>
                        <p class="login-tips" @click="handlePassword">忘记密码？</p>
                    </el-form>
                    <div class="need-qrcode" v-if="status == 2">
                        <iframe sandbox="allow-scripts allow-same-origin allow-top-navigation" scrolling="no" width="300" height="400" frameborder="0" allowtransparency="true"
                            :src="'https://open.weixin.qq.com/connect/qrconnect?appid='+ this.appid
                                + '&scope=snsapi_login'
                                + '&redirect_uri='  + encodeURIComponent(redirect_uri)
                                + '&state=' + this.state
                                + '&login_type=jssdk'
                                + '&style='
                                + '&self_redirect=default'
                                + '&href=https://xytb-prd.oss-cn-hangzhou.aliyuncs.com/ticket/20230627/1687838298933_ea356ea0.css'">
                        </iframe>
                    </div>
                </div>
            </div>
            <!-- 添加主题切换按钮到右下角 -->
            <div class="theme-switcher-corner">
                <theme-switcher></theme-switcher>
            </div>
        </div>
        <div class="login-footer" v-if="icp">
            <a href="https://beian.miit.gov.cn/" target="_blank">{{ icp }}</a>
        </div>
        <el-dialog :visible.sync="showEnterpriseDia" top="25vh" :modal="false" width="30%" :show-close="false" @close="clearValue">
            <div class="dia_chose_title">请选择登录机构</div>
            <el-select v-model="value" placeholder="请选择" style="width: 100%; margin-top: 20px" filterable>
                <el-option v-for="item in options" :key="item.enterpriseId" :label="item.enterpriseName" :value="item.enterpriseId">
                </el-option>
            </el-select>
            <span slot="footer" class="dialog-footer button_on">
                <el-button type="primary" @click="toChoseSureIn">确认登录</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import auth from '@/api/auth.js';
import { Message } from 'element-ui';
import { getUserInfo, login, getUserEnterprise, changeUserEnterprise, getLogo } from '@/api/system/login';
import { randomLenNum, encryption } from '@/utils/common';
import { CONSOLE_BASE_URL, AES_KEY, wxUrlLoginCallback, JUMP_LOGIN_DOMAIN, qrcodeAppid, loginByUserName } from '@/api/config.js';
import wxlogin from 'vue-wxlogin';
import logo_xinghuojishi from '@/assets/img/oem/logo_xinghuojishi.png';
import logo_jsjws from '@/assets/img/oem/logo_jsjws.png'
import logo_hyc from '@/assets/img/oem/logo_huiyoucai.png'
import { OSS_URL } from '@/api/config';
import ThemeSwitcher from '@/components/common/ThemeSwitcher.vue';

export default {
    components: {
        wxlogin,
        ThemeSwitcher
    },
    created() {
        //刷新验证码
        const url = window.location.href;
        const hostname = window.location.hostname;
        let urlList = url.split('/#/');
        this.refreshCode();
        this.state = 'platform_' + urlList[0];
        this.getIcp(hostname);
        this.getLogo(hostname);
        sessionStorage.setItem('loginOut', urlList[0]);
        //扫码登录账号
        const openId = this.getQueryString('openId');
        const uuid = this.getQueryString('uuid');
        if (openId) {
            this.qrcodeLogin(openId, uuid);
        }
    },
    data: function () {
        return {
            status: 2,
            state: '',
            redirect_uri: wxUrlLoginCallback,
            //验证码
            code: {
                len: 4,
                src: ''
            },
            user_id: 'undefined',
            //form提交参数
            param: {
                randomStr: '',
                username: '',
                password: '',
                code: ''
            },
            params: {},
            //表单验证规则
            rules: {
                username: [
                    { required: true, message: '请输入用户名', trigger: 'blur' },
                    { min: 1, max: 20, message: '账号不得超过20个字符', trigger: 'blur' }
                ],
                password: [
                    { required: true, message: '请输入密码', trigger: 'blur' },
                    { min: 1, max: 20, message: '密码不得超过20个字符', trigger: 'blur' }
                ],
                code: [
                    { required: true, message: '请输入验证码', trigger: 'blur' },
                    { min: 4, max: 4, message: '验证码为4个字符', trigger: 'blur' }
                ]
            },
            domainHosts: [
                {
                    domain: 'xinghuojishi.cn',
                    logoUrl: logo_xinghuojishi,
                    icp: '京ICP备2023015645号-2',
                },
                {
                    domain: 'jsjws.qiyixin.net.cn',
                    logoUrl: logo_jsjws,
                    icp: '浙ICP备2022035593号-1',
                },
                {
                    domain: 'hyc.qiyixin.net.cn',
                    logoUrl: logo_hyc,
                    icp: '浙ICP备2022035593号-1',
                },

            ],
            submitLoading: false,
            loginDomain: JUMP_LOGIN_DOMAIN,
            appid: qrcodeAppid,
            loginByUserName: loginByUserName,
            options: [],
            value: '',
            showEnterpriseDia: false,
            logoUrl: '',
            labelName: '',
            icp: ''
        };
    },
    mounted() {},
    methods: {
        getQueryString(name) {
            return (
                decodeURIComponent(
                    (new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || [, ''])[1].replace(/\+/g, '%20')
                ) || null
            );
        },
        //刷新验证码
        refreshCode() {
            this.param.code = '';
            this.param.randomStr = randomLenNum(this.code.len, true);
            this.code.src = `${CONSOLE_BASE_URL}code?randomStr=${this.param.randomStr}`;
        },
        //表单提交
        submitForm() {
            this.$refs.login.validate((valid) => {
                if (valid) {
                    if (this.submitLoading) {
                        return;
                    }
                    this.submitLoading = true;
                    this.param.grant_type = 'password';
                    this.param.scope = 'platform';
                    this.param.user_type = '1';
                    const user = encryption({
                        data: this.param,
                        key: AES_KEY,
                        param: ['password']
                    });
                    login(user).then((res) => {
                        this.submitLoading = false;
                        if (res.jti) {
                            //设置登录
                            auth.doAuth(res);
                            // 获取用户信息
                            this.fetchUserInfo();
                            this.$router.push('/');
                        } else {
                            this.refreshCode();
                        }
                    });
                }
            });
        },
        clearValue() {
            this.value = '';
        },
        //二维码扫码登录
        qrcodeLogin(openId, uuid) {
            this.submitLoading = true;
            this.params.grant_type = 'wechat';
            this.params.scope = 'platform';
            this.params.userType = '1';
            this.params.openId = openId;
            this.params.uuid = uuid;
            const user = encryption({
                data: this.params,
                key: AES_KEY,
                param: ['wechat']
            });
            login(user).then((res) => {
                this.submitLoading = false;
                if (res.jti) {
                    //设置登录
                    auth.doAuth(res);
                    // 获取用户信息
                    this.fetchUserInfo();
                } else {
                    setTimeout(() => {
                        window.location.href = window.location.href.split('?')[0];
                    }, 1500);
                }
            });
        },
        //获取用户信息
        fetchUserInfo() {
            getUserInfo().then((res) => {
                if (res.code == 0) {
                    auth.doUserInfo(res.data);
                    this.getPeoplesEnterprise();
                }
            });
        },
        toChoseType(id) {
            this.status = id;
        },
        // 忘记密码
        handlePassword() {
            Message.error('暂不支持');
        },
        toJump(path) {
            window.location.href = path;
        },
        async getPeoplesEnterprise() {
            let res = await getUserEnterprise();
            this.options = res.data;
            switch (res.data.length) {
                case 1:
                    sessionStorage.setItem('enterpriseInfo', 'choseRight');
                    this.$router.push('/');
                    break;
                case 0:
                    sessionStorage.setItem('enterpriseInfo', '');
                    auth.doLogout();
                    this.$message.error('账号未绑定税源地！');
                    break;
                default:
                    this.showEnterpriseDia = true;
            }
        },
        async toChoseSureIn() {
            if (!this.value) return this.$message.error('请选择登录的税源地！');
            let res = await changeUserEnterprise({
                id: this.value
            });
            if (res.code == 0) {
                sessionStorage.setItem('enterpriseInfo', 'choseRight');
                this.$router.push('/');
            }
        },
        async getThisUrlLogo(url) {
            let res = await getLogo(url);
            if (res.data.logoUrl) {
                this.logoUrl = OSS_URL + res.data.logoUrl;
                this.labelName = res.data.label;
            }
        },
        getIcp(url) {
            this.domainHosts.map((item) => {
                if (url.indexOf(item.domain) > -1) {
                    this.icp = item.icp;
                }
            });
        },
        getLogo(url) {
            this.domainHosts.map((item) => {
                if (url.indexOf(item.domain) > -1) {
                    this.logoUrl = item.logoUrl;
                }
            });
        }
    }
};
</script>

<style scoped lang="scss">
.login-wrap {
    position: relative;
    width: 100%;
    height: 100%;
    background: var(--login-background);
    background-size: cover;
}

.bg_icon {
    height: 82vh;
    display: block;
    object-fit: contain;
}

.chose-style {
    font-size: 16px;
    font-weight: 550;
    color: #333333;
    border-bottom: 4px solid #376bff;
}

.login-content {
    position: relative;
    height: 100%;
    background-color: transparent;
    background-image: var(--login-content-bg-image);
    background-repeat: var(--login-content-bg-repeat);
    background-position: var(--login-content-bg-position);
    background-size: var(--login-content-bg-size);
    overflow: hidden;
    color: #303133;
    -webkit-transition: 0.3s;
    transition: 0.3s;
}

.need-qrcode {
    width: 300px;
    overflow: hidden;
    margin: 0 auto;
    position: relative;
}

.white-space {
    position: absolute;
    width: 100%;
    background-color: #fff;
    bottom: 0;
    height: 60px;
    z-index: 999;
}

.icon-box {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: 550;
    color: #333333;
    margin: 50px 0 35px 0;
}

.icon-box-img {
    width: 28px;
    height: 28px;
}

.icon-word {
    margin: 0 20px 0 5px;
}

.top-chose {
    margin: 0 60px;
    font-size: 16px;
    padding: 50px 0 40px 0;
    font-weight: 400;
    color: #999999;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.top-chose span {
    user-select: none;
    cursor: pointer;
}

.port-box-item {
    text-align: right;
    padding-right: 20px;
    user-select: none;
    cursor: pointer;
}

.port-box-item.active {
    background-color: #fff;
    color: #376bff;
}

.login-header {
    background-color: #fff;
    padding: 20px 10%;

    img {
        height: 40px;
        cursor: pointer;
        float: left;
    }

    h2 {
        color: #333333;
        letter-spacing: 2px;
        font-size: 25px;
        border-left: 1px solid #333333;
        margin: 0 0 0 60px;
        padding-left: 20px;
    }
}

.port-box {
    line-height: 56px;
    font-size: 14px;
    font-weight: 550;
    color: #999999;
}

.logoBanner {
    display: inline-block;
    width: 65%;
    margin-top: 220px;
    opacity: 0.75;
}

.ms-login-left {
    width: 180px;
    background-color: #fafafa;
}

.ms-title {
    width: 100%;
    text-align: center;
    font-size: 20px;
    color: #000;
    font-weight: bold;
}

.ms-login {
    width: 400px;
    overflow: hidden;
    box-shadow: 0 7px 25px rgba(0, 0, 0, 0.08);
    background-color: #fff;
    border-radius: 12px;
    position: absolute;
    right: 12vw;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
}

.ms-content {
    padding: 0 30px 40px;
    overflow: hidden;
}

::v-deep .el-input--small .el-input__inner {
    border: none;
}

::v-deep .el-input {
    border-bottom: 1px solid #ebedf2;
    margin-bottom: 5px;
}

.code {
    margin-left: 10px;
    position: absolute;
    bottom: 6px;

    .code-img {
        //border: 1px solid #f0f0f0;
        display: block;
    }
}

.iconFont {
    font-size: 18px;
}

.login-btn {
    margin-top: 24px;
    text-align: center;
}

.login-btn button {
    width: 100%;
    height: 36px;
    margin-bottom: 4px;
    border-radius: 30px;
    background: linear-gradient(to right, #70b7f8, #0d6dea, #0d6dea, #70b7f8);
    border: none;
}

.login-tips {
    font-size: 12px;
    line-height: 30px;
    color: #000;
    float: right;
    cursor: pointer;
    font-weight: bold;
}

//底部的样式
.login-footer {
    color: #fff;
    width: 100%;
    position: fixed;
    bottom: 0;
    height: 60px;
    line-height: 60px;
    background: var(--login-footer-background);
}

.theme-switcher-wrapper {
    display: flex;
    align-items: center;
}

.login-footer a {
    color: #fff;
    margin-right: 10px;
}

@media screen and (max-width: 414px) {
    .login-header {
        padding: 5%;

        img {
            height: 30px;
        }

        h2 {
            font-size: 20px;
            margin: 0 0 0 40px;
        }
    }

    .ms-login {
        position: absolute;
        left: 50%;
        top: 55%;
        width: 300px;
        margin: -190px 0 0 -150px;
        border-radius: 5px;
        overflow: hidden;
        background-color: #fff;
    }
}

.impowerBox .title {
    text-align: center;
    font-size: 20px;
    display: none !important;
}
.logo-size {
    width: 200px;
    display: block;
    margin: 0 auto;
}
.logo-name-tax {
    font-size: 20px;
    font-weight: bold;
    text-align: center;
    margin: 10px 0;
}

.theme-switcher-corner {
    position: absolute;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    background-color: rgba(255, 255, 255, 0.2);
    padding: 5px 10px;
    border-radius: 4px;
}
</style>
