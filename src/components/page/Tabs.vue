<template>
  <div>
      <el-card shadow="never">
          <span slot="header" class="clearfix"> 消息列表 </span>
          <div>
              <el-tabs v-model="message" @tab-click="tabChange" style="box-shadow: none;">
                  <el-tab-pane :label="`未读消息(${tableData.total})`" name="0">
                      <el-table :data="tableData.records" :show-header="false" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell" @row-click="rowClick">
                          <el-table-column>
                              <template slot-scope="scope">
                                  <span class="message-title">{{ scope.row.title }}</span>
                              </template>
                          </el-table-column>
                          <el-table-column prop="date" width="180"></el-table-column>
                          <el-table-column width="120">
                              <template slot-scope="scope">
                                  <el-button size="small" @click.stop="handleRead(scope.row.id)">标为已读</el-button>
                              </template>
                          </el-table-column>
                      </el-table>
                      <div class="pagination">
                          <el-pagination
                              @current-change="handleCurrentChange"
                              :current-page="tableData.current"
                              layout="prev, pager, next, jumper"
                              :total="tableData.total"
                          ></el-pagination>
                      </div>
                      <div class="handle-row">
                          <el-button type="primary" @click="allread">全部标为已读</el-button>
                      </div>
                  </el-tab-pane>
                  <el-tab-pane :label="`已读消息(${tableDatas.total})`" name="1">
                      <el-table :data="tableDatas.records" :show-header="false" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell" @row-click="rowClick">
                          <el-table-column>
                              <template slot-scope="scope">
                                  <span class="message-title">{{ scope.row.title }}</span>
                              </template>
                          </el-table-column>
                          <el-table-column prop="date" width="150"></el-table-column>
                          <el-table-column width="120">
                              <template slot-scope="scope">
                                  <el-button type="danger" @click.stop="handleDel(scope.row.id)">删除</el-button>
                              </template>
                          </el-table-column>
                      </el-table>
                      <div class="pagination">
                          <el-pagination
                              @size-change="handleSizeChanges"
                              @current-change="handleCurrentChanges"
                              :current-page="tableDatas.current"
                              :page-size="tableDatas.size"
                              :page-sizes="[10, 20, 30, 40]"
                              layout="prev, pager, next, jumper"
                              :total="tableDatas.total"
                          ></el-pagination>
                      </div>
                      <!-- <div class="handle-row">
                          <el-button type="danger">删除全部</el-button>
                      </div> -->
                  </el-tab-pane>
              </el-tabs>
          </div>
      </el-card>
      <el-dialog title="消息详情" :visible.sync="dialogFormVisible" top="10vh">
          <el-form>
              <el-form-item label="发送时间：" label-width="140px">
                  {{ messageInfo.createTime }}
              </el-form-item>
              <el-form-item label="通知标题：" label-width="140px">
                  {{ messageInfo.title }}
              </el-form-item>
              <el-form-item label="通知内容：" label-width="140px">
                  <div v-html="messageInfo.content"></div>
              </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
              <el-button type="primary" @click="dialogFormVisible = false">关闭</el-button>
          </div>
      </el-dialog>
  </div>
</template>

<script>
import { sysMailViewRecord, oneRead, oneDelRead, oneClickRead } from '@/api/message/message.js';
export default {
  name: 'tabs',
  data() {
      return {
          message: '0',
          showHeader: false,
          form: {
              size: 10,
              current: 1,
              isRead: 0
          },
          forms: {
              size: 10,
              current: 1,
              isRead: 1
          },
          tableData: {},
          tableDatas: {},
          messageInfo: {},
          dialogFormVisible: false
      };
  },
  methods: {
      async allread() {
          let res = oneClickRead();
          if (res.data.code != 0) return false;
          this.getsysMailViewRecord();
          this.getsysReadlyMailViewRecord();
      },
      async handleDel(id) {
          let ids = [];
          ids.push(id);
          let res = await oneDelRead({ ids });
          if (res.data.code != 0) return false;
          this.getsysMailViewRecord();
          this.getsysReadlyMailViewRecord();
      },
      //设为已读
      async handleRead(id) {
          let ids = [];
          ids.push(id);
          let res = await oneRead({ ids });
          if (res.data.code != 0) return false;
          this.getsysMailViewRecord();
          this.getsysReadlyMailViewRecord();
      },
      //单行点击
      rowClick(row) {
          this.dialogFormVisible = true;
          this.messageInfo = row;
      },
      //未读
      async getsysMailViewRecord() {
          let res = await sysMailViewRecord(this.form);
          this.tableData = res.data.data;
          console.log(this.tableData);
      },
      //已读
      async getsysReadlyMailViewRecord() {
          let res = await sysMailViewRecord(this.forms);
          this.tableDatas = res.data.data;
      },
      handleSizeChange(size) {
          this.form.size = size;
          this.form.current = 1;
          this.getsysMailViewRecord();
      },
      handleCurrentChange(current) {
          this.form.current = current;
          this.getsysMailViewRecord();
      },
      tabChange() {
          this.forms.current = 1;
          this.form.current = 1;
          this.getsysMailViewRecord();
          this.getsysReadlyMailViewRecord();
      },
      handleSizeChanges(size) {
          this.forms.size = size;
          this.forms.current = 1;
          this.getsysReadlyMailViewRecord();
      },
      handleCurrentChanges(current) {
          this.forms.current = current;
          this.getsysReadlyMailViewRecord();
      }
  },

  created() {
      this.getsysMailViewRecord();
      this.getsysReadlyMailViewRecord();
  }
};
</script>

<style>
.message-title {
  cursor: pointer;
}

.handle-row {
  margin-top: 30px;
}
</style>

