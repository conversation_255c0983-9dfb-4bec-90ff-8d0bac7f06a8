<template>
    <div>
        <div>
            <el-card shadow="never">
                <div slot="header" class="clearfix">
                    <span>签约管理</span>
                </div>
                <el-form :inline="true" :model="Form" class="demo-form-inline">
                    <el-row>
                        <el-col :span='14'>
                            <el-form-item label="签约状态">
                                <el-select v-model="Form.signStatus" clearable placeholder="请选择">
                                    <el-option :value="1" label="待签署 "> </el-option>
                                    <el-option :value="2" label="已签署 "> </el-option>
                                    <el-option :value="3" label="需更新协议"> </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="签约税地：">
                                <el-select v-model="Form.taxesId" placeholder="请选择" clearable style="width: 200px" filterable>
                                    <el-option v-for="item in taxesList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="合作机构">
                                <el-select v-model="Form.enterpriseId" placeholder="选择机构" clearable filterable>
                                    <el-option
                                        :label="i.enterpriseName"
                                        :value="i.enterpriseId"
                                        v-for="i in listEnterprise"
                                        :key="i.enterpriseId"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="人员姓名">
                                <el-input type="text" v-model="Form.mainBodySearch" placeholder="模糊搜素"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span='10'>
                            <el-form-item>
                                <el-button type="primary" icon="el-icon-search" size="medium" @click="toSearch">查 询 </el-button>
                                <el-button type="primary" size="medium" @click="toManyDo" v-if="qyxAdmin">批量签署</el-button>
                                <el-button type="primary" size="medium" @click="toRegenerationPDF" v-if="qyxAdmin" title='更新协议与用印'>批量更新协议</el-button>
                                <el-button size="medium" @click="toExcel">导出</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <div class="time_box">
                    <div style="width: 260px">
                        <div>
                            <el-menu class="el-menu-vertical-demo" unique-opened @select="handleSelect" :default-active="showIndex">
                                <el-submenu :index="item.signName" v-for="(item, index) in queryList" :key="index">
                                    <template slot="title">
                                        <span>{{ item.signName }}</span>
                                    </template>
                                    <el-menu-item-group>
                                        <el-menu-item
                                            :index="item.signName + ';' + ''"
                                        >全部
                                            <el-tag type="info" style="margin-left: 10px">{{ item.dateDetails.reduce((sum, items) => sum + items.number, 0) }}</el-tag>
                                        </el-menu-item>
                                        <el-menu-item
                                            :index="item.signName + ';' + items.date"
                                            v-for="(items, idx) in item.dateDetails"
                                            :key="idx"
                                            >{{ items.date }}
                                            <el-tag type="info" style="margin-left: 10px">{{ items.number }}</el-tag>
                                        </el-menu-item>
                                    </el-menu-item-group>
                                </el-submenu>
                            </el-menu>
                        </div>
                    </div>
                    <div style="flex: 1">
                        <el-table
                            :data="tableData.results"
                            style="width: 100%"
                            v-loading="loading"
                            @selection-change="handleSelectionChange"
                        >
                            <el-table-column type="selection" width="45" :selectable="selectEnable"> </el-table-column>

                            <el-table-column prop="name" min-width="220" label="合同信息" show-overflow-tooltip fixed="left">
                                <template slot-scope="scope">
                                    <span>{{ scope.row.signInfo.signName }}</span>
                                    <div style='cursor: pointer'
                                    >{{ scope.row.signInfo.enterpriseName }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="account" min-width="220" label="签约主体(公司)">
                                <template slot-scope="scope">
                                    <span>公司名称：{{ scope.row.company.name }}</span>
                                    <div>签署时间：{{ scope.row.company.createTime ? scope.row.company.createTime : '--' }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column min-width="120" label="签约主体(个人)">
                                <template slot-scope="scope">
                                    <span>签约个体：{{ scope.row.single.name }}</span>
                                    <div>签署时间：{{ scope.row.single.createTime ? scope.row.single.createTime : '--' }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column label="签约状态">
                                <template slot-scope="scope">
                                    <span v-if="scope.row.isSign == 1">待签署</span>
                                    <span v-if="scope.row.isSign == 2" :style="{ color: scope.row.signType == 1 ? '#209373' : '#409EFF' }"
                                        >已签署</span
                                    >
                                </template>
                            </el-table-column>
                            <el-table-column label="操作">
                                <template slot-scope="scope">
                                    <el-button type="text" @click="toLookUrl(scope.row.url)" v-if="scope.row.isSign == 2" style="font-size: 15px;"
                                        >查看文件</el-button
                                    >
                                    <el-button
                                        type="text"
                                        @click="toQuiteSign(scope.row)"
                                        v-if="scope.row.isSign == 1 && qyxAdmin"
                                        v-has="'platform_signing_sign'" style="font-size: 15px;"
                                        >签署协议</el-button
                                    >
                                    <div>
                                        <el-button
                                            type="text"
                                            v-if="scope.row.isSign == 1"
                                            style="color: #ff3c2f; font-size: 15px;"
                                            @click="toDetelInfo(scope.row.id)"
                                            v-has="'platform_signing_delete'"
                                            >删除</el-button
                                        >
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div class="pagination">
                            <el-pagination
                                @current-change="handleCurrentChange"
                                :current-page="tableData.current"
                                layout="prev, pager, next, jumper"
                                :total="tableData.total"
                            ></el-pagination>
                        </div>
                    </div>
                </div>
            </el-card>
        </div>
    </div>
</template>

<script>
import {
    signQueryList,
    signList,
    excelOut,
    removeSign,
    quiteSign,
    checkStatus,
    createSilenceSignFileList,
    regenerationSilenceSignFileList,
    getTaxesList
} from '@/api/personal/personal.js';
import { Message } from 'element-ui';
import { getUserInfo } from '@/api/system/login';
import { listEnterpriseList } from '@/api/grant/grant';

export default {
    data() {
        return {
            Form: {
                signStatus: 2, //临时需求：默认显示已签署
                mainBodySearch: undefined,
                date: undefined,
                signName: undefined,
                enterpriseId: undefined,
                taxesId: undefined
            },
            queryList: [],
            tableData: {},
            current: 1,
            size: 10,
            showIndex: '',
            loading: false,
            timeOut: undefined,
            qyxAdmin: false,
            listEnterprise: [],
            taxesList: [],
        };
    },
    methods: {
        //选择左侧栏
        toExcel() {
            excelOut({
                ...this.Form
            }).then((res) => {
                let excel = new Blob([res.data]);
                let url = URL.createObjectURL(excel);
                let a = document.createElement('a');
                a.href = url;
                a.download = '签约管理' + '.xlsx';
                a.click();
            });
        },
        handleSelect(key, keyPath) {
            let infoList = [];
            infoList = key.split(';');
            this.Form.signName = infoList[0];
            this.Form.date = infoList[1];
            this.current = 1;
            this.getData();
        },
        toLookUrl(url) {
            window.open(url, '_blank');
        },
        getData() {
            signList({
                ...this.Form,
                current: this.current,
                size: this.size
            }).then((res) => {
                if (res.data.code == 0) {
                    this.tableData = res.data.data;
                }
            });
        },
        toSearch() {
            this.current = 1;
            this.getData();
        },
        handleSizeChange(size) {
            this.size = size;
            this.current = 1;
            this.getData();
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.current = current;
            this.getData();
        },
        handleSelectionChange(val) {
            let list = [];
            val.forEach((item) => {
                list.push({
                    taxesId: item.company.id,
                    enterpriseId: item.signInfo.id,
                    workerId: item.single.id,
                    id: item.id
                });
            });
            this.multipleSelection = list;
        },
        //撤销签约
        toDetelInfo(id) {
            this.$confirm(`确定要撤销签约吗？`, '提示', {
                type: 'warning'
            })
                .then(() => {
                    removeSign({
                        signId: id
                    }).then((res) => {
                        this.getData();
                    });
                })
                .catch(() => {});
        },
        toQuiteSign(item) {
            this.$confirm(`确定要签署协议吗？`, '提示', {
                type: 'warning'
            })
                .then(() => {
                    quiteSign({
                        taxesId: item.company.id,
                        enterpriseId: item.signInfo.id,
                        workerId: item.single.id
                    }).then((res) => {
                        Message.success(res.data.msg);
                        this.loading = true;
                        this.timeOut = setTimeout(() => {
                            checkStatus({
                                signId: item.id
                            }).then((res) => {
                                if (res.data.code == 0) {
                                    if (res.data.data == 2) {
                                        this.getData();
                                        this.loading = false;
                                        clearTimeout(this.timeOut);
                                    }
                                }
                            });
                        }, 3000);
                    });
                })
                .catch(() => {});
        },
        selectEnable(row, rowIndex) {
            if (row.isSign == 2) {
                return false; // 禁用
            } else {
                return true; // 不禁用
            }
        },
        //批量签署
        toManyDo() {
            this.$confirm(`确定要批量签署协议吗？`, '提示', {
                type: 'warning'
            })
                .then(() => {
                    this.loading = true;
                    createSilenceSignFileList(this.multipleSelection).then((res) => {
                        setTimeout(() => {
                            this.loading = false;
                            this.getData();
                        }, 500 * this.multipleSelection.length);
                    });
                    // this.multipleSelection.forEach((s) => {
                    //     quiteSign(s).then((res) => {
                    //         this.loading = true;
                    //     });
                    //     setTimeout(() => {
                    //         checkStatus({
                    //             signId: s.id
                    //         }).then((res) => {
                    //             if (res.data.code == 0) { 
                    //                 if (res.data.data == 2) {
                    //                     this.loading = false;
                    //                     this.getData();
                    //                 }
                    //             }
                    //         });
                    //     }, 1000 * this.multipleSelection.length);
                    // });
                })
                .catch(() => {});
        },
        //批量重新生成协议文件
        toRegenerationPDF() {
            this.$confirm(`确定要批量签署协议吗？`, '提示', {
                type: 'warning'
            })
                .then(() => {
                    this.loading = true;
                    regenerationSilenceSignFileList(this.multipleSelection).then((res) => {
                        setTimeout(() => {
                            this.loading = false;
                            this.getData();
                        }, 500 * this.multipleSelection.length);
                    });
                })
                .catch(() => {});
        },
    },
    async created() {
        listEnterpriseList().then((res) => {
            if (res.data.code === 0) {
                this.listEnterprise = res.data.data;
            }
        });
        getTaxesList().then((res) => {
            if (res.data.code == 0) {
                this.taxesList = res.data.data;
            }
        });
        signQueryList().then((res) => {
            if (res.data.code == 0) {
                this.queryList = res.data.data;
                this.queryList.forEach(query => {
                    query.dateDetails.sort((a, b) => new Date(b.date) - new Date(a.date));
                });
                this.Form.date = ''; // this.queryList[0].dateDetails[0].date;
                this.Form.signName = this.queryList[0].signName;
                this.showIndex = this.queryList[0].signName + ';';
            }
            this.getData();
        });
        let res = await getUserInfo();
        if (res.data.belongId == '26ff71ec665b4557b4fe64ff1f7d469d') {
            this.qyxAdmin = true;
        } else {
            this.qyxAdmin = false;
        }
    }
};
</script>

<style scoped>
.time_box {
    display: flex;
    width: 100%;
}
</style>