<template>
    <div>
        <el-dialog title="添加白名单人员" :visible.sync="dialogVisible" width="60%" @close="closeDia" append-to-body>
            <el-form label-width="130px" :model="form">
                <el-form-item label="姓名：">
                    <el-input v-model="form.name" clearable style="width: 250px"></el-input>
                </el-form-item>
                <el-form-item label="身份证号：">
                    <el-input v-model="form.idCardNumber" clearable style="width: 250px"></el-input>
                </el-form-item>
                <el-form-item label="银行卡号：">
                    <el-input v-model="form.accountNumber" clearable style="width: 250px"></el-input>
                </el-form-item>
                <el-form-item label="手机号：">
                    <el-input v-model="form.phone" clearable style="width: 250px"></el-input>
                </el-form-item>
                <el-form-item label="创建说明：">
                    <el-input
                        v-model="form.remake"
                        type="textarea"
                        style="width: 250px"
                        maxlength="200"
                        show-word-limit
                        :autosize="{ minRows: 3 }"
                    ></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeDia">取 消</el-button>
                <el-button type="primary" @click="toAddWhitePeople">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { Message } from 'element-ui';

export default {
    data() {
        return {
            dialogVisible: false,
            form: {}
        };
    },
    props: {
        toShowDia: {
            type: Boolean,
            require: true
        }
    },
    watch: {
        toShowDia(val) {
            this.dialogVisible = val;
        }
    },
    methods: {
        closeDia() {
            this.$emit('closeDia');
        },
        toAddWhitePeople() {
            if(!this.form.name) return this.$message.error('请输入姓名！')
            if(!this.form.idCardNumber) return this.$message.error('请输入身份证号！')
            if(!this.form.accountNumber) return this.$message.error('请输入银行卡号！')
            if(!this.form.phone) return this.$message.error('请输入手机号！')
            if(!this.form.remake) return this.$message.error('请输入创建说明！')
            this.$emit('addWhite', this.form);
            this.form = {};
        }
    },
    created() {}
};
</script>

<style scoped>
</style>