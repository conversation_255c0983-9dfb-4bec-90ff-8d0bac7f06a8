<template>
    <div>
        <el-dialog title="白名单添加记录" :visible.sync="dialogVisible" width="80%" @close="closeDia" @open="toOpenDia" append-to-body>
            <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                <el-table-column prop="name" label="姓名" width="140" show-overflow-tooltip fixed="left"></el-table-column>
                <el-table-column prop="idCardNumber" label="身份证号"></el-table-column>
                <el-table-column label="性别" width="80" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span>{{ scope.row.sex == 1 ? '男' : '女' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="年龄" prop="age"> </el-table-column>
                <el-table-column label="出生日期" prop="birthDate"> </el-table-column>
                <el-table-column prop="createTime" label="创建时间" show-overflow-tooltip></el-table-column>
                <el-table-column label="创建人" show-overflow-tooltip prop="createName"> </el-table-column>
                <el-table-column prop="remake" label="创建说明" show-overflow-tooltip></el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page="tableData.current"
                    :page-size="tableData.size"
                    :page-sizes="[10,]"
                    layout="total,sizes, prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeDia">取 消</el-button>
                <el-button type="primary">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { Message } from 'element-ui';
import { getWhiteListPage } from '@/api/personal/personal.js';
export default {
    data() {
        return {
            dialogVisible: false,
            form: {
                size: 10,
                current: 1
            },
            tableData: {}
        };
    },
    props: {
        toShowDia: {
            type: Boolean,
            require: true
        }
    },
    watch: {
        toShowDia(val) {
            this.dialogVisible = val;
        }
    },
    methods: {
        closeDia() {
            this.$emit('closeDia');
        },
        async getWhitePeople() {
            let res = await getWhiteListPage(this.form);
            this.tableData = res.data.data;
        },
        toOpenDia() {
            this.getWhitePeople();
        },
        handleCurrentChange(current) {
            this.form.current = current;
            this.getWhitePeople();
        },
    }
};
</script>

<style scoped>
</style>