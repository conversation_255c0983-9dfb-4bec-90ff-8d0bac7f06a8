<template>
    <div>
        <el-card shadow="never">
            <div slot="header" class="clearfix button-list">
                <span>人员名单</span>
                <div>
                    <el-button style="font-size: 15px" @click="toShowList">白名单添加记录</el-button>
                    <el-button type="primary" style="font-size: 15px" @click="toAddPeople">添加白名单人员</el-button>
                </div>
            </div>
            <el-form :inline="true" :model="Form" class="demo-form-inline">
                <!-- <el-form-item label="关联机构">
                    <el-select v-model="Form.taxesId" clearable placeholder="请选择">
                        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                </el-form-item> -->
                <el-form-item label="是否锁定">
                    <el-select v-model="Form.lockStatus" clearable placeholder="请选择">
                        <el-option v-for="item in lockStatus" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="微信注册">
                    <el-select v-model="Form.wechat" clearable placeholder="请选择">
                        <el-option v-for="item in wechat" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="模糊搜索">
                    <el-input v-model="Form.selectName" placeholder="请选择"></el-input>
                </el-form-item>
                <el-form-item label="注册时间">
                    <el-date-picker
                        v-model="Form.date"
                        type="daterange"
                        range-separator="~"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd"
                        clearable
                        @change="toChangeTime"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="白名单添加">
                    <el-select v-model="Form.isWhite" clearable placeholder="请选择">
                        <el-option v-for="item in isWhite" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button size="medium" @click="toExcel">导 出</el-button>
                    <el-button type="primary" icon="el-icon-search" size="medium" @click="toSearchInfo">查 询 </el-button>
                </el-form-item>
            </el-form>
            <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                <el-table-column prop="name" label="姓名" min-width="80" show-overflow-tooltip fixed="left"></el-table-column>
                <el-table-column prop="idCardNumber" label="身份证号" width="200"></el-table-column>
                <el-table-column label="性别" width="80" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span>{{ scope.row.sex == 1 ? '男' : '女' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="年龄" prop="age"> </el-table-column>
                <el-table-column label="出生日期" prop="birthDate"> </el-table-column>
                <el-table-column prop="createTime" label="注册时间" width="200" show-overflow-tooltip></el-table-column>
                <!-- <el-table-column label="是否签约" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span>{{ scope.row.signing == 0 ? '未签约' : '已签约' }}</span>
                    </template>
                </el-table-column> -->
<!--                <el-table-column label="微信注册" show-overflow-tooltip>-->
<!--                    <template slot-scope="scope">-->
<!--                        <span>{{ scope.row.wechat == 1 ? '已注册' : '未注册' }}</span>-->
<!--                    </template>-->
<!--                </el-table-column>-->
                <el-table-column prop="lastLoginDate" label="最后活跃时间" show-overflow-tooltip></el-table-column>
                <el-table-column label="白名单添加" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span>{{ scope.row.isWhite == 1 ? '否' : '是' }}</span>
                    </template>
                </el-table-column>
                <el-table-column min-width="220" label="操作" fixed="right">
                    <template slot-scope="scope">
                        <el-button @click="handleEdit(scope.row.id)" v-has="'platform_user_edi'" type="text" icon="el-icon-edit" style="font-size: 15px;"
                            >查看详情</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page="tableData.current"
                    layout="prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
        </el-card>
        <detail :dialogVisible="dialogVisible" @closeDia="closeDia" :peopleDetails="peopleDetails"></detail>
        <add :toShowDia="dialogAdd" @closeDia="closeDia" @addWhite="toAddWhitePeople"></add>
        <list :toShowDia="showListDia" @closeDia="closeDia"></list>
    </div>
</template>

<script>
import detail from '../components/detail.vue';
import { peopleList, peopleDetail, peopleExcel, whitePeople } from '@/api/personal/personal.js';
import { getFileNameUUID } from '@/utils/oss.js';
import add from './model/add.vue';
import list from './model/list.vue';
export default {
    components: {
        detail,
        add,
        list
    },
    computed: {},
    data() {
        return {
            Form: {
                name: '',
                phone: ''
            },
            password: '',
            tableData: {},
            current: 1,
            size: 10,
            visible: false,
            resetVisible: false,
            distributeVisible: false,
            form: {},
            dialogType: 0,
            manageVisible: false,
            userId: '',
            options: [],
            dialogVisible: false,
            lockStatus: [
                {
                    name: '未锁定',
                    id: 0
                },
                {
                    name: '已锁定',
                    id: 1
                }
            ],
            wechat: [
                {
                    name: '未注册',
                    id: 0
                },
                {
                    name: '已注册',
                    id: 1
                }
            ],
            isWhite: [
                {
                    name: '是',
                    id: 0
                },
                {
                    name: '否',
                    id: 1
                }
            ],
            peopleDetails: {},
            dialogAdd: false,
            showListDia: false
        };
    },
    mounted() {
        this.getData();
    },
    methods: {
        // 查看详情
        handleEdit(id) {
            peopleDetail({
                workId: id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.peopleDetails = res.data.data;
                    this.dialogVisible = true;
                }
            });
        },
        toExcel() {
            peopleExcel({
                ...this.Form
            }).then((res) => {
                let excel = new Blob([res.data]);
                let url = URL.createObjectURL(excel);
                let a = document.createElement('a');
                a.href = url;
                a.download = '人员名单' + getFileNameUUID() + '.xls';
                a.click();
            });
        },
        getData() {
            peopleList({
                ...this.Form,
                current: this.current,
                size: this.size
            }).then((res) => {
                if (res.data.code === 0) {
                    this.tableData = res.data.data;
                }
            });
        },
        toAddPeople() {
            this.dialogAdd = true;
        },
        toChangeTime(date) {
            if (date) {
                this.Form.startRegistDate = date[0] + ' ' + '00:00:00';
                this.Form.endRegistDate = date[1] + ' ' + '23:59:59';
            } else {
                this.Form.endRegistDate = '';
                this.Form.endRegistDate = '';
            }
        },
        //分页-每页条数改变
        handleSizeChange(size) {
            this.size = size;
            this.current = 1;
            this.getData();
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.current = current;
            this.getData();
        },
        closeDia() {
            this.dialogVisible = false;
            this.dialogAdd = false;
            this.showListDia = false;
        },
        toSearchInfo() {
            peopleList({
                ...this.Form,
                current: 1,
                size: this.size
            }).then((res) => {
                if (res.data.code === 0) {
                    this.tableData = res.data.data;
                }
            });
        },
        toShowList() {
            this.showListDia = true;
        },
        toAddWhitePeople(e) {
            whitePeople(e).then((res) => {
                if (res.data.code != 0) return false;
                this.$message.success('添加成功');
                this.current = 1;
                this.getData();
                this.dialogAdd = false;
            });
        }
    }
};
</script>

<style scoped lang="scss">
.btn {
    color: #ffffff;
    padding: 5px 10px;
    border-radius: 5px;
}

.button-list {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
</style>
