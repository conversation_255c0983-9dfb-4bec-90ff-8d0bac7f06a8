<template>
    <div>
        <el-dialog title="用户详情" :visible.sync="toshowDia" @close="closeDias" width="80%" @open="openDia">
            <div class="people_detail">
                <div class="left_box">
                    <img :src="peopleDetails.avatar" alt="" class="people_head" />
                    <div class="people_name">{{ peopleDetails.name }}</div>
                    <div class="people_info_item">微信绑定：{{ peopleDetails.wechat ? '已绑定' : '未绑定' }}</div>
                    <div class="people_info_item">注册时间：{{ peopleDetails.registDate }}</div>
                    <div class="people_info_item">最后登录：{{ peopleDetails.lastLoginDate }}</div>
                    <el-button type="primary"
                        @click="!peopleDetails.lockStatus ? lockPeople(peopleDetails.id) : unLockPeople(peopleDetails.id)">{{
                            !peopleDetails.lockStatus ? '锁定' : '解锁' }}</el-button>
                </div>
                <el-tabs type="border-card" style="flex: 1;box-shadow: none;">
                    <el-tab-pane label="实名信息">
                        <el-descriptions class="margin-top" title="用户信息" :column="3" border>
                            <el-descriptions-item>
                                <template slot="label"> 真实姓名 </template>
                                {{ peopleDetails.name }}
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template slot="label"> 性别 </template>
                                {{ peopleDetails.sex == 1 ? '男' : '女' }}
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template slot="label"> 民族 </template>
                                汉
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template slot="label"> 年龄 </template>
                                {{ peopleDetails.age }}
                            </el-descriptions-item>
                            <el-descriptions-item :span="2">
                                <template slot="label"> 住址 </template>
                                {{ peopleDetails.address }}
                            </el-descriptions-item>
                            <el-descriptions-item :span="2">
                                <template slot="label"> 身份证号 </template>
                                {{ peopleDetails.idCardNumber }}
                                <el-dropdown style="margin-left: 60px" @command="handleCommand">
                                    <el-button type="primary"> 操作<i class="el-icon-arrow-down el-icon--right"></i>
                                    </el-button>
                                    <el-dropdown-menu slot="dropdown" v-if="peopleDetails.ifUploadIdImg == 1">
                                        <el-dropdown-item command="look">查看照片</el-dropdown-item>
                                        <el-dropdown-item command="upload">重新上传</el-dropdown-item>
                                    </el-dropdown-menu>
                                    <el-dropdown-menu slot="dropdown" v-if="peopleDetails.ifUploadIdImg == 0">
                                        <el-dropdown-item command="upload">上传照片</el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </el-descriptions-item>
                            <el-descriptions-item :span="3">
                                <template slot="label"> 储蓄卡号 </template>
                                {{ peopleDetails.bankNumber }}
                            </el-descriptions-item>
                        </el-descriptions>
                    </el-tab-pane>
                    <el-tab-pane label="最近账单">
                        <el-table :data="tableData.records" style="color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                            <el-table-column label="交易目标" min-width="150" prop="merchantName" show-overflow-tooltip>
                            </el-table-column>
                            <el-table-column prop="belongType" label="所属业务"></el-table-column>
                            <el-table-column prop="phone" label="状态" show-overflow-tooltip>
                                <template slot-scope="scope">
                                    <span v-if="scope.row.payStatus == 1">待支付</span>
                                    <span v-if="scope.row.payStatus == 2">支付中</span>
                                    <span v-if="scope.row.payStatus == 3">已完成</span>
                                    <span v-if="scope.row.payStatus == 4">失败</span>
                                    <span v-if="scope.row.payStatus == 5">已完成有退票</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="数额" prop="payMoney"> </el-table-column>
                            <el-table-column prop="remark" label="备注" show-overflow-tooltip></el-table-column>
                            <el-table-column label="创建时间/交割时间" min-width="200" show-overflow-tooltip>
                                <template slot-scope="scope">
                                    <div>{{ scope.row.createTime }}</div>
                                    <div>{{ scope.row.updateTime }}</div>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div class="pagination">
                            <el-pagination 
                                @current-change="handleCurrentChange"
                                :current-page="tableData.current" 
                                layout="prev, pager, next, jumper" 
                                :total="tableData.total"
                            ></el-pagination>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="电子签约">
                        <el-table :data="selectInfo" style="color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                            <el-table-column label="合同信息" min-width="150" show-overflow-tooltip fixed="left">
                                <template slot-scope="scope">
                                    <span>{{ scope.row.signInfo.signName }}</span>
                                    <div>{{ scope.row.signInfo.enterpriseName }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="account" label="签署主体(公司)">
                                <template slot-scope="scope">
                                    <span>公司名称：{{ scope.row.signInfo.enterpriseName }}</span>
                                    <div>签署时间：{{ scope.row.signInfo.signTime }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="phone" label="签署主体(个人)" min-width="120" show-overflow-tooltip>
                                <template slot-scope="scope">
                                    <span>签署个体：{{ scope.row.single.name }}</span>
                                    <div>签署时间：{{ scope.row.single.createTime }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column label="签署状态">
                                <template slot-scope="scope">
                                    <span :style="{ color: scope.row.signType == 1 ? '#209373' : '#409EFF' }"
                                        v-show="scope.row.isSign == 2">已签署</span>
                                    <span style="color: #ff0000" v-show="scope.row.isSign == 1">未签署</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="最后操作时间" prop="updateTime" show-overflow-tooltip> </el-table-column>
                            <el-table-column label="签署文件" show-overflow-tooltip>
                                <template slot-scope="scope">
                                    <el-button type="text" @click="lookDetail(scope.row.url)" v-show="scope.row.url" style="font-size: 15px;">查看文件</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-tab-pane>
                    <el-tab-pane label="绑定微信">
                        <el-table :data="wxList" style="color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                            <el-table-column label="微信头像" min-width="150" show-overflow-tooltip>
                                <template slot-scope="scope">
                                    <el-image :src="scope.row.avatar" style="width: 60px; height: 60px"></el-image>
                                </template>
                            </el-table-column>
                            <el-table-column prop="wechatNickName" label="微信昵称"></el-table-column>
                            <el-table-column prop="bindDate" label="绑定时间" min-width="120"
                                show-overflow-tooltip></el-table-column>
                            <el-table-column label="操作">
                                <template slot-scope="scope">
                                    <el-button type="text" style="color: #ff0000; font-size: 15px;"
                                        @click="toUnBind(scope.row.id)">解除绑定</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </el-dialog>
        <el-dialog width="60%" title="身份信息上传" :visible.sync="innerVisible" append-to-body>
            <el-form ref="form" label-width="120px">
                <el-form-item label="身份证正面:">
                    <el-upload 
                        class="upload-demo" 
                        drag 
                        action="" 
                        multiple 
                        :show-file-list="false"
                        :http-request="uploadURLFront" 
                        :on-success="uploadSuccess"
                        :on-error="uploadError"
                        :disabled="lookYes"
                    >
                        <div v-if="!idCardFront">
                            <i class="el-icon-upload"></i>
                            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                            <div class="el-upload__tip" slot="tip">只能上传jpg/png文件</div>
                        </div>
                        <div v-if="idCardFront">
                            <el-image :src="ossUrl + idCardFront" alt="" :preview-src-list="srcListFront"></el-image>
                        </div>
                    </el-upload>
                </el-form-item>
                <el-form-item label="身份证反面:">
                    <el-upload 
                        class="upload-demo" 
                        drag 
                        action="" 
                        multiple 
                        :show-file-list="false"
                        :http-request="uploadURLBack" 
                        :on-success="uploadSuccess"
                        :on-error="uploadError"
                        :disabled="lookYes"
                    >
                        <div v-if="!idCardBack">
                            <i class="el-icon-upload"></i>
                            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                            <div class="el-upload__tip" slot="tip">只能上传jpg/png文件</div>
                        </div>
                        <div v-if="idCardBack">
                            <el-image :src="ossUrl + idCardBack" alt="" :preview-src-list="srcListBack"></el-image>
                        </div>
                    </el-upload>
                </el-form-item>
                <el-form-item>
                    <el-button @click="closeDia">取消</el-button>
                    <el-button type="primary" @click="toSubmitPic">确认</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>

<script>
import {
    peopleIdCard,
    peopleDetail,
    peopleWx,
    unBindWx,
    peopleLock,
    peopleUnLock,
    selectSign,
    selectSalaryByWorkId
} from '@/api/personal/personal.js';
import { client, getFileNameUUID, getTimeNow } from '../../../../utils/oss.js';
import { Message } from 'element-ui';
import { OSS_URL } from '@/api/config.js';

export default {
    data() {
        return {
            toshowDia: false,
            innerVisible: false,
            tableData: {
                records: []
            },
            idCardBack: '',
            idCardFront: '',
            lookYes: false,
            srcListFront: [],
            srcListBack: [],
            wxList: [],
            selectInfo: [],
            current: 1,
            size: 10,
            ossUrl: OSS_URL
        };
    },
    props: {
        dialogVisible: {
            type: Boolean,
            require: true
        },
        peopleDetails: {
            type: Object,
            require: false
        }
    },
    watch: {
        dialogVisible: {
            handler(newVal) {
                this.toshowDia = newVal;
            }
        },
        peopleDetails: {
            handler(s) {
                peopleWx({
                    workId: s.id
                }).then((res) => {
                    if (res.data.code == 0) {
                        this.wxList = res.data.data;
                    }
                });
                selectSign({
                    workId: s.id
                }).then((res) => {
                    if (res.data.code == 0) {
                        this.selectInfo = res.data.data;
                    }
                });
            }
        }
    },
    methods: {
        closeDias() {
            this.$emit('closeDia');
        },
        openDia() {
            this.getData();
        },
        lookDetail(url) {
            window.open(url, '_blank');
        },
        lockPeople(id) {
            this.$confirm(`确定要锁定用户吗？`, '提示', {
                type: 'warning'
            }).then(() => {
                peopleLock({
                    workId: id
                }).then((res) => {
                    if (res.data.code == 0) {
                        this.peopleDetails.lockStatus = !this.peopleDetails.lockStatus;
                        Message({
                            message: '操作成功！',
                            type: 'success'
                        });
                        this.$forceUpdate();
                    }
                });
            });
        },
        getData() {
            //最近账单
            selectSalaryByWorkId({
                workerId: this.peopleDetails.id,
                current: this.current,
                size: this.size
            }).then((res) => {
                if (res.data.code == 0 && res.data.data) {
                    this.tableData = res.data.data;
                } else {
                    this.tableData = {
                        records: []
                    }
                }
            });
        },
        handleSizeChange() {
            this.size = size;
            this.current = 1;
            this.getData();
        },
        //分页-当前页改变
        handleCurrentChange() {
            this.current = current;
            this.getData(current);
        },
        unLockPeople(id) {
            this.$confirm(`确定要解锁用户吗？`, '提示', {
                type: 'warning'
            }).then(() => {
                peopleUnLock({
                    workId: id
                }).then((res) => {
                    if (res.data.code == 0) {
                        this.peopleDetails.lockStatus = !this.peopleDetails.lockStatus;
                        Message({
                            message: '操作成功！',
                            type: 'success'
                        });
                        this.$forceUpdate();
                    }
                });
            });
        },
        handleCommand(command) {
            if (command == 'look') {
                this.lookYes = true;
                peopleDetail({
                    workId: this.peopleDetails.id
                }).then((res) => {
                    if (res.data.code == 0) {
                        this.srcListFront.push(res.data.data.idCardFront);
                        this.srcListBack.push(res.data.data.idCardBack);
                        this.idCardBack = res.data.data.idCardBack;
                        this.idCardFront = res.data.data.idCardFront;
                    }
                });
            } else {
                this.lookYes = false;
                this.srcListFront = [];
                this.srcListBack = [];
                this.idCardBack = '';
                this.idCardFront = '';
            }
            this.innerVisible = true;
        },
        handleAvatarSuccess() { },
        uploadSuccess(res) {
            console.log('success: ', res);
        },
        uploadError(err) {
            console.log('error: ', err);
            this.$message.error('上传失败！请重试');
        },
        uploadURLFront(option) {
            var suffix = option.file.name.substring(option.file.name.lastIndexOf('.'));
            //注意哦，这里指定文件夹'image/'，尝试过写在配置文件，但是各种不行，写在这里就可以
            var fileName = '/IDcard/' + getTimeNow() + '/' + getFileNameUUID() + suffix;
            //定义唯一的文件名，打印出来的uid其实就是时间戳
            client()
                .multipartUpload(fileName, option.file, {
                    headers: {
                        'Content-Type': 'image/jpg'
                    },
                    progress: function (percentage, cpt) {
                        option.onProgress({ percent: Math.floor(percentage * 100)});
                    }
                })
                .then((res) => {
                    if(res.res.status === 200) {
                        this.idCardFront = fileName;
                        option.onSuccess(res);
                    }else {
                        option.onError(res);
                    }
                }).catch((err) => {
                    option.onError(err);
                });
        },
        uploadURLBack(option) {
            var suffix = option.file.name.substring(option.file.name.lastIndexOf('.'));
            //注意哦，这里指定文件夹'image/'，尝试过写在配置文件，但是各种不行，写在这里就可以
            var fileName = '/IDcard/' + getTimeNow() + '/' + getFileNameUUID() + suffix;
            //定义唯一的文件名，打印出来的uid其实就是时间戳
            client()
                .multipartUpload(fileName, option.file, {
                    headers: {
                        'Content-Type': 'image/jpg'
                    },
                    progress: function (percentage, cpt) {
                        option.onProgress({ percent: Math.floor(percentage * 100)});
                    }
                })
                .then((res) => {
                    if(res.res.status === 200) {
                        this.idCardBack = fileName;
                        option.onSuccess(res);
                    }else {
                        option.onError(res);
                    }
                }).catch((err) => {
                    option.onError(err);
                });
        },
        toSubmitPic() {
            var formFile = new FormData();
            formFile.append('workId', this.peopleDetails.id);
            formFile.append('idCardBack', this.idCardBack);
            formFile.append('idCardFront', this.idCardFront);
            peopleIdCard(formFile).then((res) => {
                if (res.data.code == 0) {
                    Message({
                        message: '更新成功！',
                        type: 'success'
                    });
                    this.peopleDetails.ifUploadIdImg = 1;
                    this.innerVisible = false;
                    peopleDetail({
                        workId: this.peopleDetails.id
                    });
                }
            });
        },
        closeDia() {
            this.idCardBack = '';
            this.idCardFront = '';
            this.innerVisible = false;
        },
        toUnBind(id) {
            this.$confirm(`确定要解绑微信吗？`, '提示', {
                type: 'warning'
            }).then(() => {
                unBindWx({
                    wxTId: id
                }).then((res) => {
                    if (res.data.data == 0) {
                        peopleWx({
                            workId: this.peopleDetails.id
                        }).then((res) => {
                            if (res.data.code == 0) {
                                this.wxList = res.data.data;
                            }
                        });
                    }
                });
            });
        }
    }
};
</script>

<style scoped>
.button_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.people_detail {
    display: flex;
}

.people_head {
    width: 80px;
    display: block;
    height: 80px;
    margin: 0 auto;
    margin-bottom: 10px;
}

.people_name {
    text-align: center;
    margin-bottom: 10px;
}

.people_info_item {
    margin-bottom: 10px;
}

.left_box {
    margin-right: 40px;
}
</style>
