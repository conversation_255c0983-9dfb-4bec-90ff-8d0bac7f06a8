<template>
    <div>
        <el-card>
            <el-tabs @tab-click="handleClick" style="box-shadow: none;" class="tab-title-lg">
                <el-tab-pane :label="item.name" v-for="(item, index) in statusBox" :key="index" :name="item.id">
                    <el-form :inline="true" ref="form" :model="form" class="demo-form-inline">
                        <el-row>
                            <el-col :span="14">
                                <el-form-item label="钱包选择">
                                    <el-select v-model="form.walletId" placeholder="请选择" clearable filterable>
                                        <el-option :label="i.walletStr" :value="i.walletId" v-for="i in walletBox" :key="i.walletId"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="选择机构">
                                    <el-select v-model="form.enterpriseId" placeholder="选择机构" clearable @change="toChangeValue" filterable>
                                        <el-option
                                            :label="i.enterpriseName"
                                            :value="i.enterpriseId"
                                            v-for="i in listEnterprise"
                                            :key="i.enterpriseId"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="发放渠道">
                                    <el-select v-model="form.dataType" placeholder="请选择" clearable>
                                        <el-option :label="i.name" :value="i.value" v-for="i in dataType" :key="i.value"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="选择项目">
                                    <el-select v-model="form.projectId" placeholder="选择项目" clearable filterable :no-data-text="noDataText">
                                        <el-option :label="i.name" :value="i.id" v-for="i in projectList" :key="i.id"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="创建时间">
                                    <el-date-picker
                                        v-model="form.date"
                                        type="daterange"
                                        range-separator="~"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        value-format="yyyy-MM-dd"
                                        clearable
                                        @change="toChangeTime"
                                    >
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="10">
                                <el-form-item>
                                    <el-button type="primary" icon="el-icon-search" size="medium" @click="getDataNeed">查询 </el-button>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                    <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                        <el-table-column label="创建时间" width="150" show-overflow-tooltip fixed>
                            <template slot-scope="scope">
                                <span>{{ scope.row.createTime.split(' ')[0] }}</span>
                                <br />
                                <span>{{ scope.row.createTime.split(' ')[1] }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="projectName" label="项目" width="200" show-overflow-tooltip fixed></el-table-column>
                        <el-table-column label="钱包" min-width="300" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <span>{{ scope.row.merchantsName }} - {{ scope.row.companyName.replace('有限公司', '') }}</span>
                                <div style="display:flex; align-items: center; ">
                                    {{ scope.row.bankName }}
                                    <img width="16px" src="@/assets/img/zsyh.png" v-show="scope.row.bankName.indexOf('招商') > -1 || scope.row.bankName.indexOf('招行') > -1" style="margin-left: 3px"/>
                                    <img width="16px" src="@/assets/img/alipay.png" v-show="scope.row.bankName.indexOf('支付宝') > -1" style="margin-left: 3px"/>
                                    <img width="16px" src="@/assets/img/pabank.png" v-show="scope.row.bankName.indexOf('平安') > -1" style="margin-left: 3px"/>
                                    <img width="16px" src="@/assets/img/wpay.png" v-show="scope.row.bankName.indexOf('微信') > -1" style="margin-left: 3px" />
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="发起人" prop="createName" width="180"> </el-table-column>
                        <el-table-column label="审核人" prop="auditUserName" width="180"> </el-table-column>
                        <el-table-column label="总金额" prop="billTotalMoney" width="180">
                            <template slot-scope="scope">
                                <span class="money">{{ moneyStr(scope.row.billTotalMoney) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="状态" min-width="200" prop="auditStatusStr" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <span>{{ scope.row.auditStatusStr ? scope.row.auditStatusStr : '操作中' }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="发放渠道" prop="billTotalMoney" width="150" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <span>{{ dataType.find((v) => v.value == scope.row.dataType).name }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="成功金额" prop="billTotalMoney" width="120">
                            <template slot-scope="scope">
                                <span class="money">{{ moneyStr(scope.row.successPayTotalMoney) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="失败金额" prop="billTotalMoney" width="120">
                            <template slot-scope="scope">
                                <span class="money">{{ moneyStr(scope.row.errorPayTotalMoney) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="总/成功/失败（笔）" prop="totalNumber" width="200" align="center">
                            <template slot-scope="scope">
                                <span>{{ scope.row.totalNumber }} / {{ scope.row.successNumber }} / {{ scope.row.failNumber }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column min-width="150" label="备注">
                            <template slot-scope="scope">
                                <span>{{ scope.row.remark != 'undefined' && scope.row.remark ? scope.row.remark : '---' }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" fixed="right" width="170" align="center" header-align="center">
                            <template slot-scope="scope">
                                <!-- <el-button
                                    type="text"
                                    @click="
                                        toJump(
                                            '/grant/batch_apply?id=' +
                                                scope.row.id +
                                                '&projectName=' +
                                                scope.row.projectName +
                                                '&projectGroupName=' +
                                                scope.row.projectGroupName,
                                            scope.row.walletId
                                        )
                                    "
                                    v-show="scope.row.auditStatus == 1"
                                    >审核</el-button
                                >
                                <el-button type="text" v-show="scope.row.auditStatus == 1" @click="toCancel(scope.row.id)">取消发放</el-button> -->
                                <el-button
                                    type="text"
                                    size="medium"
                                    v-if="scope.row.auditStatus != 10 && scope.row.auditStatus != 7"
                                    @click="toJumps('/grant/batch_detail?id=' + scope.row.id + '&projectName=' + scope.row.projectName)"
                                    >详情</el-button
                                >
                                <el-button 
                                    type="text" 
                                    size="medium"
                                    v-if="scope.row.huifuReceipts && scope.row.huifuReceipts.length > 0"
                                    @click="showReceiptDialog(scope.row.huifuReceipts)"
                                >凭证</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
                <div class="pagination">
                    <el-pagination
                        v-if="tableData.total > 0"
                        @current-change="handleCurrentChange"
                        :current-page="form.current"
                        layout="prev, pager, next, jumper"
                        :total="tableData.total"
                    ></el-pagination>
                </div>
                <el-dialog title="审核驳回" :visible.sync="dialogVisible" width="60%">
                    <el-form>
                        <el-form-item label="驳回说明" label-width="120px">
                            <el-input type="textarea" v-model="remark" style="width: 400px"></el-input>
                        </el-form-item>
                    </el-form>
                    <div slot="footer" class="dialog-footer">
                        <el-button @click="closeDia">取 消</el-button>
                        <el-button type="primary" @click="toSureSubmit">确 定</el-button>
                    </div>
                </el-dialog>
            </el-tabs>
        </el-card>
        
        <!-- 汇付凭证对话框 -->
        <el-dialog
            title="汇付凭证"
            :visible.sync="receiptDialogVisible"
            width="400px">
            <div v-for="(receipt, index) in currentReceipts" :key="index" style="margin: 8px 0; display: flex; align-items: center;">
                <span style="width: 220px;">{{ getReceiptTypeName(receipt.type) }}</span>
                <el-button type="text" size="mini" style="margin-right: 10px;" @click="toLookDetail(receipt.ossUrl)">查看</el-button>
                <el-button type="text" size="mini" @click="downloadHuifuReceipt(receipt)">下载</el-button>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="receiptDialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { Message } from 'element-ui';
import { salaryList, listProjectByUserId, verifyWallet, applyInfo, listEnterpriseList } from '@/api/grant/grant.js';
import { walletList } from '@/api/account/account.js';
import { OSS_URL } from '@/api/config.js';
export default {
    data() {
        return {
            statusBox: [
                {
                    name: '全部',
                    id: undefined
                },
                {
                    name: '实名认证',
                    id: '10'
                },
                {
                    name: '待审核',
                    id: '1'
                },
                {
                    name: '风控审核中',
                    id: '2'
                },
                {
                    name: '操作中',
                    id: '3'
                },
                {
                    name: '已完成',
                    id: '4'
                },
                {
                    name: '已取消',
                    id: '5'
                }
            ],
            form: {
                current: 1,
                size: 10,
                projectId: undefined,
                auditStatus: undefined,
                startTime: '',
                endTime: '',
                enterpriseId: ''
            },
            tableData: {},
            tableDatas: [],
            projectList: [],
            listEnterprise: [],
            dialogVisible: false,
            remark: '',
            cancelId: '',
            walletBox: [],
            dataType: [
                {
                    value: 0,
                    name: '平台发放'
                },
                {
                    value: 1,
                    name: 'API发放'
                },
                {
                    value: 3,
                    name: '线下发放'
                },
            ],
            noDataText: '请先选择机构',
            receiptDialogVisible: false,
            currentReceipts: []
        };
    },
    methods: {
        toChangeValue(val) {
            this.form.projectId = undefined;
            this.noDataText = '请先选择机构';
            if(val) {
                listProjectByUserId(val).then((res) => {
                    if (res.data.code === 0) {
                        this.projectList = res.data.data;
                        if(this.projectList.length == 0) {
                            this.noDataText = '当前机构无项目';
                        }
                    }
                });
            } else {
                this.projectList = [];
            }
        },
        getData() {
            if(this.$route.query.current_page) {
                this.form.current = this.$route.query.current_page;
            }
            salaryList({
                ...this.form
            }).then((res) => {
                if (res.data.code === 0) {
                    this.tableData = res.data.data;
                }
            });
        },
        getDataNeed() {
            this.form.current = 1;
            this.getData();
        },
        //分页-每页条数改变
        handleSizeChange(size) {
            this.form.size = size;
            this.form.current = 1;
            this.getData();
        },
        closeDia() {
            this.dialogVisible = false;
            this.remark = '';
            this.cancelId = '';
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.form.current = current;
            this.$router.replace({query: {current_page: current}})
            this.getData(current);
        },
        handleClick(tab, event) {
            if (tab.paneName != '0') {
                this.form.auditStatus = parseInt(tab.name);
                this.form.current = 1;
                this.getData();
            } else {
                this.form.auditStatus = undefined;
                this.form.current = 1;
                this.getData();
            }
        },
        toJump(path, id) {
            verifyWallet({
                walletId: id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.$router.push({
                        path: path,
                        query: {
                            list_page: this.form.current
                        }
                    });
                }
            });
        },
        toJumps(path) {
            this.$router.push({
                        path: path,
                        query: {
                            list_page: this.form.current
                        }
                    });
        },
        toChangeTime(date) {
            if (date) {
                this.form.startTime = date[0] + ' ' + '00:00:00';
                this.form.endTime = date[1] + ' ' + '23:59:59';
            } else {
                this.form.startTime = '';
                this.form.endTime = '';
            }
        },
        toSureSubmit() {
            if (!this.remark) return this.$message.error('请输入驳回理由！');
            applyInfo({
                id: this.cancelId,
                status: 2,
                remark: this.remark
            }).then((res) => {
                if (res.data.code == 0) {
                    Message.success('驳回成功');
                    this.remark = '';
                    this.dialogVisible = false;
                    this.getData();
                }
            });
        },
        toCancel(id) {
            this.cancelId = id;
            this.dialogVisible = true;
        },
        //千分位替换
        moneyStr(money) {
            if (!money) money = 0;
            var str = '￥' + money.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2})
            return str;
        },
        showReceiptDialog(receipts) {
            this.currentReceipts = receipts;
            this.receiptDialogVisible = true;
        },
        getReceiptTypeName(type) {
            const typeMap = {
                0: '发放扣款（企业账户-税地账户）',
                1: '划拨（税地账户-个人账户）',
                3: '失败退款（税地账户-企业账户）',
                4: '提现（个人账户-银行卡）'
            };
            return typeMap[type] || `未知类型(${type})`;
        },
        toLookDetail(url) {
            window.open(OSS_URL + '/' + url);
        },
        downloadHuifuReceipt(receipt) {
            const pdfUrl = OSS_URL + '/' + receipt.ossUrl;
            const receiptTypeName = this.getReceiptTypeName(receipt.type);
            const fileName = `${receiptTypeName}_${new Date().toISOString().slice(0, 10)}.pdf`;
            
            fetch(pdfUrl)
                .then(response => response.blob())
                .then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = fileName;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                })
                .catch(err => {
                    this.$message.error('下载失败，请重试');
                    console.error('下载失败：', err);
                });
        }
    },
    created() {
        this.getData();
        if(this.form.enterpriseId) {
            listProjectByUserId(this.form.enterpriseId).then((res) => {
                if (res.data.code === 0) {
                    this.projectList = res.data.data;
                }
            });
        }
        listEnterpriseList().then((res) => {
            if (res.data.code === 0) {
                this.listEnterprise = res.data.data;
            }
        });
        walletList(this.form.enterpriseId).then((res) => {
            if (res.data.code === 0) {
                this.walletBox = res.data.data.map((s) => {
                    s.walletStr = s.merchantsName + '-' + s.companyName + '-' + s.bankName;
                    return s;
                });
            }
        });
    }
};
</script>

<style>
    .tab-title-lg .el-tabs__item {
        font-size: 16px;
        line-height: 1.5;
        font-weight: 400;
    }

    .tab-title-lg .el-tabs__header {
        margin-bottom: 20px;
    }
</style>
