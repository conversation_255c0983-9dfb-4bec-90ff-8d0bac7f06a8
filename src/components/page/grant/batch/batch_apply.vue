<template>
    <div>
        <el-card class="left" shadow="never">
            <div slot="header" class="clearfix">
                <span>发起付款</span>
            </div>
            <div class="project_name">项目及小组：{{ projectName }} - {{ projectGroupName }}</div>
            <div class="project_top">
                <div class="blue_box">发放人员</div>
            </div>
            <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                <el-table-column label="员工姓名" min-width="150" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <div>{{ scope.row.workerName }}<span class="tip_member">人证合一</span></div>
                        {{ scope.row.idCardNumber }}
                    </template>
                </el-table-column>
                <el-table-column prop="bankNumber" label="银行卡/支付宝"></el-table-column>
                <el-table-column prop="mouthMoney" label="本月已结算" min-width="120" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <div>{{ scope.row.mouthMoney.toLocaleString('en-US') }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="本次结算" prop="payMoney">
                    <template slot-scope="scope">
                        <div>{{ scope.row.payMoney.toLocaleString('en-US') }}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="billExtraMoney" label="手续费" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <div>{{ scope.row.billExtraMoney.toLocaleString('en-US') }}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="createTime" label="模式" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span>{{ scope.row.calculationType == 1 ? '内扣' : '外扣' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="是否签约" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span>{{ scope.row.signing ? '是' : '否' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="付款备注" min-width="200" prop="remark" show-overflow-tooltip></el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page="tableData.current"
                    layout="prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
            <div class="project_top">
                <div class="blue_box">发放金额</div>
            </div>
            <el-form ref="form" label-width="120px">
                <el-form-item label="审核人员">
                    <span>{{ auditDetailInfo.amount.auditUserStr }}</span>
                </el-form-item>
                <el-form-item label="发放钱包">
                    <span>{{ auditDetailInfo.amount.companyName }}</span>
                </el-form-item>
                <el-form-item label="发放人数">
                    <span>{{ auditDetailInfo.amount.workerNumber }}</span>
                </el-form-item>
                <el-form-item label="发放金额">
                    <span class="money">¥ {{ auditDetailInfo.amount.payMoney.toLocaleString('en-US') }}</span>
                </el-form-item>
                <el-form-item label="手续费">
                    <span class="money">¥ {{ auditDetailInfo.amount.billExtraMoney.toLocaleString('en-US') }} </span>
                    手续费模式：外扣，总金额=发放金额*（1+服务费）
                </el-form-item>
                <el-form-item label="发放总金额">
                    <span class="money">¥ {{ auditDetailInfo.amount.billTotalMoney.toLocaleString('en-US') }} </span>
                </el-form-item>
                <el-form-item label="钱包余额">
                    <span class="money">¥ {{ auditDetailInfo.amount.currentMoney.toLocaleString('en-US') }}</span>
                </el-form-item>
            </el-form>
            <div class="project_top">
                <div class="blue_box">进度记录</div>
            </div>
            <el-form style="margin: 0 20px">
                <el-timeline :reverse="true">
                    <el-timeline-item
                        v-for="(activity, index) in activities"
                        :key="index"
                        :color="activity.color"
                        :timestamp="activity.createTime"
                    >
                        {{ activity.createName }}-{{ activity.optName }}
                    </el-timeline-item>
                </el-timeline>
                <!-- <div>
                    <el-button class="button-size" type="danger" plain @click="toShowDia"> 驳回 </el-button>
                    <el-button type="primary" class="button-size" @click="toSureInfo"> 确认发放 </el-button>
                </div> -->
            </el-form>
        </el-card>
        <!-- 审核列表 -->
        <el-dialog title="审核驳回" :visible.sync="dialogVisible" width="60%">
            <el-form :model="forms">
                <el-form-item label="驳回状态" label-width="120px">
                    <el-radio-group v-model="forms.statuss">
                        <el-radio label="1">重新修改</el-radio>
                        <el-radio label="2">取消付款</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="驳回说明" label-width="120px">
                    <el-input type="textarea" v-model="forms.remark" style="width: 400px"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="toSureSubmit">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { Message } from 'element-ui';
import { batchDetailPeopleList, auditDetail, applyInfo, auditPass } from '@/api/grant/grant.js';

export default {
    data() {
        return {
            tableData: {},
            current: 1,
            size: 10,
            form: {
                size: 10,
                id: 1
            },
            projectName: '',
            id: undefined,
            projectGroupName: '',
            auditDetailInfo: {},
            activities: [],
            dialogVisible: false,
            forms: {}
        };
    },
    mounted() {
        this.getData();
    },
    methods: {
        getData() {
            //发放人员
            batchDetailPeopleList({
                ...this.form,
                id: this.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.tableData = res.data.data;
                }
            });
            auditDetail({
                id: this.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.auditDetailInfo = res.data.data;
                    res.data.data.record.forEach((v) => {
                        v.color = '';
                        if (v.optType == 1) {
                            v.color = '#1890ff';
                        } else if (v.optType == 2) {
                            v.color = '#52c41a';
                        } else {
                            v.color = '#f5222d';
                        }
                    });
                    this.activities = res.data.data.record;
                }
            });
        },
        //分页-每页条数改变
        handleSizeChange(size) {
            this.form.size = size;
            this.form.current = 1;
            this.getData();
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.form.current = current;
            this.getData();
        },
        toShowDia() {
            this.dialogVisible = true;
        },
        toSureSubmit() {
            if (!this.forms.remark) return this.$message.error('请输入驳回理由！');
            this.$confirm(`确定提交驳回信息吗？`, '提示', {
                type: 'warning'
            }).then(() => {
                applyInfo({
                    ...this.forms,
                    id: this.id,
                    status: 2
                }).then((res) => {
                    if (res.data.code == 0) {
                        Message.success('提交成功');
                        this.$router.go(-1);
                    }
                });
            });
        },
        async toSureInfo() {
            let ress = await this.$saveCode();
            if (ress) {
                auditPass({
                    id: this.id,
                    password: ress.password
                }).then((res) => {
                    if (res.data.code == 0) {
                        Message.success('发放成功');
                        this.$router.go(-1);
                    }
                });
            }
        }
    },
    created() {
        this.projectName = this.$route.query.projectName;
        this.id = this.$route.query.id;
        this.projectGroupName = this.$route.query.projectGroupName;
    }
};
</script>

<style scoped>
.project_name {
    font-size: 18px;
    font-weight: 550;
}
.blue_box {
    padding: 8px 16px;
    background-color: #f9fafc;
    border-radius: 4px;
    border-left: 5px solid #7ca0e5;
    margin: 20px 0;
    width: 300px;
}
.project_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.tip_member {
    border: 1px solid #000;
    color: #000;
    margin-left: 10px;
}
.money {
    font-size: 20px;
    font-weight: 550;
}
.button-size {
    width: 180px;
    height: 40px;
    font-size: 18px;
    margin-top: 20px;
    margin-bottom: 40px;
}
</style>