<template>
    <div>
        <el-card class="left" shadow="never">
            <div slot="header" class="clearfix">
                <el-button type="text" @click="navigatorBack" style="font-size: 19px;"><i class="el-icon-d-arrow-left"></i> 返回</el-button>
                <el-divider direction="vertical"></el-divider>
                <span>{{ projectName }}</span>
            </div>
            <el-tabs type="border-card" @tab-click="toClickTab" style="box-shadow: none;">
                <el-tab-pane label="发放人员">
                    <el-table :data="tableData.records" style="width: 100%; color: rgba(0, 0, 0, 0.65)" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                        <el-table-column label="员工姓名" min-width="150" show-overflow-tooltip fixed>
                            <template slot-scope="scope">
                                <div>{{ scope.row.workerName }}<span class="tip_member">人证合一</span></div>
                                {{ scope.row.idCardNumber }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="bankNumber" label="银行卡/支付宝" width="220"></el-table-column>
                        <el-table-column prop="mouthMoney" label="本月已结算" min-width="100" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <div class="money_normal">{{scope.row.mouthMoney ? '￥' + scope.row.mouthMoney.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}):'---' }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column label="本次结算" prop="payMoney" min-width="100">
                            <template slot-scope="scope">
                                <div class="money_normal">{{ scope.row.payMoney ? '￥' + scope.row.payMoney.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) : '---' }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="billExtraMoney" label="手续费" show-overflow-tooltip min-width="100">
                            <template slot-scope="scope">
                                <div class="money_normal">{{ scope.row.billExtraMoney ? '￥' + scope.row.billExtraMoney.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) : '---' }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="createTime" label="模式" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <span>{{ scope.row.calculationType == 1 ? '内扣' : '外扣' }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="是否签约" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <span>{{ scope.row.signing ? '是' : '否' }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="付款备注" min-width="200" prop="remark" show-overflow-tooltip></el-table-column>
                    </el-table>
                </el-tab-pane>
                <el-tab-pane label="发放金额">
                    <el-form label-width="120px" style="width: 50%">
                        <el-form-item label="审核人员">
                            <span>{{ batchDetailInfo.amount.auditUserStr }}</span>
                        </el-form-item>
                        <el-form-item label="发放钱包">
                            <span>{{ batchDetailInfo.amount.companyName }}</span>
                        </el-form-item>
                        <el-form-item label="发放人数">
                            <span>
                                <span style="color: #00a6ff">{{ batchDetailInfo.amount.successWorkerNum }}</span>
                                 / 
                                <span style="color: #ff0000">{{ batchDetailInfo.amount.errorWorkerNum }}</span> （成功/失败）</span>
                        </el-form-item>
                        <el-form-item label="汇付凭证：" v-if="batchDetailInfo.huifuReceipts && batchDetailInfo.huifuReceipts.length > 0">
                            <div v-for="(receipt, index) in batchDetailInfo.huifuReceipts" :key="index" style="margin-bottom: 5px;">
                                <span>{{ getReceiptTypeName(receipt.type) }}</span>
                                <el-button type="text" @click="toLookDetail(receipt.ossUrl)" style="margin-left: 10px;">查看</el-button>
                                <el-button type="text" @click="downloadHuifuReceipt(receipt)" style="margin-left: 5px;">下载</el-button>
                            </div>
                        </el-form-item>
                        <div style="width: 480px">
                            <el-divider>成功发放</el-divider>
                        </div>
                        <el-form-item label="发放金额">
                            <span class="money_normal" style="font-size: 20px">{{ batchDetailInfo.amount.successPayMoney ? '￥' + batchDetailInfo.amount.successPayMoney.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) : '---' }}</span>
                        </el-form-item>
                        <el-form-item label="手续费">
                            <span class="money_normal"
                                ><span style="font-size: 20px; margin-right: 20px"
                                    >{{batchDetailInfo.amount.successBillExtraMoney ? '￥' + batchDetailInfo.amount.successBillExtraMoney.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) : '---' }}</span
                                >
                                <!-- 手续费模式：外扣，总金额=发放金额*（1+服务费）</span
                            > -->
                            </span>
                        </el-form-item>
                        <el-form-item label="发放总金额">
                            <span class="money_normal" style="font-size: 20px">{{batchDetailInfo.amount.successPayTotalMoney ? '￥' + batchDetailInfo.amount.successPayTotalMoney.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) : '---' }}</span>
                        </el-form-item>
                        <div style="width: 480px">
                            <el-divider>失败发放</el-divider>
                        </div>
                        <el-form-item label="发放金额">
                            <span class="money_normal" style="font-size: 20px">{{batchDetailInfo.amount.errorPayMoney ? '￥' + batchDetailInfo.amount.errorPayMoney.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) : '---' }}</span>
                        </el-form-item>
                        <el-form-item label="手续费">
                            <span class="money_normal"
                                ><span style="font-size: 20px; margin-right: 20px"
                                    >{{batchDetailInfo.amount.errorBillExtraMoney ? '￥' + batchDetailInfo.amount.errorBillExtraMoney.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) : '---' }}</span
                                >
                                <!-- 手续费模式：外扣，总金额=发放金额*（1+服务费）</span
                            > -->
                            </span>
                        </el-form-item>
                        <el-form-item label="发放总金额">
                            <span class="money_normal" style="font-size: 20px">{{ batchDetailInfo.amount.errorPayTotalMoney ? '￥' + batchDetailInfo.amount.errorPayTotalMoney.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) : '---' }}</span>
                        </el-form-item>
                    </el-form>
                </el-tab-pane>
                <el-tab-pane label="发放记录">
                    <div class="project_top">
                        <div class="blue_box">
                            表头：<span>{{ headerName.headerName }}</span>
                        </div>
                        <el-button type="primary" style="margin-left: 20px; font-size: 15px" @click="toExcel">导出</el-button>
                        <el-button type="primary" style="font-size: 15px" @click="toNewPZ">恢复凭证</el-button>
                    </div>
                    <el-table :data="tableDatas.records" style="width: 100%; color: rgba(0, 0, 0, 0.65)" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                        <el-table-column label="商户钱包" width="200" show-overflow-tooltip fixed>
                            <template slot-scope="scope">
                                <span>{{ scope.row.merchantsName }} - {{ scope.row.companyName.replace('有限公司', '') }}</span>
                                <div style="display:flex; align-items: center; ">
                                    {{ scope.row.bankName }}
                                    <img width="20px" src="@/assets/img/zsyh.png" v-show="scope.row.bankName.indexOf('招商') > -1 || scope.row.bankName.indexOf('招行') > -1" style="margin-left: 3px"/>
                                    <img width="20px" src="@/assets/img/alipay.png" v-show="scope.row.bankName.indexOf('支付宝') > -1" style="margin-left: 3px"/>
                                    <img width="20px" src="@/assets/img/pabank.png" v-show="scope.row.bankName.indexOf('平安') > -1" style="margin-left: 3px"/>
                                    <img width="16px" src="@/assets/img/wpay.png" v-show="scope.row.bankName.indexOf('微信') > -1" style="margin-left: 3px" />
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="bankNumber" label="项目" width="150">
                            <template slot-scope="scope">
                                <div>{{ scope.row.projectName }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column label="收款人" width="220" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <div v-if=" scope.row.workerName"><i class="el-icon-user"></i> {{ scope.row.workerName }}</div>
                                <div v-if=" scope.row.idCardNumber"><i class="el-icon-postcard"></i> {{ scope.row.idCardNumber }}</div>
                                <div v-if=" scope.row.phone"><i class="el-icon-phone-outline"></i> {{ scope.row.phone }}</div>
                                <div v-if=" scope.row.bankNumber"><i class="el-icon-bank-card"></i> {{ scope.row.bankNumber }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="orderNo" label="流水单号" width="200" show-overflow-tooltip></el-table-column>
                        <el-table-column label="发放金额" prop="payMoney" width="120">
                            <template slot-scope="scope">
                                <span class="money_normal">{{scope.row.payMoney ? '￥' + scope.row.payMoney.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) : '---' }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="payStatusStr" label="付款状态" width="100" show-overflow-tooltip></el-table-column>
                        <el-table-column prop="bankRemark" label="渠道结果" width="150" show-overflow-tooltip> </el-table-column>
                        <el-table-column label="付款备注" show-overflow-tooltip prop="remark" width="150"> </el-table-column>
                        <el-table-column label="创建时间/交割时间" show-overflow-tooltip width="200">
                            <template slot-scope="scope">
                                <span>{{ scope.row.createTime }}</span>
                                <div>{{ scope.row.updateTime }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column label="付款凭证" min-width="150" show-overflow-tooltip fixed="right">
                            <template slot-scope="scope">
                                <template v-if="scope.row.huifuReceipts && scope.row.huifuReceipts.length > 0">
                                    <el-button type="text" @click="showReceiptDialog(scope.row.huifuReceipts)">查看凭证</el-button>
                                </template>
                                <template v-else>
                                    <el-button type="text" v-if="scope.row.picFileUrl" @click="toLookDetail(scope.row.picFileUrl)">查看凭证</el-button><br/>
                                    <el-button type="text" v-if="scope.row.picFileUrl" @click="toDownload(scope.row)">下载凭证</el-button>
                                    <el-button type="text" style="font-size: 15px;" v-else>---</el-button>
                                </template>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
                <el-tab-pane label="流水记录">
                    <el-table :data="tableDatass.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                        <el-table-column prop="companyName" label="商户钱包" width="200" show-overflow-tooltip fixed>
                            <template slot-scope="scope">
                                <span>{{ scope.row.merchantsName }} - {{ scope.row.companyName.replace('有限公司', '') }}</span>
                                <div style="display:flex; align-items: center; ">
                                    {{ scope.row.bankName }}
                                    <img width="16px" src="@/assets/img/zsyh.png" v-show="scope.row.bankName.indexOf('招商') > -1 || scope.row.bankName.indexOf('招行') > -1" style="margin-left: 3px"/>
                                    <img width="16px" src="@/assets/img/alipay.png" v-show="scope.row.bankName.indexOf('支付宝') > -1" style="margin-left: 3px"/>
                                    <img width="16px" src="@/assets/img/pabank.png" v-show="scope.row.bankName.indexOf('平安') > -1" style="margin-left: 3px"/>
                                    <img width="16px" src="@/assets/img/wpay.png" v-show="scope.row.bankName.indexOf('微信') > -1" style="margin-left: 3px" />
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="enterpriseName" label="交易目标" width="200" show-overflow-tooltip></el-table-column>
                        <el-table-column prop="businessTypeStr" label="所属业务" width="120" show-overflow-tooltip></el-table-column>
                        <el-table-column label="状态" prop="payStatusStr" width="100" show-overflow-tooltip></el-table-column>
                        <!-- <el-table-column prop="billExtraMoney" label="drawee" show-overflow-tooltip></el-table-column> -->
                        <el-table-column prop="money" label="数额" show-overflow-tooltip width="100">
                            <template slot-scope="scope">
                                <span class="money_normal">{{scope.row.money ? '￥' + scope.row.money.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) : '---' }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="remark" label="备注" width="200" show-overflow-tooltip> </el-table-column>
                        <el-table-column label="创建时间/交割时间" width="200" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <span>{{ scope.row.createTime }}</span>
                                <div>{{ scope.row.updateTime }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column label="付款凭证" min-width="150" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <el-button type="text" v-if="scope.row.picFileUrl" @click="toLookDetail(scope.row.picFileUrl)">查看凭证</el-button><br/>
                                <el-button type="text" v-if="scope.row.picFileUrl" @click="toDownload(scope.row)">下载凭证</el-button>
                                <el-button type="text" v-if="!scope.row.picFileUrl">-</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
                <el-tab-pane label="进度记录">
                    <el-timeline :reverse="true">
                        <el-timeline-item
                            v-for="(activity, index) in activities"
                            :key="index"
                            :color="activity.color"
                            :timestamp="activity.createTime"
                        >
                            {{ activity.createName }}-{{ activity.optName }}
                        </el-timeline-item>
                    </el-timeline>
                </el-tab-pane>
                <el-tab-pane label="实名认证失败人员">
                    <div class="btn-box">
                        <el-button style="font-size: 16px" type="danger" @click="toExcelError">导出认证失败名单</el-button>
                    </div>
                    <el-table :data="tableDatasss.records" style="width: 100%; color: rgba(0, 0, 0, 0.65)" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                        <el-table-column label="员工姓名" min-width="150" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <div>{{ scope.row.workerName }}</div>
                                {{ scope.row.idCardNumber }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="bankNumber" label="银行卡"></el-table-column>
                        <el-table-column label="本次结算" prop="payMoney">
                            <template slot-scope="scope">
                                {{scope.row.payMoney ? '￥' + scope.row.payMoney.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) : '---' }}
                            </template>
                        </el-table-column>
                        <el-table-column label="付款备注" min-width="200" prop="remark" show-overflow-tooltip></el-table-column>
                        <el-table-column label="认证失败原因" min-width="200" prop="remark" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <span style="color: #ff6362">{{ scope.row.bankRemark }}</span>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
                <div class="pagination">
                    <el-pagination
                        @current-change="handleCurrentChange"
                        :current-page="form.current"
                        layout="prev, pager, next, jumper"
                        :total="total"
                        v-if="paneIndex != 1 && paneIndex != 4"
                    ></el-pagination>
                </div>
            </el-tabs>
        </el-card>
        
        <!-- 汇付凭证对话框 -->
        <el-dialog
            title="汇付凭证"
            :visible.sync="receiptDialogVisible"
            width="400px">
            <div v-for="(receipt, index) in currentReceipts" :key="index" style="margin: 8px 0; display: flex; align-items: center;">
                <span style="width: 220px;">{{ getReceiptTypeName(receipt.type) }}</span>
                <el-button type="text" size="mini" style="margin-right: 10px;" @click="toLookDetail(receipt.ossUrl)">查看</el-button>
                <el-button type="text" size="mini" @click="downloadHuifuReceipt(receipt)">下载</el-button>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="receiptDialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import {
    batchDetail,
    batchDetailPeopleList,
    batchDetailGrantRecord,
    batchDetailFlowRecord,
    getHeaderName,
    exportBatchSalaryBillDetails,
    errorMessagePage,
    errorDownload,
    excelErrorV2,
    restorePayment
} from '@/api/grant/grant.js';
import { OSS_URL } from '@/api/config.js';
export default {
    data() {
        return {
            id: undefined,
            projectName: undefined,
            batchDetailInfo: {
                amount: {}
            },
            form: {
                current: 1,
                size: 10
            },
            tableData: {},
            paneIndex: 0,
            activities: [],
            tableDatas: {},
            tableDatass: {},
            tableDatasss: {},

            total: 0,
            headerName: {},
            receiptDialogVisible: false,
            currentReceipts: []
        };
    },
    methods: {
        //分页-每页条数改变
        handleSizeChange(size) {
            this.form.size = size;
            this.form.current = 1;
            this.getData();
        },
        async toNewPZ() {
            let res = await restorePayment(this.$route.query.id);
            if (res.data.code != 0) return false;
            this.$msgbox({
                title: '操作成功',
                message: '付款批次正在恢复（恢复时间大约5-15分钟）',
                showCancelButton: true,
                confirmButtonText: '确定'
            }).then((action) => {});
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.form.current = current;
            this.getData();
        },
        getData() {
            //发放人员
            batchDetailPeopleList({
                ...this.form,
                id: this.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.tableData = res.data.data;
                    if (this.paneIndex == 0) {
                        this.total = res.data.data.total;
                    }
                }
            });
            //发放记录
            batchDetailGrantRecord({
                ...this.form,
                id: this.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.tableDatas = res.data.data;
                    if (this.paneIndex == 2) {
                        this.total = res.data.data.total;
                    }
                }
            });
            //流水记录
            batchDetailFlowRecord({
                ...this.form,
                id: this.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.tableDatass = res.data.data;
                    if (this.paneIndex == 3) {
                        this.total = res.data.data.total;
                    }
                }
            });
            //失败人
            errorMessagePage({
                ...this.form,
                id: this.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.tableDatasss = res.data.data;
                    if (this.paneIndex == 5) {
                        this.total = res.data.data.total;
                    }
                }
            });
        },
        toClickTab(tab, event) {
            this.paneIndex = parseInt(tab.paneName);
            this.form.current = 1;
            this.getData();
        },
        toLookDetail(url) {
            window.open(OSS_URL + '/' + url);
        },
        toDownload(row){
            // rename and download pdf file
            var pdfUrl = OSS_URL + '/' + row.picFileUrl;
            // 流水号_姓名_金额_时间.pdf,
            var pdfName =
                (row.orderNo || "") + '_' +
                (row.workerName || "") + '_' +
                (row.money || "") + '_' +
                (row.createTime.replace(/[:]/g, '')) + '.pdf';
            if (window.navigator.msSaveBlob) {
                var xhr = new XMLHttpRequest();
                xhr.open('GET', pdfUrl, true);
                xhr.responseType = 'arraybuffer';
                xhr.onload = function() {
                    if (this.status === 200) {
                        var blob = new Blob([this.response], { type: 'application/pdf' });
                        window.navigator.msSaveBlob(blob, pdfName);
                    }
                };
                xhr.send();
            } else {
                fetch(pdfUrl)
                    .then(response => response.blob())
                    .then(blob => {
                        var url = window.URL.createObjectURL(blob);
                        var a = document.createElement('a');
                        a.href = url;
                        a.download = pdfName;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                    });
            }
        },
        async toExcel() {
            let s = await this.$exportDia();
            let res = await exportBatchSalaryBillDetails(this.$route.query.id, s);
            // let excel = new Blob([res.data]);
            // let url = URL.createObjectURL(excel);
            // let a = document.createElement('a');
            // a.href = url;
            // let suf = '';
            // if (s.isFile == 0) {
            //     suf = '.xlsx';
            // } else {
            //     suf = '.zip';
            // }
            // a.download = this.headerName.headerName + suf;
            // a.click();
            this.$confirm('已加入下载队列，是否前往下载中心查看?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'success'
            }).then(() => {
                this.$router.push('/system/downloadCenter');
            });
        },
        async toExcelError() {
            let res = await excelErrorV2(this.$route.query.id);
            if (res.data.code != 0) return false;
            errorDownload({
                fileName: res.data.data
            }).then((ress) => {
                this.blobUrl = ress.data;
                let excel = new Blob([this.blobUrl]);
                let url = URL.createObjectURL(excel);
                let a = document.createElement('a');
                a.href = url;
                a.download = '认证失败名单.xlsx';
                a.click();
            });
        },
        navigatorBack() {
            this.$router.replace({
                path: '/grant/batch',
                query: {
                    current_page: this.$route.query.list_page
                }
            });
        },
        showReceiptDialog(receipts) {
            this.currentReceipts = receipts;
            this.receiptDialogVisible = true;
        },
        getReceiptTypeName(type) {
            const typeMap = {
                0: '发放扣款（企业账户-税地账户）',
                1: '划拨（税地账户-个人账户）',
                3: '失败退款（税地账户-企业账户）',
                4: '提现（个人账户-银行卡）'
            };
            return typeMap[type] || `未知类型(${type})`;
        },
        downloadHuifuReceipt(receipt) {
            const pdfUrl = OSS_URL + '/' + receipt.ossUrl;
            const receiptTypeName = this.getReceiptTypeName(receipt.type);
            const fileName = `${receiptTypeName}_${new Date().toISOString().slice(0, 10)}.pdf`;
            
            fetch(pdfUrl)
                .then(response => response.blob())
                .then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = fileName;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                })
                .catch(err => {
                    this.$message.error('下载失败，请重试');
                    console.error('下载失败：', err);
                });
        }
    },
    created() {
        this.id = this.$route.query.id;
        this.projectName = this.$route.query.projectName;
        batchDetail({
            id: this.id
        }).then((res) => {
            if (res.data.code === 0) {
                this.batchDetailInfo = res.data.data;
                res.data.data.record.forEach((v) => {
                    v.color = '';
                    if (v.optType == 1) {
                        v.color = '#1890ff';
                    } else if (v.optType == 2) {
                        v.color = '#52c41a';
                    } else {
                        v.color = '#f5222d';
                    }
                });
                this.activities = res.data.data.record;
            }
        });
        this.getData();
        getHeaderName(this.$route.query.id).then((res) => {
            if (res.data.code != 0) return false;
            this.headerName = res.data.data;
        });
    }
};
</script>

<style scoped>
.btn-box {
    padding-bottom: 15px;
    text-align: right;
}

.tip_member {
    border: 1px solid #000;
    color: #000;
    margin-left: 10px;
}
.project_name {
    font-size: 18px;
    font-weight: 550;
}
.blue_box {
    padding: 8px 16px;
    background-color: #f9fafc;
    border-radius: 4px;
    border-left: 5px solid #7ca0e5;
    margin: 20px 0;
    min-width: 400px;
}
.project_top {
    display: flex;
    align-items: center;
}
.tip_member {
    border: 1px solid #000;
    color: #000;
    margin-left: 10px;
}
.money {
    font-size: 20px;
    font-weight: 550;
}
.button-size {
    width: 180px;
    height: 40px;
    font-size: 18px;
    margin-top: 20px;
    margin-bottom: 40px;
}
.modify_btn {
    margin-left: 20px;
    font-size: 16px;
}
.red_word {
    color: #ff0000;
}
</style>
