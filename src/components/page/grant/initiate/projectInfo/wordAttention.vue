<template>
    <div >
        <div class="black-bg" @click="toCloseAtton"></div>
        <div class="el-card-box">
            <el-card>
                <div class="el-card-top">
                    <i class="el-icon-warning"></i>
                    温馨提示：
                </div>
                <div class="blue-word">您的付款备注包含敏感词，请重新修改文件内的付款备注再次导入！</div>
                <div class="yellow-word">
                    敏感词包含：<span v-for="(item, index) in wordListData" :key="index"
                        >{{ item }}<span v-if="index<wordListData.length-1">、</span></span
                    >
                </div>
                <div style="text-align: center">
                    <el-button type="primary" class="el-card-btn" @click="toCloseAtton">我知道了</el-button>
                </div>
            </el-card>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            wordListData:[]
        };
    },
    methods: {
        toCloseAtton() {
            this.$emit('closeDia');
        }
    },
    props: {
        wordList: {
            type: Array,
            require: true
        }
    },
    watch: {
   
    },
    created(){
        this.wordListData = this.wordList
    },
    computed: {}
};
</script>

<style scoped>
.project_name {
    font-size: 18px;
    font-weight: 550;
}
.blue_box {
    padding: 8px 16px;
    background-color: #f9fafc;
    border-radius: 4px;
    border-left: 5px solid #7ca0e5;
    margin: 20px 0;
    width: 300px;
}
.project_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.tip_member {
    border: 1px solid #000;
    color: #000;
    margin-left: 10px;
}
.money {
    font-size: 20px;
    font-weight: 550;
}
.button-size {
    width: 180px;
    height: 40px;
    font-size: 18px;
    margin-top: 20px;
    margin-bottom: 40px;
}
.black-bg {
    position: fixed;
    top: 0;
    width: 100%;
    height: 100%;
    left: 0;
    background: rgba(0, 0, 0, 0.6);
    z-index: 98;
}
.el-card-box {
    position: fixed;
    top: 40%;
    left: 50%;
    width: 80vh;
    transform: translate(-50%, -50%);
    z-index: 999;
}
.blue-word {
    font-size: 18px;
    color: #409eff;
    margin-bottom: 20px;
}
.el-card-top {
    font-size: 17px;
    color: #409eff;
    font-weight: 600;
    margin-bottom: 30px;
}
.yellow-word {
    color: #f39c2e;
    font-size: 18px;
}
.el-card-btn {
    width: 180px;
    height: 40px;
    font-size: 18px;
    margin-top: 30px;
    margin-bottom: 3px;
}
</style>