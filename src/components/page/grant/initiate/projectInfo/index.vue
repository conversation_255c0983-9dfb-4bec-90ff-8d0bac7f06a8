<template>
    <div>
        <div class="project_name">项目及小组：{{ projectInfo.name }} -{{ groupInfoItem.name }}</div>
        <div class="project_top">
            <div class="blue_box">发放人员</div>
            <div>
                <el-button style="font-size: 16px" @click="toShowDia" v-has="'salary_importEmployees'">文件批量导入</el-button>
                <el-button type="primary" style="font-size: 16px" @click="toAddPeople" v-has="'salary_addPayee'">增加收款人</el-button>
            </div>
        </div>
        <el-table :data="tableData" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell" :key="calculationType" max-height="663">
            <el-table-column type="index" :index="indexMethod"> </el-table-column>
            <el-table-column label="员工姓名" width="180" show-overflow-tooltip>
                <template slot-scope="scope">
                    <div>{{ scope.row.employeeName }}<span class="tip_member" v-if="scope.row.witnessBoolean">人证合一</span></div>
                    {{ scope.row.identification }}
                </template>
            </el-table-column>
            <el-table-column label="银行卡" width="180">
                <template slot-scope="scope">
                    <div>{{ scope.row.bankCard }}</div>
                    {{ scope.row.phoneNumber }}
                </template>
            </el-table-column>
            <el-table-column prop="mouthMoney" label="本月已结算" width="120" show-overflow-tooltip></el-table-column>
            <el-table-column label="本次结算">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.money" type="number" min="1" @blur="toAddNew(scope.row.money)"></el-input>
                </template>
            </el-table-column>
            <el-table-column label="是否签约" show-overflow-tooltip>
                <template slot-scope="scope">
                    <div :style="{ color: scope.row.signingBoolean ? '' : '#ff0000' }">
                        {{ scope.row.signingBoolean ? '已签约' : '未签约' }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="工作组" show-overflow-tooltip>
                <template slot-scope="scope">
                    <div :style="{ color: scope.row.workingGroupBoolean ? '' : '#ff0000' }">
                        {{ scope.row.workingGroupBoolean ? '已在组内' : '未在组内' }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="付款备注" min-width="200" show-overflow-tooltip>
                <template slot-scope="scope">
                    <el-input v-model="scope.row.remark" @blur="toAddNewReamke()"></el-input>
                </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right">
                <template slot-scope="scope">
                    <el-button @click="handleEdit(scope.row.workerId)"  type="text" style="color: #f56c6c"
                        >删除</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <div class="project_top">
            <div class="blue_box">发放金额</div>
        </div>
        <el-form ref="form" label-width="120px">
            <el-form-item label="选择钱包">
                <el-select v-model="form.wallet" placeholder="选择钱包" @change="toChangeWallet" clearable value-key="id" filterable>
                    <el-option :label="i.walletStr" :value="i" v-for="i in walletList" :key="i.id"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="选择审核人员">
                <el-select v-model="form.auditorId" placeholder="选择审核人员" @change="toChangeInput">
                    <el-option :label="i.name" :value="i.id" v-for="i in auditPersonList" :key="i.id"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="发放人数">
                <span>{{ tableData.length }}</span>
            </el-form-item>
            <el-form-item label="钱包余额">
                <span class="money_normal">{{ showMoney.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</span>
            </el-form-item>
            <el-alert title="确认发放后人员将自动加入该项目小组内" type="warning" show-icon :closable="false"> </el-alert>
            <el-form-item>
                <el-button class="button-size" @click="toBeforetSepts"> 上一步 </el-button>
                <!-- <el-button type="primary" class="button-size" @click="toNextSepts"> 确认发放 </el-button> -->
                <el-button type="primary" class="button-size" @click="toCalculationRate" :disabled="tableData.length == 0">
                    计算费率
                </el-button>
                <el-button type="success" class="button-size" :disabled="tableData.length == 0" @click="toSaveDraftBox">保存</el-button>
            </el-form-item>
        </el-form>
        <importExcel
            :projectInfoId="projectInfo.id"
            :projectGroupId="groupInfoItem.id"
            :showDia="showDia"
            @closeDia="closeDia"
            @sureInfo="sureInfo"
            @headerName="headerName"
        ></importExcel>
        <addPeople :showAddDia="showAddDia" @closeDia="closeDia" :groupInfoShow="groupInfoShow" @toAddPeople="toAddPeopleHttp"></addPeople>
        <rateDia :showRateDia="showRateDia" :rateInfo="rateInfo" @closeDia="closeDia" @sureSendMoney="sureSendMoney"></rateDia>
    </div>
</template>

<script>
import { Message } from 'element-ui';
import { currentPage } from '@/utils/common';
import importExcel from './importExcel.vue';
import addPeople from './addPeople.vue';
import {
    addPeoples,
    getEnterpriseWallet,
    getAuditPersonList,
    rateCalculation,
    enterpriseDetermineIssue,
    getEnterpriseGroupWorkerList,
    draftBox,
    getDraftBox
} from '@/api/grant/grantV2.js';
import rateDia from './rateDia.vue';
export default {
    data() {
        return {
            tableData: [],
            current: 1,
            size: 10,
            form: {},
            showMoney: undefined,
            groupInfoItem: {},
            groupInfoShow: [],
            showDia: false,
            projectInfo: {},
            showAddDia: false,
            walletList: [],
            sendMoney: 0,
            calculationType: undefined,
            auditPersonList: [],
            showRateDia: false,
            rateInfo: {
                DetermineIssue: {
                    worker: [],
                    wallet: {
                        calculationType: undefined
                    }
                }
            },
            rowIndex: undefined,
            fullscreenLoading: false,
            salaryId: '',
            tableHeaderName:''
        };
    },
    components: {
        importExcel,
        addPeople,
        rateDia
    },
    props: {
        groupInfo: {
            type: Object,
            require: true
        },
        projectName: {
            type: Object,
            require: false
        }
    },
    watch: {
        projectName(val) {
            if (val) {
                this.projectInfo = val;
            }
        },
        groupInfo(val) {
            if (val) {
                this.groupInfoItem = val;
            }
        }
    },
    mounted() {
        if (JSON.parse(sessionStorage.getItem('projectInfo'))) {
            this.groupInfoItem = JSON.parse(sessionStorage.getItem('groupInfo'));
            this.projectInfo = JSON.parse(sessionStorage.getItem('projectInfo'));
        }
        //判断页面是否缓存信息
        if (sessionStorage.getItem('newLists') && JSON.parse(sessionStorage.getItem('initiateInfo'))) {
            // this.tableData = JSON.parse(sessionStorage.getItem('newLists'));
            // this.tableData.forEach((s) => {
            //     this.sendMoney = this.sendMoney + parseFloat(s.money);
            // });
        }
        getEnterpriseWallet().then((res) => {
            res.data.data.forEach((s) => {
                s.walletStr = s.merchantName + '-' + s.companyName + '-' + s.bankName;
            });
            this.walletList = res.data.data;
            if (this.$route.params.salaryId) {
            this.getDraftBoxDetail();
        }
        });
        getAuditPersonList({
            type: 3,
            walletId: ''
        }).then((res) => {
            this.auditPersonList = res.data.data;
            console.log(this.auditPersonList);
            this.form.auditorId = res.data.data[0].id;
        });
   
    },
    methods: {
        toCalculationRate() {
            if (this.tableData.length == 0) {
                this.$message.error('发放人员不能为空！');
                return false;
            }
            if (!this.form.auditorId) {
                this.$message.error('请选择审核人员！');
                return false;
            }
            if (!this.form.wallet) {
                this.$message.error('请选择钱包！');
                return false;
            }

            this.tableData.forEach((s) => {
                if (!s.money) {
                    this.$message.error('本次结算包含0元或者为空！');
                    return false;
                }
                s.payMoney = s.money;
                s.signFlag = undefined;
                s.bankNumber = s.bankCard;
                s.idCardNumber = s.identification;
                s.phone = s.phoneNumber;
                if (s.signingBoolean) {
                    s.signFlag = 1;
                } else {
                    s.signFlag = 0;
                }
                s.workerName = s.employeeName;
            });
            rateCalculation({
                auditorId: this.form.auditorId,
                projectId: this.projectInfo.id,
                groupId: this.groupInfoItem.id,
                wallet: this.form.wallet,
                worker: this.tableData,
                salaryId: this.salaryId,
                headerName:this.tableHeaderName
            }).then((res) => {
                if (res.data.code == 0) {
                    this.showRateDia = true;
                    this.rateInfo = res.data.data;
                }
            });
        },
        indexMethod(index) {
            this.rowIndex = index;
            return index + 1;
        },
        handleEdit(id) {
            this.$confirm(`确定要删除该员工吗？`, '提示', {
                type: 'warning'
            }).then(() => {
                this.tableData.forEach((element, index) => {
                    if (element.workerId == id) {
                        this.tableData.splice(index, 1);
                    }
                });
                //页面缓存表格信息
                sessionStorage.setItem('newLists', JSON.stringify(this.tableData));
                sessionStorage.setItem('initiateInfo', 'true');
            });
        },
        headerName(e){
            this.tableHeaderName = e
        },
        toShowDia() {
            this.showDia = true;
        },
        closeDia() {
            this.showDia = false;
            this.showAddDia = false;
            this.showRateDia = false;
        },
        toNextSepts() {},
        toBeforetSepts() {
            sessionStorage.setItem('activeStatus', 0);
            this.tableData = [];
            this.$emit('beforeSepts');
        },
        sureInfo(e) {
            // console.log('导入的excel', e);
            // let newListStr = sessionStorage.getItem('newList');
            // this.tableData = this.tableData.concat(JSON.parse(newListStr));
            // this.tableData = this.tableData.filter(function (item, index, self) {
            //     return self.findIndex((el) => el.workerId == item.workerId) === index;
            // });
            // //页面缓存表格信息
            // sessionStorage.setItem('newLists', JSON.stringify(this.tableData));
            // sessionStorage.setItem('initiateInfo', 'true');
            this.tableData = e
            this.showDia = false;
        },
        //添加收款人弹窗
        toAddPeople() {
            getEnterpriseGroupWorkerList({
                projectId: this.projectInfo.id,
                groupId: this.groupInfoItem.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.groupInfoShow = res.data.data;
                    this.showAddDia = true;
                }
            });
        },
        //走接口信息添加收款人
        toAddPeopleHttp(e) {
            addPeoples({
                projectId: this.projectInfo.id,
                groupId: this.groupInfoItem.id,
                payeeList: e
            }).then((res) => {
                this.tableData = this.tableData.concat(res.data.data);
                this.tableData = this.tableData.filter(function (item, index, self) {
                    return self.findIndex((el) => el.workerId == item.workerId) === index;
                });
                console.log(this.tableData);
                this.showAddDia = false;
                //页面缓存表格信息
                sessionStorage.setItem('newLists', JSON.stringify(this.tableData));
                sessionStorage.setItem('initiateInfo', 'true');
            });
        },
        //更新结算信息
        toAddNew(money) {
            let sendMoney = 0;
            if (money <= 0) {
                this.tableData[this.rowIndex].money = 1;
                return Message.error('发放金额不能小于1');
            }
            // this.tableData.forEach((s) => {
            //     if (s.money) {
            //         sendMoney = sendMoney + parseFloat(s.money);
            //     }
            // });
            // this.sendMoney = sendMoney;
            //页面缓存表格信息
            sessionStorage.setItem('newLists', JSON.stringify(this.tableData));
            sessionStorage.setItem('initiateInfo', 'true');
        },
        toAddNewReamke() {
            sessionStorage.setItem('newLists', JSON.stringify(this.tableData));
            sessionStorage.setItem('initiateInfo', 'true');
        },
        toChangeWallet(val) {
            // console.log(this.form);
            if (val) {
                this.showMoney = (val.currentMoney - val.frozenMoney).toFixed(2);
            } else {
                this.showMoney = undefined;
            }
            // this.calculationType = val.calculationType;
            this.tableData.forEach((s) => {
                s.rateMoney = parseFloat();
            });
            // this.$forceUpdate();
        },
        //确认发放
        sureSendMoney() {
            enterpriseDetermineIssue({
                id: this.rateInfo.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.showRateDia = false;
                    sessionStorage.setItem('activeStatus', 0);
                    this.tableData = [];
                    this.$emit('nextSepts');
                }
            });
        },
        toChangeInput() {
            this.$forceUpdate();
        },
        async toSaveDraftBox() {
            if (!this.form.wallet) {
                this.$message.error('请选择钱包！');
                return false;
            }
            let res = await draftBox({
                employeeDtoList: this.tableData,
                projectId: this.projectInfo.id,
                groupId: this.groupInfoItem.id,
                walletId: this.form.wallet.id,
                salaryId: this.salaryId,
                headerName:this.tableHeaderName
            });
            if (res.data.code == 0) {
                this.$confirm('保存成功，是否跳转到付款批次页面查看？', '成功', {
                    confirmButtonText: '确定',
                    cancelButtonText: '再次发起',
                    type: 'success'
                })
                    .then((res) => {
                        this.$router.push('/grant/batch?fromPage=1');
                    })
                    .catch(() => {
                        window.location.reload();
                    });
            }
        },
        async getDraftBoxDetail() {
            let res = await getDraftBox(this.$route.params.salaryId);
            if (res.data.code != 0) return false;
            this.projectInfo.name = res.data.data.projectName;
            this.groupInfoItem.name = res.data.data.groupName;
            this.tableData = res.data.data.employeeDtoList;
            this.projectInfo.id = res.data.data.projectId;
            this.groupInfoItem.id = res.data.data.groupId;
            this.walletList.forEach((v) => {
                if (v.id == res.data.data.walletId) {
                    this.form.wallet = v;
                    this.showMoney = (v.currentMoney - v.frozenMoney).toFixed(2);
                }
            });
            this.salaryId = res.data.data.salaryId;
            this.tableHeaderName = res.data.data.headerName
        }
    }
};
</script>

<style scoped>
.project_name {
    font-size: 18px;
    font-weight: 550;
}
.blue_box {
    padding: 8px 16px;
    background-color: #f9fafc;
    border-radius: 4px;
    border-left: 5px solid #7ca0e5;
    margin: 20px 0;
    width: 300px;
}
.project_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.tip_member {
    border: 1px solid #000;
    color: #000;
    margin-left: 10px;
}
.money {
    font-size: 20px;
    font-weight: 550;
}
.button-size {
    width: 180px;
    height: 40px;
    font-size: 18px;
    margin-top: 20px;
    margin-bottom: 40px;
}
</style>