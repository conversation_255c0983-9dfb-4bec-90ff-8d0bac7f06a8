<template>
    <div>
        <el-dialog
            title="文件批量导入"
            :visible.sync="dialogVisible"
            @open="toOpenDia"
            @close="toCloseDia"
            width="40%"
            top="25vh"
            :show-close="false"
        >
            <div v-loading="loading" element-loading-text="导入中...">
                <div class="notic_box">
                    <i class="el-icon-warning notic_size"></i>
                    文件导入使用系统提供的模板导入，请点击右边下载模板文件
                    <el-button style="margin-left: 15px" @click="toDown">文件模板</el-button>
                </div>
                <div class="upload_btn">
                    <el-button type="primary">文件批量导入</el-button>
                    <el-upload
                        class="upload-demo"
                        :action="action"
                        :on-success="handlePreview"
                        multiple
                        :show-file-list="false"
                        :headers="headers"
                        :on-progress="showLoding"
                        :on-error="onErrorUpload"
                    >
                    </el-upload>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import auth from '@/api/auth.js';
import { LOCAL_BASE_URL } from '@/api/config.js';
import errorUpload from './errorUpload.vue';
import {
    errorDownload,
    employeesCertification,
    salaryConfirm,
    uploadTemplate,
    isCertification,
    isCertificationComplete
} from '@/api/grant/grantV2.js';

export default {
    components: {
        errorUpload
    },
    data() {
        return {
            action: '',
            headers: {
                Authorization: 'Bearer ' + auth.curUser().access_token
            },
            dialogVisible: false,
            uploadError: false,
            blobUrl: '',
            workerList: [],
            showNoticeGroup: false,
            noticInfo: '',
            showSubmit: false,
            sureSubmitBtu: true,
            loading: false,
            showButton: undefined,
            numberFor: 0,
            newQueue: true,
            uuid: '',
            firstIn: true,
            indexRefales: 0,
            numberSize: 0,
            inReadlyShowAtten: false,
            headerName: ''
        };
    },
    props: {
        projectInfoId: {
            type: String,
            require: true
        },
        showDia: {
            type: Boolean,
            require: true
        },
        projectGroupId: {
            type: String,
            require: true
        }
    },
    watch: {
        showDia(val) {
            this.dialogVisible = val;
        }
    },
    mounted() {},
    methods: {
        //表格滚动
        autoScroll(init, i) {
            this.$nextTick(() => {
                const t = 20;
                const box = this.$el.querySelector('.el-table__body-wrapper');
                const content = this.$el.querySelector('.el-table__body');
                if (init) box.scrollTop = 0;
                // this.timer = setInterval(() => {
                this.rollStart(box, content, i);
                // }, t);
            });
        },
        rollStart(box, content, i) {
            if (box.scrollTop >= content.scrollHeight - box.offsetHeight) {
                box.scrollTop = 0;
                // clearInterval(this.timer);
            } else {
                if (i > 0) {
                    box.scrollTop = 40 * (i - 1);
                } else {
                    box.scrollTop = 40 * i;
                }
            }
        },
        rollStartLast(box, content) {
            box.scrollTop = content.scrollHeight - box.offsetHeight;
        },
        autoScrollLast() {
            this.$nextTick(() => {
                const t = 20;
                const box = this.$el.querySelector('.el-table__body-wrapper');
                const content = this.$el.querySelector('.el-table__body');
                if (init) box.scrollTop = 0;
                // this.timer = setInterval(() => {
                this.rollStartLast(box, content);
                // }, t);
            });
        },
        toDown() {
            let url =
                process.env.NODE_ENV === 'prod'
                    ? 'https://xytb-prd.oss-cn-hangzhou.aliyuncs.com/laborContracts/20230822/%E5%8F%91%E6%94%BE%E4%BA%BA%E5%91%98%E4%BF%A1%E6%81%AF%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx'
                    : 'http://xytb-test.oss-cn-hangzhou.aliyuncs.com/businessLicenseUrl/20230822/1692674677023_19593f77.xlsx';
            // uploadTemplate().then((res) => {
            window.open(url);
            // });
        },
        mouseover() {
            clearInterval(this.timer);
        },
        mouseout() {
            this.autoScroll(false);
        },
        handlePreview(res) {
            console.log(res.data,'当前支付数组');
        
            this.loading = false;
            if (res.code == 0) {
                // res.data.
                this.$emit('employData', res.data);
            } else {
                this.$message.error(res.msg);
                // this.uploadError = true;
                // this.noticInfo = '导入文件内信息错误，请下载文件，根据右边的提示进行修改后重新导入';
                // errorDownload({
                //     fileName: res.msg
                // }).then((ress) => {
                //     this.blobUrl = ress.data;
                // });
            }
        },
        toOpenDia() {
            this.action = LOCAL_BASE_URL + `platform/salary/importOfflineEmployees`;
        },
        toCloseDia() {
            this.uploadError = false;
            this.workerList = [];
            this.showNoticeGroup = false;
            this.showSubmit = false;
            this.sureSubmitBtu = true;
            this.$emit('closeDia');
        },
        //下载文件流
        downLoad() {
            let excel = new Blob([this.blobUrl]);
            let url = URL.createObjectURL(excel);
            let a = document.createElement('a');
            a.href = url;
            a.download = '发放工资-错误提示.xlsx';
            a.click();
        },
        //数组五个为一组
        groupFive(array, subGroupLength) {
            let index = 0;
            let newArray = [];

            while (index < array.length) {
                newArray.push(array.slice(index, (index += subGroupLength)));
            }
            return newArray;
        },
        toReadyName() {
            const groupedArray = this.groupFive(this.workerList, 5);
            this.$confirm(`确定要实名认证吗？`, '提示', {
                type: 'warning'
            }).then(() => {
                // Promise.all(
                //     groupedArray.map(async (s, index) => {
                //         await employeesCertification(s, this.uuid).then((res) => {
                //             this.firstIn = true;
                //             this.newQueue = false;
                //             this.$forceUpdate();
                //             if (res.data.code == 0) {
                //                 this.workerList.forEach((v, index) => {
                //                     res.data.data.dtos.forEach((t, idx) => {
                //                         if (v.identification == t.identification) {
                //                             v.witness = t.witness;
                //                         }
                //                     });
                //                 });
                //                 this.showSubmit = true;
                //                 this.sureSubmitBtu = false;
                //             }
                //         });
                //     })
                // ).then(() => {
                //     this.getInfoList();
                // });
                employeesCertification(this.workerList, this.uuid).then((res) => {
                    this.firstIn = true;
                    this.newQueue = false;
                    this.$forceUpdate();
                    this.loading = true;
                    setTimeout(() => {
                        this.loading = false;
                    }, 1500);
                    this.timerGet = setInterval(() => {
                        this.getisCertificationComplete();
                    }, 2000);
                });
            });
        },
        async getisCertificationComplete() {
            let res = await isCertificationComplete(this.uuid);
            if (res.data.code == 0) {
                this.workerList.forEach((v, index) => {
                    res.data.data.dtos.forEach((t, idx) => {
                        if (v.identification == t.identification) {
                            v.witness = t.witness;
                        }
                    });
                });
                this.autoScroll(true, res.data.data.size);
                this.inReadlyShowAtten = true;
                if (res.data.data.isComplete) {
                    this.autoScrollLast();
                    clearInterval(this.timerGet);
                    clearInterval(this.timer);
                    this.getInfoList();
                }
            }
        },
        getInfoList() {
            isCertification(this.uuid).then((res) => {
                this.inReadlyShowAtten = false;
                if (!res.data.data.isBoolean) {
                    errorDownload({
                        fileName: res.data.data.fileName
                    }).then((ress) => {
                        this.blobUrl = ress.data;
                        this.newQueue = false;
                        this.$forceUpdate();
                        this.sureSubmitBtu = true;
                        this.noticInfo = '实名认证失败，请下载文件，根据右边的提示进行修改后重新导入';
                        this.uploadError = true;
                    });
                } else {
                    this.showSubmit = true;
                    this.sureSubmitBtu = false;
                }
            });
        },
        toSubmitTable() {
            this.$confirm(`确定要导入吗？`, '提示', {
                type: 'warning'
            }).then(() => {
                salaryConfirm({
                    importEmployeeDtoList: this.workerList,
                    projectGroupId: this.projectGroupId
                }).then((res) => {
                    if (res.data.code == 0) {
                        this.uploadError = false;
                        this.workerList = [];
                        this.showNoticeGroup = false;
                        this.showSubmit = false;
                        this.sureSubmitBtu = true;
                        sessionStorage.setItem('newList', JSON.stringify(res.data.data));
                        this.$emit('sureInfo', res.data.data);
                        this.$emit('headerName', this.headerName);
                    }
                });
            });
        },
        onErrorUpload() {
            this.loading = false;
            this.$message.error('您上传的文件格式错误，请检查后重新上传');
        },
        showLoding() {
            this.loading = true;
        }
    }
};
</script>

<style scoped>
.upload_btn {
    position: relative;
    width: 150px;
    height: 40px;
    margin-top: 30px;
    margin-bottom: 5px;
}
.upload-demo {
    position: absolute;
    top: 0;
    left: 0;

    opacity: 0;
}
::v-deep .el-upload--text {
    width: 130px;
    height: 40px;
}
.notic_size {
    font-size: 30px;
    color: #f5a300;
    margin-right: 10px;
}
.notic_small {
    font-size: 20px;
    color: #f5a300;
    margin-right: 10px;
}
.notic_box {
    display: flex;
    align-items: center;
}
.tip_member {
    color: #ff0000;
}
.notice-group {
    display: flex;
    margin-top: 10px;
    align-items: center;
}
</style>
