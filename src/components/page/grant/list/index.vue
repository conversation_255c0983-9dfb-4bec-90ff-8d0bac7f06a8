<template>
    <div>
        <el-card shadow="never">
            <div slot="header" class="clearfix">
                <span>发放记录</span>
            </div>
            <el-form :inline="true" ref="form" :model="form" class="demo-form-inline">
                <el-row type="flex" align="bottom">
                    <el-col :span="18">
                        <el-row>
                            <el-form-item label="机构选择：">
                                <el-select v-model="form.enterpriseId" placeholder="请选择" clearable filterable @change="toChangeValue">
                                    <el-option
                                        :label="i.enterpriseName"
                                        :value="i.enterpriseId"
                                        v-for="i in listEnterprise"
                                        :key="i.enterpriseId"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="钱包类型：">
                                <el-select v-model="form.walletType" placeholder="请选择" clearable filterable @change="toChangeValueType">
                                    <el-option :label="i.name" :value="i.id" v-for="i in walletBoxStatus" :key="i.id"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="钱包选择：">
                                <el-select v-model="form.walletId" placeholder="请选择" clearable filterable>
                                    <el-option :label="i.walletStr" :value="i.walletId" v-for="i in walletBox" :key="i.walletId"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="交易状态：">
                                <el-select v-model="form.payStatus" placeholder="请选择" clearable>
                                    <el-option :label="i.name" :value="i.id" v-for="i in statusBox" :key="i.id"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="发放渠道：">
                                <el-select v-model="form.dataType" placeholder="请选择" clearable>
                                    <el-option :label="i.name" :value="i.value" v-for="i in dataType" :key="i.value"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="模糊搜索：">
                                <el-input v-model="form.text" placeholder="请输入关键字" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="金额范围：">
                                <el-input v-model="form.minMoney" type="number" placeholder="最小金额" style="width: 150px" clearable></el-input>
                                -
                                <el-input v-model="form.maxMoney" type="number" placeholder="最大金额" style="width: 150px" clearable></el-input>
                            </el-form-item>
                        </el-row>
                        <el-row>
                            <el-form-item style="text-align: right;">
                                <el-select v-model="form.type" placeholder="请选择" clearable>
                                    <el-option label="创建时间" value="1"></el-option>
                                    <el-option label="交割时间" value="2"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item>
                                <el-date-picker
                                    v-model="form.date"
                                    type="daterange"
                                    range-separator="~"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    value-format="yyyy-MM-dd"
                                    clearable
                                    @change="toChangeTime"
                                >
                                </el-date-picker>
                            </el-form-item>
                        </el-row>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item>
                            <el-button size="medium" @click="excelList">导 出 </el-button>
                            <el-button type="primary" icon="el-icon-search" size="medium" @click="getDataSearch">查 询 </el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <!-- <div class="money_box">
                <div>
                    已完成&nbsp;&nbsp;<span style="color: #318000" class="money">{{ moneyIntPart(readyMoney) }}<span style="font-size: 16px;">{{ moneyDeciPart(readyMoney) }}</span></span>
                </div>
                <div>
                    有退票&nbsp;&nbsp;<span style="color: #ffa500" class="money">{{moneyIntPart(backMoney) }}<span style="font-size: 16px;">{{ moneyDeciPart(backMoney) }}</span></span>
                </div>
                <div>
                    已取消&nbsp;&nbsp;<span style="color: #ff0609" class="money">{{ moneyIntPart(removeMoney) }}<span style="font-size: 16px;">{{ moneyDeciPart(removeMoney) }}</span></span>
                </div>
                <div>
                    待支付&nbsp;&nbsp;<span style="color: #2c3034" class="money">{{ moneyIntPart(auditMoney) }}<span style="font-size: 16px;">{{ moneyDeciPart(auditMoney) }}</span></span>
                </div>
            </div> -->
            <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                <el-table-column prop="companyName" label="商户钱包" width="300" fixed>
                    <template slot-scope="scope">
                        <div style="padding-right: 20px;">{{ scope.row.walletStr }}</div>
                        <div style="display:flex; align-items: center; ">
                            {{ scope.row.bankName }}
                            <img width="20px" src="@/assets/img/zsyh.png" v-show="scope.row.bankName.indexOf('招商') > -1 || scope.row.bankName.indexOf('招行') > -1" style="margin-left: 3px"/>
                            <img width="20px" src="@/assets/img/alipay.png" v-show="scope.row.bankName.indexOf('支付宝') > -1" style="margin-left: 3px"/>
                            <img width="20px" src="@/assets/img/pabank.png" v-show="scope.row.bankName.indexOf('平安') > -1" style="margin-left: 3px"/>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="enterpriseName" label="项目" min-width="250">
                    <template slot-scope="scope">
                        <div style="font-weight: bold;">{{ scope.row.projectName }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="收款人" min-width="300">
                    <template slot-scope="scope">
                        <div>{{ scope.row.workerName }}</div>
                        <div><span class="el-icon-postcard icon_margin_right"></span>{{ scope.row.idCardNumber }}</div>
                        <div><span class="el-icon-phone-outline icon_margin_right"></span>{{ scope.row.phone }}</div>
                        <div><span class="el-icon-bank-card icon_margin_right"></span>{{ scope.row.bankNumber }}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="money" label="发放金额" min-width="150">
                    <template slot-scope="scope">
                        <span :style="{ color: scope.row.money > 0 ? '#ff6600' : '' }" class="money">{{ scope.row.moneyStr.toLocaleString('zh-CN') }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="状态" prop="payStatusStr" min-width="120">
                    <template slot-scope="scope">
                        <span :style="{color: statusColor[scope.row.payStatus]}">{{ scope.row.payStatusStr }}</span>
                    </template>
                </el-table-column>
                <el-table-column min-width="200" label="备注" prop="remark" show-overflow-tooltip></el-table-column>
                <el-table-column width="200" label="创建时间/交割时间" prop="createTime"></el-table-column>
                <el-table-column label="渠道结果" width="150" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <div>{{ dataType.find((v) => v.value == scope.row.dataType).name }}</div>
                        <div>{{ scope.row.bankRemark ? scope.row.bankRemark : '---' }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="付款凭证" width="100">
                    <template slot-scope="scope">
                        <template v-if="scope.row.huifuReceipts && scope.row.huifuReceipts.length > 0">
                            <el-button type="text" @click="showReceiptDialog(scope.row.huifuReceipts)" style="font-size: 14px;">查看凭证</el-button>
                        </template>
                        <template v-else>
                            <el-button type="text" v-if="scope.row.picFileUrl" @click="toLookDetail(scope.row.picFileUrl)" style="font-size: 14px;">查看凭证</el-button><br/>
                            <el-button type="text" v-if="scope.row.picFileUrl" @click="toDownload(scope.row)" style="font-size: 14px;">下载凭证</el-button>
                            <span v-if="!scope.row.picFileUrl">---</span>
                        </template>
                    </template>
                </el-table-column>
                <el-table-column label="流水单号" prop="orderNo" width="200" show-overflow-tooltip></el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page="tableData.current"
                    layout="prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
        </el-card>
        
        <!-- 汇付凭证对话框 -->
        <el-dialog
            title="汇付凭证"
            :visible.sync="receiptDialogVisible"
            width="400px">
            <div v-for="(receipt, index) in currentReceipts" :key="index" style="margin: 8px 0; display: flex; align-items: center;">
                <span style="width: 220px;">{{ getReceiptTypeName(receipt.type) }}</span>
                <el-button type="text" size="mini" style="margin-right: 10px;" @click="toLookDetail(receipt.ossUrl)">查看</el-button>
                <el-button type="text" size="mini" @click="downloadHuifuReceipt(receipt)">下载</el-button>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="receiptDialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { statisrBillInfo, exportBillInfo, listEnterpriseList } from '@/api/grant/grant.js';
import { salaryBillInfo} from '@/api/grant/grantV2.js';
import { walletListV2 } from '@/api/account/account.js';
import { getFileNameUUID } from '@/utils/oss.js';
import { OSS_URL } from '@/api/config.js';
export default {
    data() {
        return {
            statusBox: [
                {
                    name: '待支付',
                    id: 1
                },
                {
                    name: '支付中',
                    id: 2
                },
                {
                    name: '已完成',
                    id: 3
                },
                {
                    name: '失败',
                    id: 4
                },
                {
                    name: '已完成(有退票)',
                    id: 5
                }
            ],
            statusColor:{
                1: "#2c3034",
                2: "#2c3034",
                3: "#318000",
                4: "#ff0609",
                5: "#ffa500",
                9: "#ff0609"
            },
            form: {
                enterpriseId: '',
                walletId: undefined,
                payStatus: '',
                text: '',
                minMoney: '',
                maxMoney: '',
                type: '1',
                startTime: '',
                endTime: '',
                walletType: ''
            },
            tableData: {},
            current: 1,
            size: 10,
            listEnterprise: [],
            walletBox: [],
            readyMoney: 0, //已完成
            backMoney: 0, //已退票
            removeMoney: 0, //已取消
            auditMoney: 0, //待审核
            dataType: [
                {
                    value: 0,
                    name: '平台发放'
                },
                {
                    value: 1,
                    name: 'API发放'
                },
                {
                    value: 3,
                    name: '线下发放'
                }
            ],
            walletBoxStatus: [
                {
                    name: '招商银行',
                    id: 1
                },
                {
                    name: '支付宝',
                    id: 2
                },
                {
                    name: '微信',
                    id: 3
                },
                {
                    name: '平安银行',
                    id: 4
                }
            ],
            receiptDialogVisible: false,
            currentReceipts: []
        };
    },
    created() {
        listEnterpriseList().then((res) => {
            if (res.data.code === 0) {
                this.listEnterprise = res.data.data;
            }
        });
        walletListV2({ enterpriseId: this.form.enterpriseId, type: this.form.walletType }).then((res) => {
            if (res.data.code === 0) {
                this.walletBox = res.data.data.map((s) => {
                    s.walletStr = s.merchantsName + '-' + s.companyName + '-' + s.bankName;
                    return s;
                });
            }
        });
        this.getData();
    },
    methods: {
        toChangeValue(val) {
            this.form.walletId = undefined;
            walletListV2({ enterpriseId: val, type: this.form.walletType }).then((res) => {
                if (res.data.code === 0) {
                    this.walletBox = res.data.data.map((s) => {
                        s.walletStr = s.merchantsName + '-' + s.companyName + '-' + s.bankName;
                        return s;
                    });
                }
            });
        },
        toChangeValueType(val) {
            this.form.walletId = undefined;
            walletListV2({ type: val, enterpriseId: this.form.enterpriseId }).then((res) => {
                if (res.data.code === 0) {
                    this.walletBox = res.data.data.map((s) => {
                        s.walletStr = s.merchantsName + '-' + s.companyName + '-' + s.bankName;
                        return s;
                    });
                }
            });
        },
        toChangeTime(date) {
            if (date) {
                this.form.startTime = date[0] + ' ' + '00:00:00';
                this.form.endTime = date[1] + ' ' + '23:59:59';
            } else {
                this.form.startTime = '';
                this.form.endTime = '';
            }
        },
        handleSizeChange(size) {
            this.size = size;
            this.current = 1;
            this.getData();
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.current = current;
            this.getData(current);
        },
        toLookDetail(url) {
            window.open(OSS_URL + '/' + url);
        },
        toDownload(row){
            // rename and download pdf file
            var pdfUrl = OSS_URL + '/' + row.picFileUrl;
            // 流水号_姓名_金额_时间.pdf,
            var pdfName =
                (row.orderNo || "") + '_' +
                (row.workerName || "") + '_' +
                (row.money || "") + '_' +
                (row.createTime.replace(/[:]/g, '')) + '.pdf';
            if (window.navigator.msSaveBlob) {
                var xhr = new XMLHttpRequest();
                xhr.open('GET', pdfUrl, true);
                xhr.responseType = 'arraybuffer';
                xhr.onload = function() {
                    if (this.status === 200) {
                        var blob = new Blob([this.response], { type: 'application/pdf' });
                        window.navigator.msSaveBlob(blob, pdfName);
                    }
                };
                xhr.send();
            } else {
                fetch(pdfUrl)
                    .then(response => response.blob())
                    .then(blob => {
                        var url = window.URL.createObjectURL(blob);
                        var a = document.createElement('a');
                        a.href = url;
                        a.download = pdfName;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                    });
            }
        },
        getData() {
            // statisrBillInfo({
            //     ...this.form
            // }).then((res) => {
            //     if (res.data.code === 0) {
            //         res.data.data.forEach((s) => {
            //             if (s.auditStatus == 3) {
            //                 this.readyMoney = parseFloat(s.sumMoney);
            //             } else if (s.auditStatus == 5) {
            //                 this.backMoney = parseFloat(s.sumMoney);
            //             } else if (s.auditStatus == 4) {
            //                 this.removeMoney = parseFloat(s.sumMoney);
            //             } else if (s.auditStatus == 1) {
            //                 this.auditMoney = parseFloat(s.sumMoney);
            //             }
            //         });
            //     }
            // });
            salaryBillInfo({
                ...this.form,
                current: this.current,
                size: this.size
            }).then((res) => {
                if (res.data.code === 0) {
                    res.data.data.records.forEach((v) => {
                        v.moneyStr = '';
                        if (v.money > 0) {
                            v.moneyStr = '+￥' + v.money.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                        } else {
                            v.moneyStr = '-￥' + Math.abs(v.payMoney).toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                        }
                        v.companyName = v.companyName.replace('有限公司', '');
                        v.walletStr = v.merchantsName + '-' + v.companyName;
                    });
                    this.tableData = res.data.data;
                }
            });
        },
        getDataSearch() {
            this.current = 1;
            this.getData();
        },
        async excelList() {
            const s = await this.$exportDia();
            exportBillInfo({
                ...this.form,
                isFile: s.isFile
            }).then((res) => {
                // let excel = new Blob([res.data]);
                // let url = URL.createObjectURL(excel);
                // let a = document.createElement('a');
                // let suf = '';
                // if (s.isFile == 0) {
                //     suf = '.xlsx';
                // } else {
                //     suf = '.zip';
                // }
                // a.href = url;
                // a.download = '导出账单' + getFileNameUUID() + suf;
                // a.click();
                this.$confirm('已加入下载队列，是否前往下载中心查看?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'success'
                }).then(() => {
                    this.$router.push('/system/downloadCenter');
                });
            });
        },
        moneyIntPart(money) {
            let res = money.toLocaleString('zh-CN', { style: 'currency', currency: 'CNY' });
            let dotIndex = res.indexOf('.')
            res = res.slice(0, dotIndex + 1);
            return res;
        },
        moneyDeciPart(money) {
            let res = money.toLocaleString('zh-CN', { style: 'currency', currency: 'CNY' });
            let dotIndex = res.indexOf('.')
            res = res.slice(dotIndex + 1);
            return res;
        },
        showReceiptDialog(receipts) {
            this.currentReceipts = receipts;
            this.receiptDialogVisible = true;
        },
        getReceiptTypeName(type) {
            const typeMap = {
                0: '发放扣款（企业账户-税地账户）',
                1: '划拨（税地账户-个人账户）',
                3: '失败退款（税地账户-企业账户）',
                4: '提现（个人账户-银行卡）'
            };
            return typeMap[type] || `未知类型(${type})`;
        },
        downloadHuifuReceipt(receipt) {
            const pdfUrl = OSS_URL + '/' + receipt.ossUrl;
            const receiptTypeName = this.getReceiptTypeName(receipt.type);
            const fileName = `${receiptTypeName}_${new Date().toISOString().slice(0, 10)}.pdf`;
            
            fetch(pdfUrl)
                .then(response => response.blob())
                .then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = fileName;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                })
                .catch(err => {
                    this.$message.error('下载失败，请重试');
                    console.error('下载失败：', err);
                });
        }
    }
};
</script>

<style scoped>
.money_box {
    display: flex;
    justify-content: space-around;
    font-size: 14px;
    color: #989898;
    margin-bottom: 30px;
    margin-top: 10px;
}
.money_box span {
    font-weight: 300;
    font-size: 18px;
}

.icon_margin_right {
    margin-right: 5px
}
</style>
