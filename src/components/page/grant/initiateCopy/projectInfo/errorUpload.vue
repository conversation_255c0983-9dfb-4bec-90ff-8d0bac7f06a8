<template>
    <div class="red_box">
        <i class="el-icon-error notic_size"></i>
        {{noticInfo}}
        <el-button type="text" style="margin-left:10px" @click="toDownLoadFile">下载文件</el-button>
    </div>
</template>

<script>
export default {
    methods:{
        toDownLoadFile(){
            this.$emit('downLoad')
        }
    },
    props:{
        noticInfo:{
            type:String,
            require:false
        }
    }
};
</script>

<style  scoped>
.red_box {
    background-color: #fef0f0;
    color: #f56c6c;
    height: 44px;
    display: flex;
    align-items: center;
    border-radius: 5px;
    padding: 0 20px;
}
.notic_size {
    font-size: 30px;
    color: #fa5555;
    margin-right: 10px;
}
</style>

