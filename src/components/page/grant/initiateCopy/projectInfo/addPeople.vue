<template>
    <div>
        <el-dialog title="增加收款人" :visible.sync="dialogTableVisible" @open="openAddDia" @close="closeDia">
            <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
            <div style="margin: 15px 0"></div>
            <div v-for="i in groupInfoOption" :key="i.groupId" class="group-list">
                <span>{{ i.groupName }}</span>
                <el-divider style="margin: 5px"></el-divider>
                <el-checkbox-group v-model="checkedCities" @change="handleCheckedCitiesChange">
                    <div v-for="s in i.workerList" :key="s.workerId" class="group-item">
                        <el-checkbox :label="s"
                            ><div class="info_box">
                                <span>{{ s.name }}</span
                                ><span>{{ s.idCardNumber }}</span
                                ><span>{{ s.phone }}</span
                                ><span>{{ s.signingBoolean ? '已签约' : '未签约' }}</span>
                            </div></el-checkbox
                        >
                    </div>
                </el-checkbox-group>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeDia">取 消</el-button>
                <el-button type="primary" @click="toAddInfoPeople">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    data() {
        return {
            checkAll: false,
            checkedCities: [],
            cities: [],
            isIndeterminate: false,
            dialogTableVisible: false,
            groupInfoOption: []
        };
    },
    props: {
        showAddDia: {
            type: Boolean,
            require: true
        },
        groupInfoShow: {
            type: Array,
            require: true
        }
    },
    watch: {
        showAddDia(val) {
            this.dialogTableVisible = val;
        },
        groupInfoShow(val) {
            this.groupInfoOption = val;
            this.groupInfoOption.forEach((s) => {
                if (s.workerList) {
                    s.workerList.forEach((v) => {
                        v.groupId = s.groupId;
                    });
                }
            });
            console.log(this.groupInfoOption);
        }
    },
    methods: {
        handleCheckAllChange(val) {
            let cityOptions = [];
            this.groupInfoOption.forEach((element) => {
                if (element.workerList) {
                    element.workerList.forEach((s) => {
                        cityOptions.push(s);
                    });
                }
            });
            this.checkedCities = val ? cityOptions : [];
            this.isIndeterminate = false;
        },
        handleCheckedCitiesChange(value) {
            let i = 0;
            this.groupInfoOption.forEach((element) => {
                if (element.workerList) {
                    element.workerList.forEach((s) => {
                        i++;
                    });
                }
            });
            if (this.checkedCities.length == i) {
                this.checkAll = true;
                console.log(this.checkAll);
                this.$forceUpdate();
            } else {
                this.checkAll = false;
                console.log(this.checkAll);
            }
        },
        openAddDia() {},
        toAddInfoPeople() {
            this.$emit('toAddPeople', this.checkedCities);
        },
        closeDia() {
            this.$emit('closeDia');
        }
    }
};
</script>

<style scoped>
::v-deep .el-divider--horizontal {
    margin: 8px 0 15px 0;
}
.group-item {
    margin-bottom: 8px;
}
.group-list {
    margin-bottom: 12px;
}
.info_box span {
    margin: 0 5px;
}
</style>