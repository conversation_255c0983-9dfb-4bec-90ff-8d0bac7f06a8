<template>
    <div>
        <el-dialog title="费率回单" :visible.sync="dialogVisible" width="70%" @close="closeDia">
            <el-table :data="rateInfo.DetermineIssue.worker" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                <el-table-column label="员工姓名" min-width="150" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <div>{{ scope.row.workerName }}<span class="tip_member">人证合一</span></div>
                        {{ scope.row.idCardNumber }}
                    </template>
                </el-table-column>
                <el-table-column label="银行卡" width="150">
                    <template slot-scope="scope">
                        <div>{{ scope.row.bankNumber }}</div>
                        {{ scope.row.phone }}
                    </template>
                </el-table-column>
                <el-table-column prop="mouthMoney" label="本月已结算" width="120" show-overflow-tooltip></el-table-column>
                <el-table-column label="本次结算" prop="payMoney"></el-table-column>
                <el-table-column prop="billExtraMoney" label="手续费" show-overflow-tooltip></el-table-column>
                <el-table-column label="模式" show-overflow-tooltip>
                    <span v-if="rateInfo.DetermineIssue.wallet.calculationType == 1">内扣</span>
                    <span v-if="rateInfo.DetermineIssue.wallet.calculationType == 2">外扣</span>
                </el-table-column>
                <el-table-column label="是否签约" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <div :style="{ color: scope.row.signingBoolean ? '' : '#ff0000' }">
                            {{ scope.row.signingBoolean ? '已签约' : '未签约' }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="工作组" show-overflow-tooltip> 已在组内 </el-table-column>
                <el-table-column label="付款备注" min-width="200" show-overflow-tooltip prop="remark"> </el-table-column>
            </el-table>
            <div class="project_top">
                <div class="blue_box">发放金额</div>
            </div>
            <el-form ref="form" label-width="120px">
                <el-form-item label="选择审核人员"> 管理员 </el-form-item>
                <el-form-item label="选择钱包">
                    {{ rateInfo.DetermineIssue.wallet.companyName }}
                </el-form-item>
                <el-form-item label="发放人数">
                    <span>{{ rateInfo.DetermineIssue.issueNumber }}</span>
                </el-form-item>
                <el-form-item label="发放金额">
                    <span class="money">¥ {{ rateInfo.DetermineIssue.payMoney }}</span>
                </el-form-item>
                <el-form-item label="手续费">
                    <span class="money">¥ {{ rateInfo.DetermineIssue.billExtraMoney }} </span>
                    <span v-if="rateInfo.DetermineIssue.wallet.calculationType == 2">手续费模式：外扣手续费在结算金额外扣除，发放总金额=发放金额+发放金额X费率</span>
                    <span v-if="rateInfo.DetermineIssue.wallet.calculationType == 1">手续费模式：内扣手续费在结算金额内扣除，发放总金额=发放金额+手续费</span>
                </el-form-item>
                <el-form-item label="发放总金额">
                    <span class="money">¥ {{ rateInfo.DetermineIssue.billTotalMoney }} </span>
                </el-form-item>
                <el-form-item label="钱包余额">
                    <span class="money">¥ {{ showMoney }} </span>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeDia">取 消</el-button>
                <el-button type="primary" @click="toSureRate">确认发放</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    data() {
        return {
            dialogVisible: false,
            showMoney:undefined
        };
    },
    methods: {
        closeDia() {
            this.$emit('closeDia');
        },
        toSureRate() {
            this.$confirm('是否确认发放', '发放确认', {
                type: 'warning'
            }).then(() => {
                this.$emit('sureSendMoney');
            });
        }
    },
    props: {
        showRateDia: {
            type: Boolean,
            require: true
        },
        rateInfo: {
            type: Object,
            require: true
        }
    },
    watch: {
        showRateDia(val) {
            this.dialogVisible = val;
        },
        rateInfo(val){
            this.showMoney = (val.DetermineIssue.wallet.currentMoney - val.DetermineIssue.wallet.frozenMoney).toFixed(2)
        }


    },
    computed: {
        // showMoney() {
        // }
    }
};
</script>

<style scoped>
.project_name {
    font-size: 18px;
    font-weight: 550;
}
.blue_box {
    padding: 8px 16px;
    background-color: #f9fafc;
    border-radius: 4px;
    border-left: 5px solid #7ca0e5;
    margin: 20px 0;
    width: 300px;
}
.project_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.tip_member {
    border: 1px solid #000;
    color: #000;
    margin-left: 10px;
}
.money {
    font-size: 20px;
    font-weight: 550;
}
.button-size {
    width: 180px;
    height: 40px;
    font-size: 18px;
    margin-top: 20px;
    margin-bottom: 40px;
}
</style>