<template>
    <div>
        <el-dialog title="文件批量导入" :visible.sync="dialogVisible" @open="toOpenDia" @close="toCloseDia" width="80%">
            <div v-loading="loading" element-loading-text="导入中...">
                <div class="notic_box">
                    <i class="el-icon-warning notic_size"></i>
                    文件导入使用系统提供的模板导入，请点击右边下载模板文件
                    <el-button type="primary" style="margin-left: 15px" @click="toDown">文件模板</el-button>
                </div>
                <div class="upload_btn">
                    <el-button>文件批量导入</el-button>
                    <el-upload
                        class="upload-demo"
                        :action="action"
                        :on-success="handlePreview"
                        multiple
                        :show-file-list="false"
                        :headers="headers"
                        :on-progress="showLoding"
                        :on-error="onErrorUpload"
                    >
                    </el-upload>
                </div>
                <el-table :data="workerList" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell" v-if="workerList.length > 0">
                    <el-table-column type="index" width="50"></el-table-column>
                    <el-table-column label="员工姓名" width="120" prop="employeeName">
                        <template slot-scope="scope">
                            <div>
                                {{ scope.row.employeeName
                                }}<span class="tip_member" v-if="!scope.row.employeeNameBoolean">（用户已实名，信息不一致）</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="account" label="身份证号码" width="150">
                        <template slot-scope="scope">
                            <div>
                                {{ scope.row.identification
                                }}<span class="tip_member" v-if="!scope.row.identificationBoolean">（用户已实名，信息不一致）</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="银行卡/支付宝" min-width="150" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <div>
                                {{ scope.row.bankCard }}<span class="tip_member" v-if="!scope.row.bankCardBoolean">（用户已实名，信息不一致）</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="手机号码" min-width="150" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <div>
                                {{ scope.row.phoneNumber
                                }}<span class="tip_member" v-if="!scope.row.phoneNumberBoolean">（用户已实名，信息不一致）</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="人证合一" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <div>{{ scope.row.witnessBoolean ? '人证合一' : '未验证' }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="付款备注" prop="remark" show-overflow-tooltip> </el-table-column>
                    <el-table-column label="是否签约" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <div :style="{ color: scope.row.signingBoolean ? '' : '#ff0000' }">
                                {{ scope.row.signingBoolean ? '已签约' : '未签约' }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="工作组" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <div :style="{ color: scope.row.workingGroupBoolean ? '' : '#ff0000' }">
                                {{ scope.row.workingGroupBoolean ? '已在组内' : '未在组内' }}
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
                <errorUpload @downLoad="downLoad" v-if="uploadError" :noticInfo="noticInfo"></errorUpload>
                <div class="notice-group" v-if="showNoticeGroup">
                    <div style="flex: 1"></div>
                    <i class="el-icon-warning notic_small"></i>
                    确定后人员将自动加入该项目小组内
                </div>
                <span slot="footer" class="dialog-footer" v-if="workerList.length > 0 && !showButton">
                    <el-button @click="toCloseDia">取 消</el-button>
                    <el-button type="primary" :disabled="certification" @click="toReadyName" v-if="!showSubmit">实名认证</el-button>
                    <el-button type="primary" :disabled="sureSubmitBtu" @click="toSubmitTable" v-if="showSubmit">确定导入</el-button>
                </span>
                <span slot="footer" class="dialog-footer" v-if="workerList.length > 0 && showButton">
                    <el-button @click="toCloseDia">取 消</el-button>
                    <el-button type="primary" @click="toSubmitTable">确定导入</el-button>
                </span>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import auth from '@/api/auth.js';
import { LOCAL_BASE_URL } from '@/api/config.js';
import errorUpload from './errorUpload.vue';
import { errorDownload, employeesCertification, salaryConfirm,uploadTemplate } from '@/api/grant/grant.js';

export default {
    components: {
        errorUpload
    },
    data() {
        return {
            action: '',
            headers: {
                Authorization: 'Bearer ' + auth.curUser().access_token
            },
            dialogVisible: false,
            uploadError: false,
            blobUrl: '',
            workerList: [],
            showNoticeGroup: false,
            noticInfo: '',
            showSubmit: false,
            sureSubmitBtu: true,
            loading: false,
            showButton: undefined
        };
    },
    props: {
        projectInfoId: {
            type: String,
            require: true
        },
        showDia: {
            type: Boolean,
            require: true
        },
        projectGroupId: {
            type: String,
            require: true
        }
    },
    watch: {
        showDia(val) {
            this.dialogVisible = val;
        }
    },
    mounted() {},
    methods: {
        toDown() {
            uploadTemplate().then((res) => {
                window.open(res.data.data.url);
            });
        },
        handlePreview(res) {
            console.log(res);
            this.loading = false;
            if (res.code == 0) {
                this.workerList = res.data.dtos;
                this.showButton = res.data.isPass;
                if (res.data.certification) {
                    this.certification = false;
                } else {
                    this.certification = true;
                }
                this.uploadError = false;
                for (const s of this.workerList) {
                    if (!s.workingGroupBoolean) {
                        this.showNoticeGroup = true;
                        break;
                    }
                }
            } else {
                this.uploadError = true;
                this.noticInfo = '导入文件内信息错误，请下载文件，根据右边的提示进行修改后重新导入';
                errorDownload({
                    fileName: res.msg
                }).then((ress) => {
                    this.blobUrl = ress.data;
                });
            }
        },
        toOpenDia() {
            this.action = LOCAL_BASE_URL + `platform/salary/importEmployees?projectGroupId=${this.projectGroupId}`;
        },
        toCloseDia() {
            this.uploadError = false;
            this.workerList = [];
            this.showNoticeGroup = false;
            this.showSubmit = false;
            this.sureSubmitBtu = true;
            this.$emit('closeDia');
        },
        //下载文件流
        downLoad() {
            let excel = new Blob([this.blobUrl]);
            let url = URL.createObjectURL(excel);
            let a = document.createElement('a');
            a.href = url;
            a.download = '发放工资-错误提示.xlsx';
            a.click();
        },
        toReadyName() {
            this.$confirm(`确定要实名认证吗？`, '提示', {
                type: 'warning'
            }).then(() => {
                employeesCertification(this.workerList).then((res) => {
                    this.showSubmit = true;
                    if (res.data.code == 0) {
                        this.sureSubmitBtu = false;
                        this.workerList = res.data.data;
                    } else {
                        this.sureSubmitBtu = true;
                        this.noticInfo = '实名认证失败，请下载文件，根据右边的提示进行修改后重新导入';
                        this.uploadError = true;
                        errorDownload({
                            fileName: res.data.msg
                        }).then((ress) => {
                            this.blobUrl = ress.data;
                        });
                    }
                });
            });
        },
        toSubmitTable() {
            this.$confirm(`确定要导入吗？`, '提示', {
                type: 'warning'
            }).then(() => {
                salaryConfirm({
                    importEmployeeDtoList: this.workerList,
                    projectGroupId: this.projectGroupId
                }).then((res) => {
                    if (res.data.code == 0) {
                        this.uploadError = false;
                        this.workerList = [];
                        this.showNoticeGroup = false;
                        this.showSubmit = false;
                        this.sureSubmitBtu = true;
                        sessionStorage.setItem('newList', JSON.stringify(res.data.data));
                        this.$emit('sureInfo');
                    }
                });
            });
        },
        onErrorUpload() {
            this.loading = false;
            this.$message.error('上传出错啦~');
        },
        showLoding() {
            this.loading = true;
        }
    }
};
</script>

<style scoped>
.upload_btn {
    position: relative;
    width: 150px;
    height: 40px;
    margin-top: 30px;
    margin-bottom: 5px;
}
.upload-demo {
    position: absolute;
    top: 0;
    left: 0;

    opacity: 0;
}
::v-deep .el-upload--text {
    width: 130px;
    height: 40px;
}
.notic_size {
    font-size: 30px;
    color: #f5a300;
    margin-right: 10px;
}
.notic_small {
    font-size: 20px;
    color: #f5a300;
    margin-right: 10px;
}
.notic_box {
    display: flex;
    align-items: center;
}
.tip_member {
    color: #ff0000;
}
.notice-group {
    display: flex;
    margin-top: 10px;
    align-items: center;
}
</style>