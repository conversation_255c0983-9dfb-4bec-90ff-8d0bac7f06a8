<template>
    <div>
        <div class="project_name">项目及小组：{{ projectInfo.name }} -{{ groupInfoItem.name }}</div>
        <div class="project_top">
            <div class="blue_box">发放人员</div>
            <div v-if="loadingOut && firstTable">
                <el-button style="font-size: 16px" @click="toShowDia" v-has="'salary_importEmployees'">文件批量导入</el-button>
                <!-- <el-button type="primary" style="font-size: 16px" @click="toAddPeople" v-has="'salary_addPayee'">增加收款人</el-button> -->
            </div>
            <div v-if="loadingOut && !firstTable">
                <el-button style="font-size: 16px" type="danger" @click="toExcelError">导出认证失败名单</el-button>
            </div>
        </div>
        <el-alert title="注意：发放工资会根据选择的钱包，判断是银行卡还是支付宝进行汇款操作。" type="warning" show-icon :closable="false" style="margin-bottom: 20px;"> </el-alert>
        <div v-if="loadingOut && firstTable">
            <el-table
                :data="tableData"
                stripe
                style="width: 100%"
                border
                :key="calculationType"
                max-height="663"
                :row-class-name="rouClassNameFn"
            >
                <el-table-column type="index" :index="indexMethod"> </el-table-column>
                <el-table-column label="员工姓名" width="180" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <div>{{ scope.row.employeeName }}<span class="tip_member" v-if="scope.row.witnessBoolean">人证合一</span></div>
                        {{ scope.row.identification }}
                    </template>
                </el-table-column>
                <el-table-column label="银行卡/支付宝" width="180">
                    <template slot-scope="scope">
                        <div>{{ scope.row.bankCard }}</div>
                        {{ scope.row.phoneNumber }}
                    </template>
                </el-table-column>
                <el-table-column prop="mouthMoney" label="本月已结算" width="120" show-overflow-tooltip v-if="salaryId"></el-table-column>
                <el-table-column label="本次结算">
                    <template slot-scope="scope">
                        <el-input v-model="scope.row.money" type="number" min="1" @blur="toAddNew(scope.row.money)"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="是否签约" show-overflow-tooltip v-if="salaryId">
                    <template slot-scope="scope">
                        <div :style="{ color: scope.row.signingBoolean ? '' : '#ff0000' }">
                            {{ scope.row.signingBoolean ? '已签约' : '未签约' }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="工作组" show-overflow-tooltip v-if="salaryId">
                    <template slot-scope="scope">
                        <div :style="{ color: scope.row.workingGroupBoolean ? '' : '#ff0000' }">
                            {{ scope.row.workingGroupBoolean ? '已在组内' : '未在组内' }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="付款备注" min-width="200" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <el-input v-model="scope.row.remark" @blur="toAddNewReamke()"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right">
                    <template slot-scope="scope">
                        <el-button @click="handleEdit(scope.row.index)" type="text" style="color: #f56c6c">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div v-if="loadingOut && !firstTable">
            <el-tabs type="border-card">
                <el-tab-pane :label="`认证成功（${resTableData.successDetails.length}）`">
                    <el-table :data="resTableData.successDetails" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell" :key="calculationType" max-height="663">
                        <el-table-column type="index" :index="indexMethod"> </el-table-column>
                        <el-table-column label="员工姓名" width="180" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <div>{{ scope.row.workerName }}<span class="tip_member">人证合一</span></div>
                                {{ scope.row.idCardNumber }}
                            </template>
                        </el-table-column>
                        <el-table-column label="银行卡/支付宝" width="180">
                            <template slot-scope="scope">
                                <div>{{ scope.row.bankNumber }}</div>
                                {{ scope.row.phone }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="mouthMoney" label="本月已结算" width="120" show-overflow-tooltip></el-table-column>
                        <el-table-column label="本次结算">
                            <template slot-scope="scope">
                                <el-input v-model="scope.row.payMoney" type="number" min="1" @blur="toAddNew(scope.row.money)"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="是否签约" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <div :style="{ color: scope.row.signFlag == 1 ? '' : '#ff0000' }">
                                    {{ scope.row.signFlag == 1 ? '已签约' : '未签约' }}
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="工作组" show-overflow-tooltip>
                            <template>
                                <div>已在组内</div>
                            </template>
                        </el-table-column>
                        <el-table-column label="付款备注" min-width="200" show-overflow-tooltip prop="remark"></el-table-column>
                    </el-table>
                </el-tab-pane>
                <el-tab-pane :label="`认证失败（${resTableData.failureDetails.length}）`">
                    <el-table :data="resTableData.failureDetails" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell" :key="calculationType" max-height="663">
                        <el-table-column type="index" :index="indexMethod"> </el-table-column>
                        <el-table-column label="员工姓名" width="180" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <div>
                                    {{ scope.row.workerName }}
                                </div>
                                {{ scope.row.idCardNumber }}
                            </template>
                        </el-table-column>
                        <el-table-column label="银行卡/支付宝" width="180">
                            <template slot-scope="scope">
                                <div>{{ scope.row.bankNumber }}</div>
                                {{ scope.row.phone }}
                            </template>
                        </el-table-column>
                        <el-table-column label="本次结算" prop="payMoney"></el-table-column>
                        <el-table-column label="付款备注" min-width="200" show-overflow-tooltip prop="remark"></el-table-column>
                        <el-table-column label="认证未通过原因" min-width="200" show-overflow-tooltip prop="bankRemark">
                            <template slot-scope="scope">
                                <div :style="{ color: '#ff0000' }">
                                    {{ scope.row.bankRemark }}
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
            </el-tabs>
        </div>
        <div v-if="loadingOut">
            <div class="project_top">
                <div class="blue_box">发放金额</div>
            </div>
            <el-form ref="form" label-width="120px">
                <el-form-item label="选择钱包">
                    <el-select v-model="form.wallet" placeholder="选择钱包" @change="toChangeWallet" clearable value-key="id" filterable>
                        <el-option :label="i.walletStr" :value="i" v-for="i in walletList" :key="i.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="选择审核人员">
                    <el-select v-model="form.auditorId" placeholder="选择审核人员" @change="toChangeInput">
                        <el-option :label="i.name" :value="i.id" v-for="i in auditPersonList" :key="i.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="发放人数">
                    <span>{{ tableData.length }}</span>
                </el-form-item>
                <el-form-item label="钱包余额">
                    <span>{{ showMoney }}</span>
                </el-form-item>
                <el-alert title="确认发放后人员将自动加入该项目小组内" type="warning" show-icon :closable="false"> </el-alert>
                <el-form-item>
                    <el-button class="button-size" @click="toBeforetSepts"> 上一步 </el-button>
                    <!-- <el-button type="primary" class="button-size" @click="toNextSepts"> 确认发放 </el-button> -->
                    <el-button
                        type="primary"
                        class="button-size"
                        @click="salaryId ? toCalculationRate() : toRealNameRz()"
                        :disabled="tableData.length == 0"
                    >
                        {{ salaryId ? '计算费率' : '实名认证' }}
                    </el-button>
                </el-form-item>
            </el-form>
            <importExcel
                :projectInfoId="projectInfo.id"
                :projectGroupId="groupInfoItem.id"
                :showDia="showDia"
                @closeDia="closeDia"
                @employData="employData"
            ></importExcel>
            <addPeople
                :showAddDia="showAddDia"
                @closeDia="closeDia"
                :groupInfoShow="groupInfoShow"
                @toAddPeople="toAddPeopleHttp"
            ></addPeople>
            <rateDia :showRateDia="showRateDia" :rateInfo="rateInfo" @closeDia="closeDia" @sureSendMoney="sureSendMoney"></rateDia>
            <!-- 敏感词 -->
            <wordAttention v-if="toShowWord" @closeDia="closeDias" :wordList="wordList"></wordAttention>
        </div>
        <div v-if="!loadingOut" style="height: 40vh">
            <div class="loading-green-box">
                <img src="@/assets/img/loadingG.png" class="loading-green-i" alt="" />
                <div class="loading-green-word">
                    正在实名认证中，当前处理进度约为
                    <div class="number-size">{{ smInfo.size }}/{{ smInfo.totalSize }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { Message } from 'element-ui';
import { currentPage } from '@/utils/common';
import importExcel from './importExcelNew.vue';
import addPeople from './addPeople.vue';
import wordAttention from './wordAttention.vue';
import {
    addPeoples,
    getEnterpriseWallet,
    getAuditPersonList,
    enterpriseDetermineIssueV2,
    getEnterpriseGroupWorkerList,
    draftBox,
    getDraftBox,
    employeesCertificationSensitiveWord,
    employeesCertificationV2,
    isCertificationCompleteV2,
    rateCalculationV2,
    excelErrorV2,
    errorDownload
} from '@/api/grant/grant.js';
import rateDia from './rateDia.vue';
export default {
    data() {
        return {
            tableData: [],
            current: 1,
            size: 10,
            form: {},
            showMoney: undefined,
            groupInfoItem: {},
            groupInfoShow: [],
            showDia: false,
            projectInfo: {},
            showAddDia: false,
            walletList: [],
            sendMoney: 0,
            calculationType: undefined,
            auditPersonList: [],
            showRateDia: false,
            rateInfo: {
                DetermineIssue: {
                    worker: [],
                    wallet: {
                        calculationType: undefined
                    }
                }
            },
            rowIndex: undefined,
            fullscreenLoading: false,
            salaryId: '',
            tableHeaderName: '',
            loadingOut: true,
            wordList: [],
            toShowWord: false,
            smInfo: {},
            timeForWait: '',
            firstTable: true,
            resTableData: {
                successDetails: [],
                failureDetails: []
            }
        };
    },
    components: {
        importExcel,
        addPeople,
        rateDia,
        wordAttention
    },
    props: {
        groupInfo: {
            type: Object,
            require: true
        },
        projectName: {
            type: Object,
            require: false
        }
    },
    watch: {
        projectName(val) {
            if (val) {
                this.projectInfo = val;
            }
        },
        groupInfo(val) {
            if (val) {
                this.groupInfoItem = val;
            }
        }
    },
    mounted() {
        if (JSON.parse(sessionStorage.getItem('projectInfo'))) {
            this.groupInfoItem = JSON.parse(sessionStorage.getItem('groupInfo'));
            this.projectInfo = JSON.parse(sessionStorage.getItem('projectInfo'));
        }
        //判断页面是否缓存信息
        if (sessionStorage.getItem('newLists') && JSON.parse(sessionStorage.getItem('initiateInfo'))) {
            // this.tableData = JSON.parse(sessionStorage.getItem('newLists'));
            // this.tableData.forEach((s) => {
            //     this.sendMoney = this.sendMoney + parseFloat(s.money);
            // });
        }
        getEnterpriseWallet().then((res) => {
            res.data.data.forEach((s) => {
                s.walletStr = s.merchantName + '-' + s.companyName + '-' + s.bankName;
            });
            this.walletList = res.data.data;
            if (this.$route.params.salaryId) {
                this.salaryId = this.$route.params.salaryId;
                this.getDraftBoxDetailV2();
            }
            if (this.$route.query.attestation) {
                this.getDraftBoxDetailYz();
            }
        });
        getAuditPersonList({
            type: 3,
            walletId: ''
        }).then((res) => {
            this.auditPersonList = res.data.data;
            console.log(this.auditPersonList);
            this.form.auditorId = res.data.data[0].id;
        });
    },
    beforeDestroy() {
        clearInterval(this.timeForWait);
        clearInterval(this.needTwo);
    },
    methods: {
        rouClassNameFn({ row, rowIndex }) {
            //把每一行的索引放进row
            row.index = rowIndex;
        },
        toCalculationRate() {
            if (this.resTableData.successDetails.length == 0) {
                this.$message.error('发放人员不能为空！');
                return false;
            }
            if (!this.form.auditorId) {
                this.$message.error('请选择审核人员！');
                return false;
            }
            if (!this.form.wallet) {
                this.$message.error('请选择钱包！');
                return false;
            }
            Promise.all(
                this.resTableData.successDetails.map((s, i) => {
                    return new Promise(async (resolve, reject) => {
                        if (!s.payMoney) {
                            reject(i + 1);
                        } else {
                            resolve();
                        }
                    });
                })
            )
                .then(() => {
                    rateCalculationV2({
                        auditorId: this.form.auditorId,
                        projectId: this.projectInfo.id,
                        groupId: this.groupInfoItem.id,
                        wallet: this.form.wallet,
                        worker: this.resTableData.successDetails,
                        salaryId: this.salaryId,
                        headerName: this.tableHeaderName
                    }).then((res) => {
                        if (res.data.code == 0) {
                            this.rateInfo = res.data.data;
                            this.showRateDia = true;
                        }
                    });
                })
                .catch((i) => {
                    this.$message.error(`序号${i}本次结算包含0元或者为空！`);
                });
        },
        indexMethod(index) {
            this.rowIndex = index;
            return index + 1;
        },
        async toExcelError() {
            let res = await excelErrorV2(this.salaryId);
            if (res.data.code != 0) return false;
            errorDownload({
                fileName: res.data.data
            }).then((ress) => {
                this.blobUrl = ress.data;
                let excel = new Blob([this.blobUrl]);
                let url = URL.createObjectURL(excel);
                let a = document.createElement('a');
                a.href = url;
                a.download = '认证失败名单.xlsx';
                a.click();
            });
        },
        handleEdit(idx) {
            this.$confirm(`确定要删除该员工吗？`, '提示', {
                type: 'warning'
            }).then(() => {
                this.tableData.splice(idx, 1);
                //页面缓存表格信息
                sessionStorage.setItem('newLists', JSON.stringify(this.tableData));
                sessionStorage.setItem('initiateInfo', 'true');
            });
        },
        //第一次上传文件验证是否有敏感词
        employData(e) {
            this.showDia = false;
            this.tableData = e.employees;
            this.tableHeaderName = e.headerName;
            this.wordList = e.sensitiveWords;
            this.toShowWord = e.isSensitiveWords;
        },
        async toRealNameRz() {
            if (!this.form.wallet) {
                this.$message.error('请选择钱包！');
                return false;
            }
            let res = await employeesCertificationSensitiveWord(this.tableData);
            if (res.data.code != 0) return false;
            if (res.data.data.isSensitiveWords) {
                this.wordList = res.data.data.sensitiveWords;
                this.toShowWord = true;
            } else {
                let ress = await employeesCertificationV2({
                    list: this.tableData,
                    headerName: this.tableHeaderName,
                    walletId: this.form.wallet.id,
                    projectId: this.projectInfo.id,
                    projectGroupId: this.groupInfoItem.id
                });
                if (ress.data.code != 0) return false;
                this.salaryId = ress.data.data;
                this.loadingOut = false;
                this.smInfo.size = 0;
                this.smInfo.totalSize = this.tableData.length;
                this.timeForWait = setInterval(() => {
                    this.getRZinfoPeole();
                }, 2000);
            }
        },
        toShowDia() {
            this.showDia = true;
        },
        async getRZinfoPeole() {
            let res = await isCertificationCompleteV2(this.salaryId);
            this.smInfo = res.data.data;
            if (!this.smInfo.isComplete) {
                this.loadingOut = false;
            } else {
                this.loadingOut = true;
                this.$forceUpdate();
                this.firstTable = false;
                this.resTableData = res.data.data;
                this.tableData = res.data.data.successDetails;
                clearInterval(this.timeForWait);
            }
        },
        closeDia() {
            this.showDia = false;
            this.showAddDia = false;
            this.showRateDia = false;
        },
        closeDias() {
            this.toShowWord = false;
        },
        toNextSepts() {},
        toBeforetSepts() {
            sessionStorage.setItem('activeStatus', 0);
            this.tableData = [];
            this.$emit('beforeSepts');
            // console.log(this.toShowWord)
        },
        sureInfo(e) {
            // console.log('导入的excel', e);
            // let newListStr = sessionStorage.getItem('newList');
            // this.tableData = this.tableData.concat(JSON.parse(newListStr));
            // this.tableData = this.tableData.filter(function (item, index, self) {
            //     return self.findIndex((el) => el.workerId == item.workerId) === index;
            // });
            // //页面缓存表格信息
            // sessionStorage.setItem('newLists', JSON.stringify(this.tableData));
            // sessionStorage.setItem('initiateInfo', 'true');
            this.tableData = e;
            this.showDia = false;
        },
        //添加收款人弹窗
        toAddPeople() {
            getEnterpriseGroupWorkerList({
                projectId: this.projectInfo.id,
                groupId: this.groupInfoItem.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.groupInfoShow = res.data.data;
                    this.showAddDia = true;
                }
            });
        },
        //走接口信息添加收款人
        toAddPeopleHttp(e) {
            addPeoples({
                projectId: this.projectInfo.id,
                groupId: this.groupInfoItem.id,
                payeeList: e
            }).then((res) => {
                this.tableData = this.tableData.concat(res.data.data);
                this.tableData = this.tableData.filter(function (item, index, self) {
                    return self.findIndex((el) => el.workerId == item.workerId) === index;
                });
                console.log(this.tableData);
                this.showAddDia = false;
                //页面缓存表格信息
                sessionStorage.setItem('newLists', JSON.stringify(this.tableData));
                sessionStorage.setItem('initiateInfo', 'true');
            });
        },
        //更新结算信息
        toAddNew(money) {
            let sendMoney = 0;
            if (money <= 0) {
                this.tableData[this.rowIndex].money = 1;
                return Message.error('发放金额不能小于1');
            }
            // this.tableData.forEach((s) => {
            //     if (s.money) {
            //         sendMoney = sendMoney + parseFloat(s.money);
            //     }
            // });
            // this.sendMoney = sendMoney;
            //页面缓存表格信息
            sessionStorage.setItem('newLists', JSON.stringify(this.tableData));
            sessionStorage.setItem('initiateInfo', 'true');
        },
        toAddNewReamke() {
            sessionStorage.setItem('newLists', JSON.stringify(this.tableData));
            sessionStorage.setItem('initiateInfo', 'true');
        },
        toChangeWallet(val) {
            // console.log(this.form);
            if (val) {
                this.showMoney = (val.currentMoney - val.frozenMoney).toFixed(2);
            } else {
                this.showMoney = undefined;
            }
            // this.calculationType = val.calculationType;
            this.tableData.forEach((s) => {
                s.rateMoney = parseFloat();
            });
            // this.$forceUpdate();
        },
        //确认发放
        sureSendMoney() {
            enterpriseDetermineIssueV2({
                id: this.rateInfo.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.showRateDia = false;
                    sessionStorage.setItem('activeStatus', 0);
                    this.tableData = [];
                    this.$emit('nextSepts');
                }
            });
        },
        toChangeInput() {
            this.$forceUpdate();
        },
        async toSaveDraftBox() {
            if (!this.form.wallet) {
                this.$message.error('请选择钱包！');
                return false;
            }
            let res = await draftBox({
                employeeDtoList: this.tableData,
                projectId: this.projectInfo.id,
                groupId: this.groupInfoItem.id,
                walletId: this.form.wallet.id,
                salaryId: this.salaryId,
                headerName: this.tableHeaderName
            });
            if (res.data.code == 0) {
                this.$confirm('保存成功，是否跳转到付款批次页面查看？', '成功', {
                    confirmButtonText: '确定',
                    cancelButtonText: '再次发起',
                    type: 'success'
                })
                    .then((res) => {
                        this.$router.push('/grant/batch?fromPage=1');
                    })
                    .catch(() => {
                        window.location.reload();
                    });
            }
        },
        async getDraftBoxDetail() {
            let res = await getDraftBox(this.$route.params.salaryId);
            if (res.data.code != 0) return false;
            this.projectInfo.name = res.data.data.projectName;
            this.groupInfoItem.name = res.data.data.groupName;
            this.tableData = res.data.data.employeeDtoList;
            this.projectInfo.id = res.data.data.projectId;
            this.groupInfoItem.id = res.data.data.groupId;
            this.walletList.forEach((v) => {
                if (v.id == res.data.data.walletId) {
                    this.form.wallet = v;
                    this.showMoney = (v.currentMoney - v.frozenMoney).toFixed(2);
                }
            });
            this.salaryId = res.data.data.salaryId;
            this.tableHeaderName = res.data.data.headerName;
        },
        async getDraftBoxDetailV2() {
            let res = await isCertificationCompleteV2(this.$route.params.salaryId);
            if (res.data.code != 0) return false;
            this.loadingOut = true;
            this.firstTable = false;
            this.resTableData = res.data.data;
            this.projectInfo.name = res.data.data.projectName;
            this.groupInfoItem.name = res.data.data.groupName;
            this.tableData = res.data.data.successDetails;
            this.projectInfo.id = res.data.data.projectId;
            this.groupInfoItem.id = res.data.data.groupId;
            this.walletList.forEach((v) => {
                if (v.id == res.data.data.walletId) {
                    this.form.wallet = v;
                    this.showMoney = (v.currentMoney - v.frozenMoney).toFixed(2);
                }
            });
            this.salaryId = res.data.data.salaryId;
            this.tableHeaderName = res.data.data.headerName;
        },
        async getDraftBoxDetailYz() {
            this.salaryId = this.$route.query.salaryId;
            let res = await isCertificationCompleteV2(this.$route.query.salaryId);
            if (res.data.code != 0) return false;
            this.smInfo = res.data.data;
            if (!this.smInfo.isComplete) {
                this.loadingOut = false;
                this.needTwo = setInterval(() => {
                    this.aginDetailInfo();
                }, 2000);
            } else {
                this.loadingOut = true;
                this.firstTable = false;
                this.resTableData = res.data.data;
                this.projectInfo.name = res.data.data.projectName;
                this.groupInfoItem.name = res.data.data.groupName;
                this.tableData = res.data.data.successDetails;
                this.projectInfo.id = res.data.data.projectId;
                this.groupInfoItem.id = res.data.data.groupId;
                clearInterval(this.needTwo);
            }
        },
        async aginDetailInfo() {
            let res = await isCertificationCompleteV2(this.salaryId);
            this.smInfo = res.data.data;
            if (!this.smInfo.isComplete) {
                this.loadingOut = false;
            } else {
                this.loadingOut = true;
                this.firstTable = false;
                this.resTableData = res.data.data;
                this.projectInfo.name = res.data.data.projectName;
                this.groupInfoItem.name = res.data.data.groupName;
                this.tableData = res.data.data.successDetails;
                this.projectInfo.id = res.data.data.projectId;
                this.groupInfoItem.id = res.data.data.groupId;
                clearInterval(this.needTwo);
            }
        }
    }
};
</script>

<style scoped>
.project_name {
    font-size: 18px;
    font-weight: 550;
}
.blue_box {
    padding: 8px 16px;
    background-color: #f9fafc;
    border-radius: 4px;
    border-left: 5px solid #7ca0e5;
    margin: 20px 0;
    width: 300px;
}
.project_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.tip_member {
    border: 1px solid #000;
    color: #000;
    margin-left: 10px;
}
.money {
    font-size: 20px;
    font-weight: 550;
}
.button-size {
    width: 180px;
    height: 40px;
    font-size: 18px;
    margin-top: 20px;
    margin-bottom: 40px;
}
.loading-green-box {
    width: 19vw;
    margin: 0 auto;
    display: flex;
    border-radius: 10px;
    background-color: #eaf9ef;
    align-items: center;
    padding: 40px 60px;
    margin-top: 10vh;
}
.loading-green-i {
    width: 60px;
    height: 60px;
    animation: spin 1.5s linear infinite;
    margin-right: 30px;
}
@keyframes spin {
    from {
        transform: rotate(0);
    }
    to {
        transform: rotate(360deg);
    }
}
.loading-green-word {
    color: #2bc15e;
}
.number-size {
    font-size: 30px;
    margin-top: 5px;
}
</style>
