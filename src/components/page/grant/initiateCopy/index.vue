<template>
    <div class="detail_box">
        <el-card class="left">
            <div slot="header" class="clearfix">
                <span>发起付款</span>
            </div>
            <div class="top_steps">
                <el-steps :active="active" align-center finish-status="success">
                    <el-step title="选择项目及小组"></el-step>
                    <el-step title="确定人员及金额"></el-step>
                    <el-step title="发起付款提交申请"></el-step>
                </el-steps>
            </div>
            <el-form ref="form" label-width="120px" v-show="active == 0">
                <el-form-item label="选择企业">
                    <el-select v-model="form.enterpriseId" placeholder="选择企业" filterable clearable @change="toChoseEnterprise()">
                        <el-option :label="i.name" :value="i.id" v-for="i in enterpriseList" :key="i.id"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="选择项目">
                    <el-select
                        v-model="form.projectInfo"
                        placeholder="选择项目"
                        filterable
                        clearable
                        @change="toChoseProject()"
                        value-key="id"
                    >
                        <el-option :label="i.name" :value="i" v-for="i in projectList" :key="i.id"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="选择工作组">
                    <el-select v-model="form.groupId" placeholder="选择小组" @change="changeInfoNeed" filterable>
                        <el-option :label="i.name" :value="i.id" v-for="i in groupList" :key="i.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-button type="primary" class="button-size" @click="toNextSepts"> 下一步 </el-button>
            </el-form>
            <!-- 项目小组详情 -->
            <project-info
                v-show="active == 1"
                @beforeSepts="beforeSepts"
                @sureSepts="sureSepts"
                :groupInfo="groupInfo"
                :projectName="projectInfo"
                @nextSepts="nextSeptsSure"
                :enterpriseId="this.form.enterpriseId"
            ></project-info>
            <el-result icon="success" title="成功提示" subTitle="发起付款提交申请成功" v-show="active == 2">
                <template slot="extra">
                    <!-- <el-button type="primary" size="medium">返回</el-button> -->
                </template>
            </el-result>
        </el-card>
    </div>
</template>

<script>
import projectInfo from './projectInfo/index.vue';
import { getListEnterprise, getEnterpriseProjectList, getEnterpriseGroupList, getEnterpriseGroupWorkerList } from '@/api/grant/grant.js';
import { Message } from 'element-ui';
export default {
    data() {
        return {
            active: 0,
            form: {
                projectInfo: {
                    id: '',
                    name: ''
                },
                enterpriseId: ''
            },
            projectList: [],
            groupList: [],
            groupInfo: [],
            projectInfo: {},
            enterpriseList: []
        };
    },
    components: {
        projectInfo
    },
    methods: {
        toNextSepts() {
            if (!this.form.projectInfo.id) {
                Message.error('请选择项目！');
                return false;
            }
            if (!this.form.groupId) {
                Message.error('请选择工作组！');
                return false;
            }
            getEnterpriseGroupWorkerList({
                projectId: this.form.projectInfo.id,
                groupId: this.form.groupId,
                enterpriseId: this.form.enterpriseId
            }).then((res) => {
                if (res.data.code == 0) {
                    this.groupInfo = res.data.data;
                    this.active++;
                    // sessionStorage.setItem('activeStatus', this.active);
                    sessionStorage.setItem('groupInfo', JSON.stringify(this.groupInfo));
                    sessionStorage.setItem('projectInfo', JSON.stringify(this.projectInfo));
                }
            });
        },
        jumpGroupWork() {
            getEnterpriseGroupWorkerList({
                enterpriseId: this.form.enterpriseId,
                projectId: this.$route.params.projectId,
                groupId: this.$route.params.groupId
            }).then((res) => {
                if (res.data.code == 0) {
                    this.groupInfo = res.data.data;
                    this.active = 1;
                    // sessionStorage.setItem('activeStatus', this.active);
                    sessionStorage.setItem('groupInfo', JSON.stringify(this.groupInfo));
                    sessionStorage.setItem('projectInfo', JSON.stringify(this.projectInfo));
                }
            });
        },
        beforeSepts() {
            this.active = 0;
        },
        sureSepts() {
            this.active = 2;
            console.log(this.active);
        },
        toChoseProject(i) {
            console.log(i);
            this.form.groupId = '';
            getEnterpriseGroupList({
                projectId: this.form.projectInfo.id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.groupList = res.data.data;
                    this.projectInfo = this.form.projectInfo;
                }
            });
        },
        toChoseEnterprise() {
            this.form.projectInfo = {
                id: '',
                name: ''
            };
            this.form.groupId = '';
            getEnterpriseProjectList({
                enterpriseId: this.form.enterpriseId
            }).then((res) => {
                if (res.data.code == 0) {
                    this.projectList = res.data.data;
                    if (this.$route.params.projectId) {
                        res.data.data.forEach((s) => {
                            if (s.id == this.$route.params.projectId) {
                                this.projectInfo = s;
                                this.jumpGroupWork();
                            }
                        });
                    }
                }
            });
        },
        changeInfoNeed() {
            this.$forceUpdate();
        },
        nextSeptsSure() {
            this.active = 2;
        }
    },
    created() {
        console.log(this.$route.params);
        if (sessionStorage.getItem('activeStatus')) {
            this.active = parseInt(sessionStorage.getItem('activeStatus'));
        }
        getListEnterprise().then((res) => {
            if (res.data.code == 0) {
                this.enterpriseList = res.data.data;
            }
        });
        if (this.$route.params.enterpriseId) {
            this.form.enterpriseId = this.$route.params.enterpriseId
            getEnterpriseProjectList({
                enterpriseId: this.form.enterpriseId
            }).then((res) => {
                if (res.data.code == 0) {
                    this.projectList = res.data.data;
                    res.data.data.forEach((s) => {
                        if (s.id == this.$route.params.projectId) {
                            this.projectInfo = s;
                            this.jumpGroupWork();
                        }
                    });
                }
            });
        }
    }
};
</script>

<style scoped>
.top_steps {
    margin-bottom: 80px;
}
.button-size {
    width: 180px;
    height: 40px;
    font-size: 18px;
    margin-top: 20px;
    margin-bottom: 40px;
}
</style>
