<template>
    <div>
        <el-dialog :title="form.id ? '修改敏感词' : '添加敏感词'" :visible.sync="toshowDia" @close="closeDia">
            <el-form :model="form" ref="ruleForm" label-width="100px" class="demo-ruleForm">
                <el-form-item label="敏感词名称：">
                    <el-input v-model="form.sensitiveName" style="width: 300px"></el-input>
                </el-form-item>
                <el-form-item label="敏感词说明：">
                    <el-input v-model="form.remark" style="width: 300px" type="textarea" :autosize="{ minRows: 3 }"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeDia">取消</el-button>
                <el-button type="primary" @click="form.id ? toModify() : toSureGroup()">确定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    data() {
        return {
            toshowDia: false,
            form: {}
        };
    },
    props: {
        dialogVisible: {
            type: Boolean,
            require: true
        },
        info: {
            type: Object,
            require: false
        }
    },
    watch: {
        dialogVisible: {
            handler(newVal) {
                this.toshowDia = newVal;
            }
        },
        info(val) {
            if (val) {
                this.form = JSON.parse(JSON.stringify(val));
            }
        }
    },
    methods: {
        closeDia() {
            this.$emit('closeDia');
            this.form = {};
        },
        toSureGroup() {
            if (!this.form.sensitiveName) return this.$message.error('请输入敏感词名称！');
            if (!this.form.remark) return this.$message.error('请输入敏感词备注！');
            this.$confirm('确认是否要新增敏感词？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then((res) => {
                this.$emit('addWord', this.form);
                this.form = {};
            });
        },
        toModify() {
            if (!this.form.sensitiveName) return this.$message.error('请输入敏感词名称！');
            if (!this.form.remark) return this.$message.error('请输入敏感词备注！');
            this.$confirm('确认是否要修改敏感词？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then((res) => {
                this.$emit('modifyWord', this.form);
                this.form = {};
            });
        },
        toUpdataInfo() {
            this.$forceUpdate();
        },
        toRemoveGroup() {
            this.$emit('toShowCode');
        }
    },
    created() {}
};
</script>

<style scoped>
.button_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
</style>
