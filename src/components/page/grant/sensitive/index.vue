<template>
    <div>
        <el-card shadow="never">
            <div slot="header" class="clearfix button-list">
                <span>敏感词管理</span>
                <el-button type="primary" style="font-size: 15px; float: right" @click="toAddWord">添加敏感词</el-button>
            </div>
            <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                <el-table-column type="index" label="序号" width="50" fixed="left"></el-table-column>
                <el-table-column
                    prop="sensitiveName"
                    label="敏感词名称"
                    min-width="80"
                    show-overflow-tooltip
                    fixed="left"
                ></el-table-column>
                <el-table-column prop="remark" label="备注说明" min-width="80" show-overflow-tooltip fixed="left"></el-table-column>
                <el-table-column prop="createName" label="创建人"></el-table-column>
                <el-table-column prop="createTime" label="创建时间" min-width="120" show-overflow-tooltip></el-table-column>
                <el-table-column min-width="80" label="操作" fixed="right" header-align="center" align="center">
                    <template slot-scope="scope">
                        <el-button @click="handleEdit(scope.row)" type="text" icon="el-icon-edit" size="medium">编辑</el-button>
                        <el-button @click="handleDetel(scope.row.id)" type="text" style="color: red" icon="el-icon-delete" size="medium">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page="tableData.current"
                    layout="prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
        </el-card>
        <add-word
            :dialog-visible="dialogProject"
            @closeDia="closeDia"
            @addWord="addWordSure"
            :info="info"
            @modifyWord="modifyWord"
        ></add-word>
    </div>
</template>

<script>
import { Message } from 'element-ui';
import addWord from './model/add.vue';
import { sensitiveList, sensitiveAdd, sensitiveRemove,sensitiveEdit } from '@/api/grant/grant.js';
export default {
    components: {
        addWord
    },
    computed: {},
    data() {
        return {
            tableData: {},
            dialogProject: false,
            form: {
                name: '', //名称
                size: 10,
                current: 1
            },
            info: {}
        };
    },
    mounted() {
        this.getData();
    },
    methods: {
        toAddWord() {
            this.dialogProject = true;
        },
        closeDia() {
            this.dialogProject = false;
            this.info = {};
        },
        async getData() {
            let res = await sensitiveList(this.form);
            if (res.data.code != 0) return false;
            this.tableData = res.data.data;
        },
        handleSizeChange(size) {
            this.form.size = size;
            this.form.current = 1;
            this.getData();
        },

        //分页-当前页改变
        handleCurrentChange(current) {
            this.form.current = current;
            this.getData(current);
        },
        async addWordSure(e) {
            let res = await sensitiveAdd(e);
            if (res.data.code != 0) return false;
            this.dialogProject = false;
            this.form.current = 1;
            Message.success('新增成功！');
            this.getData();
        },
        async handleDetel(id) {
            let res = await this.$confirm('确认要删除该敏感词？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            });
            if (res) {
                let ress = await sensitiveRemove(id);
                if (ress.data.code != 0) return false;
                Message.success('删除成功！');
                this.getData();
            }
        },
        async modifyWord(e) {
            let res = await sensitiveEdit(e);
            if (res.data.code != 0) return false;
            this.dialogProject = false;
            Message.success('修改成功！');
            this.getData();
        },
        
        handleEdit(info) {
            this.info = info;
            this.dialogProject = true;
        }
    }
};
</script>

<style scoped lang="scss">
.btn {
    color: #ffffff;
    padding: 5px 10px;
    border-radius: 5px;
}
.button_box {
    display: flex;
    align-items: center;
}
</style>
