<template>
    <div>
        <add :onboardingId="onboardingId"></add>
    </div>
</template>

<script>
import add from './components/add.vue'
    export default {
        components: {
            add
        },
        data() {
            return {
                onboardingId: '',
                form: {

                }
            }
        },
        methods: {
        },
        created() {
            this.$route.query.id ? this.onboardingId = this.$route.query.id : ''
        }
    }
</script>

<style>

</style>