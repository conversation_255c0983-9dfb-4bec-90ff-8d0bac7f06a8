<template>
    <div style="margin:20px">
        <el-row>
            <el-col style="text-align: left" :span="12">
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item :to="{ path: fromListPath }">{{ fromListName }}</el-breadcrumb-item>
                    <el-breadcrumb-item>客户详情</el-breadcrumb-item>
                </el-breadcrumb>
            </el-col>
            <el-col style="text-align: right; margin-right: 0" :span="12">
                <el-tooltip class="item" effect="dark" content="点击复制进件单号" placement="top-start">
                    <span class="tipCopiable" title="通过进件号邀请管理员协助入录进件信息" @click="copyToClipboard(onboarding.onboardingNo)">
                        <img src="@/assets/css/bs-icons/info-circle.svg" style="color: #888; height: 12px" />
                        进件号：{{ onboarding.onboardingNo }}
                        <img src="@/assets/css/bs-icons/copy.svg" style="color: #888; height: 12px" />
                    </span>
                </el-tooltip>
            </el-col>
        </el-row>
        <el-row style="margin: 20px 0;">
            <el-steps :space="200" :active="currentStep" v-if="onboarding.status && onboarding.status < 99" finish-status="success" align-center>
                <el-step v-if="onboarding.status < 0" title="待完善材料"></el-step>
                <el-step v-if="onboarding.status > 0" title="待提交"></el-step>
                <el-step title="待风控审核"></el-step>
                <el-step title="待签约"></el-step>
                <el-step title="合同已邮寄"></el-step>
                <el-step title="待开户"></el-step>
                <el-step title="开户中"></el-step>
                <el-step title="已完成"></el-step>
            </el-steps>
            <el-tag v-if="onboarding.status == 99" type="danger" effect="dark" style="margin-left: 20px; font-size: 16px;">进件已终结</el-tag>
        </el-row>
        <el-row class="action" style="margin: 20px;" v-if="$route.path != '/onboarding/detail'">
            <el-col :span="12" style="text-align: left;">
                <el-button type="primary" size="medium" @click="showEvents = true">查看进件历史</el-button>
            </el-col>
            <el-col :span="12" style="text-align: right;">
                <el-button type="info" size="medium" v-if="[3, 4].includes(onboarding.status) && (privileges.operatorReviewable || privileges.riskControlReviewable)" @click="showDeniedReview = true">审核驳回</el-button>
                <el-button type="primary" size="medium" v-if="[3, 4].includes(onboarding.status) && (privileges.operatorReviewable || privileges.riskControlReviewable)" @click="showApproveReview = true">审核通过</el-button>
                <el-button type="danger" size="medium" v-if="(onboarding.status < 9 && onboarding.status != 1) && privileges.riskControlReviewable" @click="showCloseOnboarding = true">终结进件</el-button>
            </el-col>
        </el-row>
        <el-row style="margin: 20px;" v-else>
            <el-col :span="12" style="text-align: left;">
                <el-button type="primary" size="medium" @click="showEvents = true">查看进件历史</el-button>
            </el-col>
            <el-col :span="12" style="text-align: right;">
                <el-button type="primary" size="medium" v-if="(onboarding.status < 0 || onboarding.status == 99) && privileges.riskControlReviewable" @click="showApproveRecheck = true">复审通过</el-button>
                <el-button type="primary" size="medium" v-if="onboarding.status < 2 && privileges.editable" @click="$router.push({ path: '/onboarding/edit', query: { id: onboardingId } })">编辑</el-button>
                <el-button type="primary" size="medium" v-if="onboarding.status == 5 && privileges.documentAchievable" @click="contractDialogVisible = true">待签约合同归档</el-button>
                <el-button type="primary" size="medium" v-if="onboarding.status == 6 && privileges.documentAchievable" @click="contractDialogVisible = true">已签约合同归档</el-button>
                <el-button type="primary" size="medium" v-if="onboarding.status == 7 && privileges.openAccountable" @click="openAccount(onboarding.id, basicInfo.enterpriseName)">开户</el-button>
                <el-button type="danger" size="medium" v-if="(onboarding.status < 9 && onboarding.status != 1) && privileges.riskControlReviewable" @click="showCloseOnboarding = true">终结进件</el-button>
            </el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="(eventRemarks.length > 0 || (onboarding.status > 4 && Object.keys(attachmentTips).length > 0)) ? 17 : 23" style="margin-left: 20px;">
                <el-row style="margin-top: 20px;" :gutter="20">
                    <el-col :span="12">
                        <el-card class="basic-info" shadow="never">
                            <div slot="header" class="clearfix">
                                <span>基本信息</span>
                            </div>
                            <el-form :model="basicInfo" label-width="120px">
                                <div>
                                    <el-form-item label="企业名称：">
                                        <span>{{ basicInfo.enterpriseName }}</span><el-tag v-if="enterpriseExist" type="danger" effect="dark" style="margin-left: 10px;"><i class="el-icon-warning"></i> 企业已存在</el-tag>
                                    </el-form-item>
                                    <el-form-item label="所属渠道：">
                                        <span>{{ channelName }} <i v-if="onboarding.status == 7 && privileges.openAccountable" class="el-icon-edit" @click="dialogVisible = true" style="color: #409EFF; cursor: pointer; margin-left:10px"></i></span>
                                    </el-form-item>
                                    <el-form-item label="所属税源地：">
                                        <span>{{ taxesNames }} <i v-if="onboarding.status == 7 && privileges.openAccountable" class="el-icon-edit" @click="dialogVisible = true" style="color: #409EFF; cursor: pointer; margin-left:10px"></i></span>
                                    </el-form-item>
                                    <el-form-item label="企业简称：">
                                        <span>{{ basicInfo.enterpriseAbbreviation }}</span>
                                    </el-form-item>
                                    <el-form-item label="纳税人识别号：">
                                        <span>{{ basicInfo.taxpayerIdentification }}</span>
                                    </el-form-item>
                                    <el-form-item label="纳税人资质：">
                                        <span v-if='basicInfo.taxpayerType'>{{ basicInfo.taxpayerType === 1 ? '小规模纳税人' : '一般纳税人'}}</span>
                                    </el-form-item>
                                    <el-form-item label="注册地址：">
                                        <span>{{ basicInfo.registeredAddress }}</span>
                                    </el-form-item>
                                    <el-form-item label="企业法人：">
                                        <span>{{ basicInfo.legalPersonName }}</span>
                                    </el-form-item>
                                    <el-form-item label="企业银行账号：">
                                        <span>{{ basicInfo.bankAccount }}</span>
                                    </el-form-item>
                                    <el-form-item label="企业开户银行：">
                                        <span>{{ basicInfo.bankName }}</span>
                                    </el-form-item>
                                    <el-form-item label="企业主营业务：">
                                        <div style="word-wrap: break-word;">
                                            <span>{{ basicInfo.mainBusiness }}</span>
                                        </div>
                                    </el-form-item>
                                </div>
                            </el-form>
                        </el-card>
                    </el-col>
                    <el-col :span="12">
                        <el-card class="basic-info" shadow="never">
                            <div slot="header" class="clearfix">
                                <span>业务信息</span>
                            </div>
                            <el-form :model="businessInfo" label-width="150px">
                                <div>
                                    <el-form-item label="发票类目：">
                                        <span>{{ businessInfo.invoiceInfo ? businessInfo.invoiceInfo : invoiceCategoryNames }}</span>
                                    </el-form-item>
                                    <el-form-item label="C端用工场景描述：">
                                        <div style="word-wrap: break-word;">
                                            {{ businessInfo.employmentScene }}
                                        </div>
                                    </el-form-item>
                                    <el-form-item label="发放体量（月度）：">
                                        {{ businessInfo.monthlyAmount }} {{ businessInfo.monthlyAmount ? '元' : ''}}
                                    </el-form-item>
                                    <el-form-item label="服务费结算规则：">
                                        <div style="word-wrap: break-word;">
                                            {{ businessInfo.salarySettleRule }}
                                        </div>
                                    </el-form-item>
                                    <el-form-item label="结算费率：">
                                        {{ businessInfo.settleRate }} %
                                    </el-form-item>
                                </div>
                            </el-form>
                        </el-card>
                        <el-card shadow="never" style="margin-top: 20px">
                            <div slot="header" class="clearfix">
                                <span>联系人信息</span>
                            </div>
                            <el-table :data="contactList" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table">
                                <el-table-column prop="name" label="姓名" width="80"></el-table-column>
                                <el-table-column prop="jobTitle" label="职位" width="120"></el-table-column>
                                <el-table-column prop="address" label="地址" min-width="200"></el-table-column>
                            </el-table>
                        </el-card>
                    </el-col>
                </el-row>
                <el-row style="margin-top: 20px">
                    <el-card shadow="never">
                        <div slot="header" class="clearfix">
                            <span>工商材料及实地照片</span>
                        </div>

                        <el-table :data="preAttachments" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table">
                            <el-table-column prop="name" label="资料名称" min-width="200"></el-table-column>
                            <el-table-column prop="valid" label="有效性" width="120" align="center">
                                <template slot-scope="scope">
                                    <el-tag type="success" v-if="scope.row.valid == 1">有效</el-tag>
                                    <el-tag type="danger" v-else-if="scope.row.valid == 0">无效</el-tag>
                                    <el-tag type="warning" v-else>后补</el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="createTime" label="上传时间" min-width="180" align="center"></el-table-column>
                            <el-table-column label="操作" fixed='right' min-width="200" align="center">
                                <template slot-scope='scope'>
                                    <el-button type="primary" size="small" @click="openAttachment(scope.row.attachmentUrl)">点此下载</el-button>
                                    <el-button v-if="$route.path != '/onboarding/detail' && scope.row.valid === 1 && [3, 4].includes(onboarding.status) && (privileges.operatorReviewable || privileges.riskControlReviewable)" type="warning" size="small" @click="changeAttachmentValid(scope.row.id, 2)">后续补充</el-button>
                                    <el-button v-if="$route.path != '/onboarding/detail' && (scope.row.valid === 2 || scope.row.valid === 0) && [3, 4].includes(onboarding.status) && (privileges.operatorReviewable || privileges.riskControlReviewable)" type="success" size="small" @click="changeAttachmentValid(scope.row.id, 1)">改为有效</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div style="text-align: center; margin-top: 20px" v-if="onboarding.status > 4">
                            <el-button type="primary" size="medium" @click="additionalAttachmentsDialogVisible = true">补录材料</el-button>
                        </div>
                    </el-card>
                </el-row>
                <el-row style="margin-top: 20px">
                    <el-card shadow="never">
                        <div slot="header" class="clearfix">
                            <span>合同</span>
                        </div>
                        <el-table :data="contractAttachments" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table">
                            <el-table-column prop="name" label="合同类型" width="200" fixed="left"></el-table-column>
                            <el-table-column prop="valid" label="有效性" width="120" align="center">
                                <template slot-scope="scope">
                                    <el-tag type="success" v-if="scope.row.valid == 1">有效</el-tag>
                                    <el-tag type="danger" v-else-if="scope.row.valid == 0">无效</el-tag>
                                    <el-tag type="warning" v-else>后补</el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="createTime" label="上传时间" min-width="180" align="center"></el-table-column>
                            <el-table-column label="操作" fixed='right' min-width="200" align="center">
                                <template slot-scope='scope'>
                                    <el-button type="primary" size="small" @click="openAttachment(scope.row.attachmentUrl)">点此下载</el-button>
                                    <el-button v-if="$route.path != '/onboarding/detail' && scope.row.valid === 1 && [3, 4].includes(onboarding.status) && (privileges.operatorReviewable || privileges.riskControlReviewable)" type="warning" size="small" @click="changeAttachmentValid(scope.row.id, 2)">后续补充</el-button>
                                    <el-button v-if="$route.path != '/onboarding/detail' && (scope.row.valid === 2 || scope.row.valid === 0) && [3, 4].includes(onboarding.status) && (privileges.operatorReviewable || privileges.riskControlReviewable)" type="success" size="small" @click="changeAttachmentValid(scope.row.id, 1)">改为有效</el-button>
                                    <el-button type="primary" size="small" @click="showExpressDig(scope.row.id)">快递单号</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div style="text-align: center; margin-top: 20px" v-if="onboarding.status > 5">
                            <el-button type="primary" size="medium" @click="additionalContractDialogVisible = true">补录合同</el-button>
                        </div>
                    </el-card>
                </el-row>
            </el-col>
            <el-col :span="6" v-if="eventRemarks.length > 0 || (onboarding.status > 4 && Object.keys(attachmentTips).length > 0)">
                <el-card shadow="never" style="margin-top: 20px;" v-if="eventRemarks.length > 0">
                    <div slot="header" class="clearfix">
                        <span>风控备注</span>
                    </div>
                    <ul v-for="eventRemark in eventRemarks" style="margin-bottom: 20px; padding: 0 20px">
                        <li v-html="eventRemark.replace(/\n/g, '<br>')"></li>
                    </ul>
                </el-card>

                <el-card shadow="never" style="margin-top: 20px;" v-if="onboarding.status > 4 && attachmentTips">
                    <div slot="header" class="clearfix">
                        <span>材料缺失</span>
                    </div>
                    <ul v-for="item in attachmentTips" style="margin-bottom: 20px; padding: 0 20px">
                        <li v-html="item"></li>
                    </ul>
                </el-card>
            </el-col>
        </el-row>
        <el-row class="action" style="margin: 20px 0;" v-if="$route.path != '/onboarding/detail'">
            <el-col style="text-align: center;">
                <el-button type="info" size="medium" v-if="[3, 4].includes(onboarding.status) && (privileges.operatorReviewable || privileges.riskControlReviewable)" @click="showDeniedReview = true">审核驳回</el-button>
                <el-button type="primary" size="medium" v-if="[3, 4].includes(onboarding.status) && (privileges.operatorReviewable || privileges.riskControlReviewable)" @click="showApproveReview = true">审核通过</el-button>
                <el-button type="danger" size="medium" v-if="(onboarding.status < 9 && onboarding.status != 1) && privileges.riskControlReviewable" @click="showCloseOnboarding = true">终结进件</el-button>
            </el-col>
        </el-row>
        <el-row style="margin: 20px 0;" v-else>
            <el-col style="text-align: center;">
                <el-button type="primary" size="medium" v-if="(onboarding.status < 0 || onboarding.status == 99) && privileges.riskControlReviewable" @click="showApproveRecheck = true">复审通过</el-button>
                <el-button type="primary" size="medium" v-if="onboarding.status < 2 && privileges.editable" @click="$router.push({ path: '/onboarding/edit', query: { id: onboardingId } })">编辑</el-button>
                <el-button type="primary" size="medium" v-if="onboarding.status == 5 && privileges.documentAchievable" @click="contractDialogVisible = true">待签约合同归档</el-button>
                <el-button type="primary" size="medium" v-if="onboarding.status == 6 && privileges.documentAchievable" @click="contractDialogVisible = true">已签约合同归档</el-button>
                <el-button type="primary" size="medium" v-if="onboarding.status == 7 && privileges.openAccountable" @click="openAccount(onboarding.id, basicInfo.enterpriseName)">开户</el-button>
                <el-button type="danger" size="medium" v-if="(onboarding.status < 9 && onboarding.status != 1) && privileges.riskControlReviewable" @click="showCloseOnboarding = true">终结进件</el-button>
            </el-col>
        </el-row>

        <el-dialog title="驳回进件申请" :visible.sync="showDeniedReview">
            <el-form>
                <el-form-item label="驳回原因">
                    <el-input type="textarea" rows="5" @input="changeReviewComment" v-model="reviewComment" placeholder="请简述驳回原因"></el-input>
                </el-form-item>
                <el-form-item label="快速回复列表">
                    <el-select v-model="fastCommonReply" placeholder="请选择快速回复" @change="changeFastCommonReply">
                        <el-option v-for="item in fastCommonReplyList" :key="item" :label="item" :value="item"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="showDeniedReview = false">取 消</el-button>
                <el-button type="primary" @click="reviewDenied">驳 回</el-button>
            </div>
        </el-dialog>

        <el-dialog title="终结进件" :visible.sync="showCloseOnboarding">
            <el-alert type="warning" title="终结进件后，除复审外，该进件将无法继续进行，请谨慎操作。" show-icon></el-alert>
            <el-form>
                <el-form-item label="终结原因">
                    <el-input type="textarea" rows="5" @input="changeReviewComment" v-model="reviewComment" placeholder="请简述终结原因"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="showCloseOnboarding = false">取 消</el-button>
                <el-button type="primary" @click="closeOnboarding">终 结</el-button>
            </div>
        </el-dialog>

        <el-dialog title="审核进件申请" :visible.sync="showApproveReview">
            <el-form>
                <el-form-item label="备注">
                    <el-input type="textarea" rows="5" @input="changeReviewComment" v-model="reviewComment" placeholder=""></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="showApproveReview = false">取 消</el-button>
                <el-button type="primary" @click="reviewApprove">通 过</el-button>
            </div>
        </el-dialog>

        <el-dialog title="复审进件申请" :visible.sync="showApproveRecheck" :beforeClose="beforeRecheckDialogClose">
            <el-alert type="warning" title="复审通过后，该进件将转入“待签约”状态。" show-icon></el-alert>
            <el-form ref="recheckForm" :model="recheckData">
                <el-form-item label="复审意见" prop="recheckComment" :rules="[{ required: true, message: '请填写复审意见', trigger: 'blur' }]">
                    <el-input type="textarea" rows="5" @input="changeRecheckComment" v-model="recheckData.recheckComment" placeholder=""></el-input>
                </el-form-item>
                <el-form-item label="附件(可选)" prop="recheckAttachment">
                    <el-upload
                        class="file-upload"
                        action=""
                        :limit="1"
                        :http-request="uploadFiles"
                        :before-upload="beforeUpload"
                        :on-success="recheckAttachmentUploadSuccess"
                        :on-error="recheckAttachmentUploadError"
                        :on-remove="recheckAttachmentRemove"
                        :on-cancel="recheckAttachmentRemove"
                        :file-list="recheckData.recheckAttachment"
                        accept=".jpg,.jpeg,.png,.pdf,.zip,.rar"
                        list-type="">
                        <el-button slot="trigger" size="small" type="primary">点击上传</el-button>
                        <div slot="tip" class="el-upload__tip">只能上传jpg、png、pdf、zip和rar文件，且不超过10MB</div>
                    </el-upload>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="showApproveRecheck = false">取 消</el-button>
                <el-button type="primary" @click="recheckApprove">通 过</el-button>
            </div>
        </el-dialog>

        <el-dialog title="进件历史" :visible.sync="showEvents">
            <el-timeline>
                <el-timeline-item v-for="(onboardingEvent, index) in onboardingEventList" :key="index" :timestamp="onboardingEvent.createTime" placement="top">
                    <el-card shadow="never">
                        <h4>{{ onboardingEvent.eventName }}</h4>
                        <p v-html="onboardingEvent.remark ? onboardingEvent.remark.replace(/\n/g, '<br>') : ''"></p>
                        <p style="text-align: right" v-if="onboardingEvent.userName"><span style="font-weight: 600">操作员:</span> {{ onboardingEvent.userName}}</p>
                    </el-card>
                </el-timeline-item>
            </el-timeline>

            <div slot="footer" class="dialog-footer">
                <el-button @click="showEvents = false" type="success">关 闭</el-button>
            </div>
        </el-dialog>

        <el-dialog title="签约归档" :visible.sync="contractDialogVisible">
            <el-form :model="contract" ref="contractForm">
                <el-form-item label="待邮寄/待签约合同扫描件：" prop="files" v-if="onboarding.status == 5">
                    <el-upload
                        class="file-upload"
                        :class="{ 'hide-upload': contract.deliverFiles.length > 4 }"
                        action=""
                        :limit="5"
                        :http-request="uploadFiles"
                        :before-upload="beforeUpload"
                        :on-success="deliverContractUploadSuccess"
                        :on-error="deliverContractUploadError"
                        :on-remove="deliverContractRemove"
                        :on-cancel="deliverContractRemove"
                        :file-list="contract.deliverFiles"
                        accept=".doc,.docx,.pdf,.jpg,.jpeg,.png,.xls,.xlsx,.csv,.zip,.rar,.7z"
                        list-type="">
                        <el-button slot="trigger" size="small" type="primary">点击上传</el-button>
                        <div slot="tip" class="el-upload__tip">可上传jpg、png、pdf、Word、Excel、zip和rar等文件格式，且不超过10MB</div>
                        <a target='_blank' :href="onboardingTemplateURL" style="color: red; margin-left: 10px;">点此下载合同模版</a>
                    </el-upload>
                </el-form-item>
                <el-form-item label="终版（双方已盖章确认）合同扫描件：" prop="files" v-if="onboarding.status == 6">
                    <el-upload
                        class="file-upload"
                        :class="{ 'hide-upload': contract.finalFiles.length > 4 }"
                        action=""
                        :limit="5"
                        :http-request="uploadFiles"
                        :before-upload="beforeUpload"
                        :on-success="finalContractUploadSuccess"
                        :on-error="finalContractUploadError"
                        :on-remove="finalContractRemove"
                        :on-cancel="finalContractRemove"
                        :file-list="contract.finalFiles"
                        accept=".doc,.docx,.pdf,.jpg,.jpeg,.png,.xls,.xlsx,.csv,.zip,.rar,.7z"
                        list-type="">
                        <el-button size="small" type="primary">点击上传</el-button>
                        <div slot="tip" class="el-upload__tip">可上传jpg、png、pdf、Word、Excel、zip和rar等文件格式，且不超过10MB</div>
                    </el-upload>
                </el-form-item>
                <el-form-item prop="deliverName" label='快递公司' v-if="onboarding.status > 4">
                    <el-input id="deliverName" v-model.trim="contract.deliverName" @input="changeDeliverName" placeholder="请输入快递公司名称"></el-input>
                </el-form-item>
                <el-form-item prop="deliverNo" label='快递单号' v-if="onboarding.status > 4">
                    <el-input id="deliverNo" v-model.trim="contract.deliverNo" @input="changeDeliverNo" placeholder="请输入快递单号"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="contractDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="saveContractInfo">保 存</el-button>
                <el-button type="primary" @click="ignoreContract" v-if="onboarding.status == 5 || onboarding.status == 6">暂时跳过</el-button>
            </div>
        </el-dialog>

        <el-dialog title="补录合同" :visible.sync="additionalContractDialogVisible">
            <el-form :model="contract">
                <el-form-item label="待邮寄/待签约合同扫描件：" prop="files" v-if="onboarding.status > 5 && contract.finalFiles.length < 1">
                    <el-upload
                        class="file-upload"
                        :class="{ 'hide-upload': contract.deliverFiles.length > 4 }"
                        action=""
                        :limit="5"
                        :http-request="uploadFiles"
                        :before-upload="beforeUpload"
                        :on-success="deliverContractUploadSuccess"
                        :on-error="deliverContractUploadError"
                        :on-remove="deliverContractRemove"
                        :on-cancel="deliverContractRemove"
                        :file-list="contract.deliverFiles"
                        accept=".doc,.docx,.pdf,.jpg,.jpeg,.png,.xls,.xlsx,.csv,.zip,.rar,.7z"
                        list-type="">
                        <el-button slot="trigger" size="small" type="primary">点击上传</el-button>
                        <div slot="tip" class="el-upload__tip">可上传jpg、png、pdf、Word、Excel、zip和rar等文件格式，且不超过10MB</div>
                        <a target='_blank' :href="onboardingTemplateURL" style="color: red; margin-left: 10px;">点此下载合同模版</a>
                    </el-upload>
                </el-form-item>
                <el-form-item label="终版（双方已盖章确认）合同扫描件：" prop="files" v-if="onboarding.status > 6 && contract.deliverFiles.length < 1">
                    <el-upload
                        class="file-upload"
                        :class="{ 'hide-upload': contract.finalFiles.length > 4 }"
                        action=""
                        :limit="5"
                        :http-request="uploadFiles"
                        :before-upload="beforeUpload"
                        :on-success="finalContractUploadSuccess"
                        :on-error="finalContractUploadError"
                        :on-remove="finalContractRemove"
                        :on-cancel="finalContractRemove"
                        :file-list="contract.finalFiles"
                        accept=".doc,.docx,.pdf,.jpg,.jpeg,.png,.xls,.xlsx,.csv,.zip,.rar,.7z"
                        list-type="">
                        <el-button size="small" type="primary">点击上传</el-button>
                        <div slot="tip" class="el-upload__tip">可上传jpg、png、pdf、Word、Excel、zip和rar等文件格式，且不超过10MB</div>
                    </el-upload>
                </el-form-item>
                <el-form-item prop="deliverName" label='快递公司' v-if="onboarding.status > 5">
                    <el-input id="deliverName" v-model.trim="contract.deliverName" @input="changeDeliverName" placeholder="请输入快递公司名称"></el-input>
                </el-form-item>
                <el-form-item prop="deliverNo" label='快递单号' v-if="onboarding.status > 5">
                    <el-input id="deliverNo" v-model.trim="contract.deliverNo" @input="changeDeliverNo" placeholder="请输入快递单号"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="additionalContractDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="saveExtraContracts">保 存</el-button>
            </div>
        </el-dialog>

        <el-dialog title="修改 渠道与税源地" :visible.sync="dialogVisible">
            <el-form :model="onboarding">
                <el-form-item label="所属渠道" prop="channelId">
                    <el-select v-model="onboarding.channelId" placeholder="请选择渠道">
                        <el-option
                            v-for="item in channelList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="所属税源地" prop="taxes">
                    <el-select v-model="taxes" multiple placeholder="请选择渠道">
                        <el-option
                            v-for="item in taxesList"
                            :key="item.taxesId"
                            :label="item.taxesName"
                            :value="item.taxesId">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="editChannelAndTax">保 存</el-button>
            </div>
        </el-dialog>

        <el-dialog title="补录进件材料" :visible.sync="additionalAttachmentsDialogVisible" :beforeClose="additionalAttachmentsDialogClose">
            <el-form ref="attachmentForm" :model="additionalAttachments" label-width="300px">
                <el-form-item label="营业执照扫描件" prop="businessLicenceList">
                    <el-upload
                        class="file-upload"
                        :class="{ 'hide-upload': additionalAttachments.businessLicenceList.length > 0 }"
                        ref="businessLicenceUploader"
                        action=""
                        :limit="1"
                        :http-request="uploadFiles"
                        :before-upload="beforeUpload"
                        :on-success="businessLicenceUploadSuccess"
                        :on-error="businessLicenceUploadError"
                        :on-remove="businessLicenceeRemove"
                        :on-cancel="businessLicenceeRemove"
                        :file-list="additionalAttachments.businessLicenceList"
                        :on-exceed="businessLicenceExceed"
                        accept=".jpg,.jpeg,.png,.pdf,.zip,.rar"
                        list-type="picture-card">
                        <i class="el-icon-plus avatar-uploader-icon"></i>
                        <div slot="tip" class="el-upload__tip">只能上传jpg、png、pdf、zip和rar文件，且不超过10MB</div>
                    </el-upload>
                </el-form-item>
                <el-form-item label="社保证明、完税证明或上下游发票" prop="socialSecurityList">
                    <el-upload
                        class="file-upload"
                        :class="{ 'hide-upload': additionalAttachments.socialSecurityList.length > 4 }"
                        action=""
                        :limit="5"
                        :http-request="uploadFiles"
                        :before-upload="beforeUpload"
                        :on-success="socialSecurityUploadSuccess"
                        :on-error="socialSecurityUploadError"
                        :on-remove="socialSecurityRemove"
                        :on-cancel="socialSecurityRemove"
                        :file-list="additionalAttachments.socialSecurityList"
                        accept=".jpg,.jpeg,.png,.pdf,.zip,.rar"
                        list-type="picture-card">
                        <i class="el-icon-plus avatar-uploader-icon"></i>
                        <div slot="tip" class="el-upload__tip">只能上传jpg、png、pdf、zip和rar文件，且不超过10MB</div>
                    </el-upload>
                </el-form-item>
                <el-form-item label="企业门头照" prop="doorPicList">
                    <el-upload
                        class="file-upload"
                        :class="{ 'hide-upload': additionalAttachments.doorPicList.length > 4 }"
                        action=""
                        :limit="5"
                        :http-request="uploadFiles"
                        :before-upload="beforeUpload"
                        :on-success="doorPicUploadSuccess"
                        :on-error="doorPicUploadError"
                        :on-remove="doorPicRemove"
                        :on-cancel="doorPicRemove"
                        :file-list="additionalAttachments.doorPicList"
                        accept=".jpg,.jpeg,.png,.pdf,.zip,.rar"
                        list-type="picture-card">
                        <i class="el-icon-plus avatar-uploader-icon"></i>
                        <div slot="tip" class="el-upload__tip">只能上传jpg、png、pdf、zip和rar文件，且不超过10MB</div>
                    </el-upload>
                </el-form-item>
                <el-form-item label="办公室场景照" prop="officePicList">
                    <el-upload
                        class="file-upload"
                        :class="{ 'hide-upload': additionalAttachments.officePicList.length > 4 }"
                        action=""
                        :limit="5"
                        :http-request="uploadFiles"
                        :before-upload="beforeUpload"
                        :on-success="officePicUploadSuccess"
                        :on-error="officePicUploadError"
                        :on-remove="officePicRemove"
                        :on-cancel="officePicRemove"
                        :file-list="additionalAttachments.officePicList"
                        accept=".jpg,.jpeg,.png,.pdf,.zip,.rar"
                        list-type="picture-card">
                        <i class="el-icon-plus avatar-uploader-icon"></i>
                        <div slot="tip" class="el-upload__tip">只能上传jpg、png、pdf、zip和rar文件，且不超过10MB</div>
                    </el-upload>
                </el-form-item>
                <el-form-item label="C端用工场景照" prop="workingPicList">
                    <el-upload
                        class="file-upload"
                        :class="{ 'hide-upload': additionalAttachments.workingPicList.length > 4 }"
                        action=""
                        :limit="5"
                        :http-request="uploadFiles"
                        :before-upload="beforeUpload"
                        :on-success="workingPicUploadSuccess"
                        :on-error="workingPicUploadError"
                        :on-remove="workingPicRemove"
                        :on-cancel="workingPicRemove"
                        :file-list="additionalAttachments.workingPicList"
                        accept=".jpg,.jpeg,.png,.pdf,.zip,.rar"
                        list-type="picture-card">
                        <i class="el-icon-plus avatar-uploader-icon"></i>
                        <div slot="tip" class="el-upload__tip">只能上传jpg、png、pdf、zip和rar文件，且不超过10MB</div>
                    </el-upload>
                </el-form-item>
                <el-form-item label="基本存款账户信息照片" prop="basicBankInfo">
                    <el-upload
                        class="file-upload"
                        :class="{ 'hide-upload': additionalAttachments.basicBankInfo.length > 0 }"
                        action=""
                        :limit="1"
                        :http-request="uploadFiles"
                        :before-upload="beforeUpload"
                        :on-success="basicBankInfoUploadSuccess"
                        :on-error="basicBankInfoUploadError"
                        :on-remove="basicBankInfoRemove"
                        :on-cancel="basicBankInfoRemove"
                        :file-list="additionalAttachments.basicBankInfo"
                        accept=".jpg,.jpeg,.png,.pdf,.zip,.rar"
                        list-type="picture-card">
                        <i class="el-icon-plus avatar-uploader-icon"></i>
                        <div slot="tip" class="el-upload__tip">只能上传jpg、png、pdf、zip和rar文件，且不超过10MB</div>
                    </el-upload>
                </el-form-item>
                <el-form-item label="法人身份证正面照" prop="legalPersonIdCardFront">
                    <el-upload
                        class="file-upload"
                        :class="{ 'hide-upload': additionalAttachments.legalPersonIdCardFront.length > 0 }"
                        action=""
                        :limit="1"
                        :http-request="uploadFiles"
                        :before-upload="beforeUpload"
                        :on-success="legalPersonIdCardFrontUploadSuccess"
                        :on-error="legalPersonIdCardFrontUploadError"
                        :on-remove="legalPersonIdCardFrontRemove"
                        :on-cancel="legalPersonIdCardFrontRemove"
                        :file-list="additionalAttachments.legalPersonIdCardFront"
                        accept=".jpg,.jpeg,.png,.pdf,.zip,.rar"
                        list-type="picture-card">
                        <i class="el-icon-plus avatar-uploader-icon"></i>
                        <div slot="tip" class="el-upload__tip">只能上传jpg、png、pdf、zip和rar文件，且不超过10MB</div>
                    </el-upload>
                </el-form-item>
                <el-form-item label="法人身份证反面照" prop="legalPersonIdCardBack">
                    <el-upload
                        class="file-upload"
                        :class="{ 'hide-upload': additionalAttachments.legalPersonIdCardBack.length > 0 }"
                        action=""
                        :limit="1"
                        :http-request="uploadFiles"
                        :before-upload="beforeUpload"
                        :on-success="legalPersonIdCardBackUploadSuccess"
                        :on-error="legalPersonIdCardBackUploadError"
                        :on-remove="legalPersonIdCardBackRemove"
                        :on-cancel="legalPersonIdCardBackRemove"
                        :file-list="additionalAttachments.legalPersonIdCardBack"
                        accept=".jpg,.jpeg,.png,.pdf,.zip,.rar"
                        list-type="picture-card">
                        <i class="el-icon-plus avatar-uploader-icon"></i>
                        <div slot="tip" class="el-upload__tip">只能上传jpg、png、pdf、zip和rar文件，且不超过10MB</div>
                    </el-upload>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="additionalAttachmentsDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="saveAdditionalAttachments">保 存</el-button>
            </div>
        </el-dialog>

        <el-dialog title="快递信息" :visible.sync="showExpress" :before-close="hideExpressDig" width="30%">
            <el-form :model="expressInfo" ref="expressInfoForm">
                <el-form-item hidden>
                    <el-input v-model="expressInfo.id"></el-input>
                </el-form-item>
                <el-form-item hidden>
                    <el-input v-model="expressInfo.attachmentId"></el-input>
                </el-form-item>
                <el-form-item label="快递公司" prop="deliveryName" :rules="[{ required: true, message: '请填写快递公司', trigger: 'blur' }]">
                    <el-input v-model="expressInfo.deliveryName" placeholder="请输入快递公司名称"></el-input>
                </el-form-item>
                <el-form-item label="快递单号" prop="deliveryNo" :rules="[{ required: true, message: '请填写快递单号', trigger: 'blur' }]">
                    <el-input v-model="expressInfo.deliveryNo" placeholder="请输入快递单号"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="hideExpressDig">取 消</el-button>
                <el-button type="primary" @click="saveExpressInfo">保 存</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import { categoryListAll } from '@/api/branch/enterprise.js'
    import { getOnboardingInfo, auditOnboarding, saveContractInfo, openAccount, editOnboarding, saveAdditionalAttachments, getDeliveryInfo, saveDeliveryInfo, recheckPass, closeOnboarding, updateAttachmentValid } from '@/api/onboarding.js'
    import { listTaxesByType, getChannelList } from '@/api/branch/enterprise.js';
    import { fileUpload } from '@/api/system/user.js'
    import {Message} from 'element-ui'
    import {cdnHost} from '@/config/env.js'
    import { ONBOARDING_CONTRACT_TEMPLATE_URL, OSS_URL } from '@/api/config';
    import axios from 'axios';
    export default {
        computed: {
            invoiceCategoryNames() {
                if(!this.businessInfo.invoiceCategoryIds) {
                    return
                }
                return this.businessInfo.invoiceCategoryIds.map(id => {
                let category = this.invoiceCategoryList.find(item => item.id === id);
                return category ? category.nameStr : '';
                }).join(', ');
            },
            channelName() {
                console.log("channelList: ", this.channelList);
                console.log("channelId: ", this.onboarding.channelId);
                if(this.channelList.length > 0 && this.onboarding.channelId) {
                    let channelItem = this.channelList.find(item => item.id === this.onboarding.channelId)
                    console.log("channelItem: ", channelItem);
                    return channelItem.name;
                } else {
                    return '';
                }
            },
            taxesNames() {
                if(this.taxesList.length === 0) {
                    return '';
                }
                return this.taxes.map(taxId => {
                    let info = this.taxesList.find(item => item.taxesId === taxId);
                    return info ? info.taxesName : '';
                }).join(', ');
            },
            fromListPath() {
                return this.fromList === 'referrer' ? '/onboarding/my_list' : '/onboarding/list';
            },
            fromListName() {
                return this.fromList === 'referrer' ? '我的进件' : '进件列表';
            },
            currentStep() {
                if (this.onboarding.status === 9) {
                    return this.onboarding.status;
                }

                return this.onboarding.status < 0 ? 0 : (this.onboarding.status < 4 ? this.onboarding.status - 1 : this.onboarding.status - 3);
            }
        },
        data() {
            return {
                showDeniedReview: false,
                showApproveReview: false,
                showApproveRecheck: false,
                showCloseOnboarding: false,
                taxesList: [],
                channelList: [],
                invoiceCategoryList: [],
                onboardingId: '',
                onboarding: {},
                basicInfo: {},
                businessInfo: {},
                contactList: [],
                attachments: [],
                preAttachments: [],      // 进件材料
                contractAttachments: [],    // 合同材料
                taxes: [],
                reviewComment: "",
                fromList: "",
                attachmentTypes: {
                    1: '营业执照扫描件',
                    2: '社保证明/完税证明/上下游发票',
                    3: '企业门头照',
                    4: '办公室场景照',
                    5: '用工场景照',
                    6: '待签约/邮寄合同',
                    7: '已签约合同',
                    9: '银行基本户信息照',
                    10: '法人身份证正面',
                    11: '法人身份证背面'
                },
                preAttachmentTypes: [1, 2, 3, 4, 5, 9, 10, 11],
                contractAttachmentTypes: [6, 7],
                onboardingEventList: [],
                eventRemarks: [],
                showEvents: false,
                contractDialogVisible: false,
                additionalContractDialogVisible: false,
                additionalAttachmentsDialogVisible: false,
                contract: {
                    deliverFiles: [],
                    finalFiles: [],
                    deliverName: '',
                    deliverNo: ''
                },
                expressInfo: {
                    id: '',
                    onboardingId: '',
                    attachmentId: '',
                    deliveryName: '',
                    deliveryNo: '',
                },
                privileges: {
                    "editable": false,
                    "operatorReviewable": false,
                    "riskControlReviewable": false,
                    "documentAchievable": false,
                    "openAccountable": false
                },
                onboardingTemplateURL: ONBOARDING_CONTRACT_TEMPLATE_URL,
                dialogVisible: false,
                additionalAttachments: {
                    businessLicenceList: [],
                    socialSecurityList: [],
                    doorPicList: [],
                    officePicList: [],
                    workingPicList: [],
                    basicBankInfo: [],
                    legalPersonIdCardFront: [],
                    legalPersonIdCardBack: []
                },
                attachmentTips: {
                    1: '缺少营业执照扫描件',
                    2: '缺少社保证明/完税证明/上下游发票',
                    3: '缺少企业门头照',
                    4: '缺少办公室场景照',
                    5: '缺少用工场景照',
                    6: '缺少待签约/邮寄合同',
                    7: '缺少已签约合同',
                    9: '缺少银行基本户信息照',
                    10: '缺少法人身份证正面',
                    11: '缺少法人身份证背面'
                },
                showExpress: false,
                recheckData: {
                    recheckComment: '',
                    recheckAttachment: []
                },
                fastCommonReply: '',
                fastCommonReplyList: [
                    '门头照不符合要求，提供一镜到底的视频，视频从外部门头到内部办公区，且在内部办公区拍到本司营业执照。\n',
                    '门头照不符合要求，提供包含公司名称门牌和完整门头外观的门头照。\n',
                    '提供近半年完税证明或上下游发票或社保缴费证明（相关部门盖章版）。\n',
                ],
                enterpriseExist: false
            }
        },
        methods: {
            async copyToClipboard(text) {
                try {
                    await navigator.clipboard.writeText(text);
                    Message.success('复制进件单号成功')
                } catch (err) {
                    console.error('Failed to copy: ', err);
                }
            },
            changeReviewComment() {

            },
            changeRecheckComment(e) {
                this.recheckData.recheckComment = e;
            },
            editChannelAndTax() {
                editOnboarding({
                    id: this.onboarding.id,
                    channelId: this.onboarding.channelId,
                    taxIds: this.taxes
                }).then((res) => {
                    if (res.data.code == 0) {
                        this.$message.success('修改成功');
                        this.dialogVisible = false;
                    }
                });
            },
            reviewApprove() {
                this.audit(true, this.reviewComment);
            },
            recheckApprove() {
                this.$refs.recheckForm.validate((valid) => {
                    if (!valid) {
                        return false;
                    }
                    console.log(this.recheckData);
                    recheckPass({
                        onboardingId: this.onboardingId,
                        auditOpinion: this.recheckData.recheckComment,
                        attachmentUri: this.recheckData.recheckAttachment.length > 0 ? this.recheckData.recheckAttachment[0].uri : ''
                    }).then((res) => {
                        if (res.data.code == 0) {
                            this.$message({
                                message: '复审完成',
                                type: 'success'
                            });
                            this.$router.push({ path: '/onboarding/list' });
                        }
                    }).catch((error) => {
                        this.$message({
                            message: '复审失败，请重试',
                            type: 'error'
                        });
                    })
                });
            },
            reviewDenied() {
                this.audit(false, this.reviewComment);
            },
            audit(isPassed, comment) {
                if(!isPassed && comment.length < 1) {
                    this.$message({
                        message: '请简要描述驳回原因',
                        type: 'warning'
                    });
                    return;
                }
                let auditType = 0;
                if(this.onboarding.status === 3) {
                    auditType = 1;
                } else if(this.onboarding.status === 4) {
                    auditType = 2;
                } else {
                    this.$message({
                        message: '进件单状态异常，请刷新页面',
                        type: 'error'
                    });
                }
                auditOnboarding({
                    onboardingId: this.onboardingId,
                    passed: isPassed,
                    auditOpinion: comment,
                    auditType: auditType
                }).then((res) => {
                    if (res.data.code == 0) {
                        this.$message({
                            message: '审核完成',
                            type: 'success'
                        });
                        this.$router.push({ path: '/onboarding/list' });
                    }
                }).catch((error) => {
                    this.$message({
                        message: '审核失败，请重试',
                        type: 'error'
                    });
                })
            },
            closeOnboarding() {
                if(this.reviewComment.length < 1) {
                    this.$message({
                        message: '请简要描述终结原因',
                        type: 'warning'
                    });
                    return;
                }
                closeOnboarding({
                    onboardingId: this.onboarding.id,
                    auditOpinion: this.reviewComment
                }).then((res) => {
                    if (res.data.code == 0) {
                        this.$message.success('终结成功');
                        this.showCloseOnboarding = false;
                        this.$router.push({ path: '/onboarding/list' });
                    }
                }).catch((error) => {
                    this.$message.error('终结失败，请重试');
                    this.showCloseOnboarding = false;
                    this.getInfo();
                });
            },
            beforeUpload(file) {
                if(file.size > 10 * 1024 * 1024 ) {
                    Message.error('文件大小不能超过10MB');
                    return false;
                }
            },
            uploadFiles(option) {
                const _this = this;
                const file = option.file;
                let formData = new FormData();
                formData.append('file', file);
                formData.append('path', 'onboarding');

                fileUpload(formData).then((res) => {
                    if (res != undefined && res.data.code == 0) {
                        let new_file = {
                            name: file.name,
                            response: res,
                            percentage: 0,
                            raw: file,
                            size: file.size,
                            status: 'success',
                            uid: file.uid,
                            url: res.data.data
                        }
                        option.onSuccess(res, new_file);
                    } else {
                        option.onError(res, file);
                    }
                });
            },
            businessLicenceUploadSuccess(res, file, fileList) {
                if (res.status == 200 && res.data.code == 0) {
                    Message.success('上传成功');
                    file.uri = res.data.data;
                    file.url = cdnHost + res.data.data;
                    this.additionalAttachments.businessLicenceList = [file];
                } else {
                    Message.error('上传失败');
                }
            },
            businessLicenceeRemove(file, fileList) {
                this.additionalAttachments.businessLicenceList = fileList;
            },
            businessLicenceExceed(files) {
                this.$refs.businessLicenceUploader.clearFiles();
                this.$refs.businessLicenceUploader.handleStart(files[0]);
                this.uploadFiles({
                    file: files[0],
                    onSuccess: this.businessLicenceUploadSuccess,
                    onError: this.businessLicenceUploadError
                });
            },
            businessLicenceUploadError(err, file, fileList) {

            },

            socialSecurityUploadSuccess(res, file, fileList) {
                if (res.status == 200 && res.data.code == 0) {
                    Message.success('上传成功');
                    file.uri = res.data.data;
                    file.url = cdnHost + res.data.data;
                    this.additionalAttachments.socialSecurityList = [...this.additionalAttachments.socialSecurityList, file];
                } else {
                    Message.error('上传失败');
                }
            },
            socialSecurityUploadError(err, file, fileList) {
                Message.error('上传失败');
            },
            socialSecurityRemove(file, fileList) {
                this.additionalAttachments.socialSecurityList = fileList;
            },

            doorPicUploadSuccess(res, file, fileList) {
                if (res.status == 200 && res.data.code == 0) {
                    Message.success('上传成功');
                    file.uri = res.data.data;
                    file.url = cdnHost + res.data.data;
                    this.additionalAttachments.doorPicList = [...this.additionalAttachments.doorPicList, file];
                } else {
                    Message.error('上传失败');
                }
            },
            doorPicUploadError(err, file, fileList) {
                Message.error('上传失败');
            },
            doorPicRemove(file, fileList) {
                this.additionalAttachments.doorPicList = fileList;
            },

            officePicUploadSuccess(res, file, fileList) {
                if (res.status == 200 && res.data.code == 0) {
                    Message.success('上传成功');
                    file.uri = res.data.data;
                    file.url = cdnHost + res.data.data;
                    this.additionalAttachments.officePicList = [...this.additionalAttachments.officePicList, file];
                } else {
                    Message.error('上传失败');
                }
            },
            officePicUploadError(err, file, fileList) {
                Message.error('上传失败');
            },
            officePicRemove(file, fileList) {
                this.additionalAttachments.officePicList = fileList;
                this.attachmentsChanged = true;
            },
            workingPicUploadSuccess(res, file, fileList) {
                if (res.status == 200 && res.data.code == 0) {
                    Message.success('上传成功');
                    file.uri = res.data.data;
                    file.url = cdnHost + res.data.data;
                    this.additionalAttachments.workingPicList = [...this.additionalAttachments.workingPicList, file];
                } else {
                    Message.error('上传失败');
                }
            },
            workingPicUploadError(err, file, fileList) {
                Message.error('上传失败');
            },
            workingPicRemove(file, fileList) {
                this.additionalAttachments.workingPicList = fileList;
            },

            basicBankInfoUploadSuccess(res, file, fileList) {
                if (res.status == 200 && res.data.code == 0) {
                    Message.success('上传成功');
                    file.uri = res.data.data;
                    file.url = cdnHost + res.data.data;
                    this.additionalAttachments.basicBankInfo = [file];
                } else {
                    Message.error('上传失败');
                }
            },
            basicBankInfoUploadError(err, file, fileList) {
                Message.error('上传失败');
            },
            basicBankInfoRemove(file, fileList) {
                this.additionalAttachments.basicBankInfo = fileList;
            },

            legalPersonIdCardFrontUploadSuccess(res, file, fileList) {
                if (res.status == 200 && res.data.code == 0) {
                    Message.success('上传成功');
                    file.uri = res.data.data;
                    file.url = cdnHost + res.data.data;
                    this.additionalAttachments.legalPersonIdCardFront = [file];
                } else {
                    Message.error('上传失败');
                }
            },
            legalPersonIdCardFrontUploadError(err, file, fileList) {
                Message.error('上传失败');
            },
            legalPersonIdCardFrontRemove(file, fileList) {
                this.additionalAttachments.legalPersonIdCardFront = fileList;
            },

            legalPersonIdCardBackUploadSuccess(res, file, fileList) {
                if (res.status == 200 && res.data.code == 0) {
                    Message.success('上传成功');
                    file.uri = res.data.data;
                    file.url = cdnHost + res.data.data;
                    this.additionalAttachments.legalPersonIdCardBack = [file];
                } else {
                    Message.error('上传失败');
                }
            },
            legalPersonIdCardBackUploadError(err, file, fileList) {
                Message.error('上传失败');
            },
            legalPersonIdCardBackRemove(file, fileList) {
                this.additionalAttachments.legalPersonIdCardBack = fileList;
            },

            saveAdditionalAttachments() {
                saveAdditionalAttachments({
                    businessLicence: this.additionalAttachments.businessLicenceList.map(item => item.uri),
                    socialSecurity: this.additionalAttachments.socialSecurityList.map(item => item.uri),
                    doorPic: this.additionalAttachments.doorPicList.map(item => item.uri),
                    officePic: this.additionalAttachments.officePicList.map(item => item.uri),
                    workingPic: this.additionalAttachments.workingPicList.map(item => item.uri),
                    basicBankInfo: this.additionalAttachments.basicBankInfo.map(item => item.uri),
                    legalPersonIdCardFront: this.additionalAttachments.legalPersonIdCardFront.map(item => item.uri),
                    legalPersonIdCardBack: this.additionalAttachments.legalPersonIdCardBack.map(item => item.uri),
                    onboardingId: this.onboardingId,
                    savingType: 4
                }).then((res) => {
                    if (res.data.code == 0) {
                        Message.success('保存照片和材料成功')
                        this.getInfo();
                        this.additionalAttachments = {
                            businessLicenceList: [],
                            socialSecurityList: [],
                            doorPicList: [],
                            officePicList: [],
                            workingPicList: [],
                            basicBankInfo: [],
                            legalPersonIdCardFront: [],
                            legalPersonIdCardBack: []
                        }
                        this.additionalAttachmentsDialogVisible = false;
                    }
                });
            },
            deliverContractUploadSuccess(res, file, fileList) {
                if (res.status == 200 && res.data.code == 0) {
                    Message.success('上传成功');
                    file.uri = res.data.data;
                    file.url = cdnHost + res.data.data;
                    this.contract.deliverFiles = [...this.contract.deliverFiles, file];
                } else {
                    Message.error('上传失败');
                }
            },
            deliverContractUploadError(err, file, fileList) {
                this.contract.deliverFiles = fileList;
                Message.error('上传失败');
            },
            deliverContractRemove(file, fileList) {
                this.contract.deliverFiles = fileList;
            },

            changeDeliverName() {
                this.$refs.contractForm.validateField('deliverName');
            },
            changeDeliverNo() {
                this.$refs.contractForm.validateField('deliverNo');
            },
            finalContractUploadSuccess(res, file, fileList) {
                if (res.status == 200 && res.data.code == 0) {
                    Message.success('上传成功');
                    file.uri = res.data.data;
                    file.url = cdnHost + res.data.data;
                    this.contract.finalFiles = [...this.contract.finalFiles, file];
                } else {
                    Message.error('上传失败');
                }
            },
            finalContractUploadError(err, file, fileList) {
                this.contract.finalFiles = fileList;
                Message.error('上传失败');
            },
            finalContractRemove(file, fileList) {
                this.contract.finalFiles = fileList;
            },
            recheckAttachmentUploadSuccess(res, file, fileList) {
                if (res.status == 200 && res.data.code == 0) {
                    Message.success('上传成功');
                    file.uri = res.data.data;
                    file.url = cdnHost + res.data.data;
                    this.recheckData.recheckAttachment = [file];
                } else {
                    Message.error('上传失败');
                }
            },
            recheckAttachmentUploadError(err, file, fileList) {
                this.recheckData.recheckAttachment = []
            },
            recheckAttachmentRemove(file, fileList) {
                this.recheckData.recheckAttachment = fileList
            },
            beforeRecheckDialogClose() {
                this.recheckData = {
                    recheckComment: '',
                    recheckAttachment: []
                }
                this.showApproveRecheck = false
            },
            saveContractInfo() {
                this.$refs.contractForm.validate((valid) => {
                    if (!valid) {
                        return false;
                    }
                    let deliverContractUrls = this.contract.deliverFiles.map(file => file.uri)
                    let finalContractUrls = this.contract.finalFiles.map(file => file.uri)
                    if(deliverContractUrls.length === 0 && finalContractUrls.length === 0) {
                        Message.error('请选择要上传的合同');
                        return;
                    }
                    if((this.contract.deliverName != undefined && this.contract.deliverName.trim() != '') && (this.contract.deliverNo === undefined || this.contract.deliverNo.trim() === '')) {
                        Message.error('请填写快递单号');
                        return;
                    }
                    saveContractInfo({
                        onboardingId: this.onboarding.id,
                        deliverContractUrls: deliverContractUrls,
                        deliverName: this.contract.deliverName,
                        deliverNo: this.contract.deliverNo,
                        finalContractUrls: finalContractUrls,
                        ignoreFinalContract: false
                    }).then((res) => {
                        if (res.data.code === 0) {
                            Message.success('保存成功');
                            this.getInfo();
                            this.contract.deliverFiles = [];
                            this.contract.finalFiles = [];
                            this.contractDialogVisible = false;
                            this.additionalContractDialogVisible = false;
                        }
                    });
                });
            },
            ignoreContract() {
                this.$confirm('确定要跳过吗？', '提示', {
                    type: 'warning'
                }).then(() => {
                    saveContractInfo({
                        onboardingId: this.onboarding.id,
                        deliverContractUrls: [],
                        deliverName: "",
                        deliverNo: "",
                        finalContractUrls: [],
                        ignoreFinalContract: true
                    }).then((res) => {
                        if (res.data.code === 0) {
                            Message.success('已跳过');
                            this.getInfo();
                            this.contract.deliverFiles = [];
                            this.contract.finalFiles = [];
                            this.contractDialogVisible = false;
                            this.additionalContractDialogVisible = false;
                        }
                    });
                });
            },
            saveExtraContracts() {
                if(this.contract.deliverFiles.length === 0 && this.contract.finalFiles.length === 0) {
                    Message.error('请选择要上传的合同');
                    return;
                }
                if(this.contract.deliverFiles.length > 0 && this.contract.finalFiles.length > 0) {
                    Message.error('一次只能上传同一种类型的合同');
                    return;
                }
                if((this.contract.deliverName != undefined && this.contract.deliverName.trim() != '') && (this.contract.deliverNo === undefined || this.contract.deliverNo.trim() === '')) {
                    Message.error('请填写快递单号');
                    return;
                }
                saveContractInfo({
                    onboardingId: this.onboarding.id,
                    deliverContractUrls: this.contract.deliverFiles.map(file => file.uri),
                    finalContractUrls: this.contract.finalFiles.map(file => file.uri),
                    deliverName: this.contract.deliverName,
                    deliverNo: this.contract.deliverNo,
                    ignoreFinalContract: false
                }).then((res) => {
                    if (res.data.code === 0) {
                        Message.success('保存成功');
                        this.getInfo();
                        this.contract = {
                            deliverFiles: [],
                            finalFiles: [],
                            deliverName: '',
                            deliverNo: ''
                        }
                        this.contractDialogVisible = false;
                        this.additionalContractDialogVisible = false;
                    }
                });
            },
            getInfo() {
                getOnboardingInfo(this.onboardingId).then((res) => {
                    if (res.data.code == 0) {
                        if (res.data.data) {
                            this.preAttachments = [];
                            this.contractAttachments = [];
                            this.enterpriseExist = res.data.data.enterpriseExist;
                            this.onboarding = res.data.data.onboarding;
                            this.privileges = res.data.data.privileges;
                            if(res.data.data.onboardingInfoBasic) {
                                this.basicInfo = res.data.data.onboardingInfoBasic;
                            } else {
                                this.basicInfo.enterpriseName = this.onboarding.enterpriseName;
                            }
                            res.data.data.onboardingInfoBusiness ? this.businessInfo = res.data.data.onboardingInfoBusiness : false;
                            if(res.data.data.onboardingInvoiceCategories) {
                                let invoiceCategoryIds = []
                                res.data.data.onboardingInvoiceCategories.map(onboardingInvoiceCategory => {
                                    invoiceCategoryIds.push(onboardingInvoiceCategory.invoiceCategoryId)
                                })
                                this.businessInfo.invoiceCategoryIds = invoiceCategoryIds
                                console.log(this.businessInfo.invoiceCategoryIds)
                            }
                            if(res.data.data.onboardingInfoContacts.length > 0) {
                                this.contactList = res.data.data.onboardingInfoContacts;
                            }
                            if(res.data.data.onboardingInfoAttachments) {
                                this.attachmentTips = {
                                    1: '缺少营业执照扫描件',
                                    2: '缺少社保证明/完税证明/上下游发票',
                                    3: '缺少企业门头照',
                                    4: '缺少办公室场景照',
                                    5: '缺少用工场景照',
                                    6: '缺少待签约/邮寄合同',
                                    7: '缺少已签约合同',
                                    9: '缺少银行基本户信息照',
                                    10: '缺少法人身份证正面',
                                    11: '缺少法人身份证背面'
                                }
                                this.attachments = res.data.data.onboardingInfoAttachments
                                this.attachments.forEach(attachment => {
                                    attachment.name = this.attachmentTypes[attachment.attachmentType];
                                    if(attachment.attachmentUrl.indexOf('http') === -1) {
                                        attachment.attachmentUrl = cdnHost + attachment.attachmentUrl;
                                    }
                                    if(this.preAttachmentTypes.includes(attachment.attachmentType)) {
                                        this.preAttachments.push(attachment);
                                    } else if(this.contractAttachmentTypes.includes(attachment.attachmentType)) {
                                        this.contractAttachments.push(attachment);
                                    }
                                    // if attachment.attachmentType is key of this.attachmentTips, remove it from this.attachmentTips
                                    if(this.attachmentTips[attachment.attachmentType]) {
                                        delete this.attachmentTips[attachment.attachmentType];
                                        if(attachment.attachmentType === 7) {
                                            try {
                                                delete this.attachmentTips[6];
                                            } catch(e) {}
                                        }
                                    }
                                })
                                // sort this.preAttachments by attachmentType
                                this.preAttachments.sort((a, b) => a.attachmentType - b.attachmentType);
                                // sort this.contractAttachments by attachmentType
                                this.contractAttachments.sort((a, b) => a.attachmentType - b.attachmentType);
                            }
                            if(res.data.data.onboardingTaxes) {
                                this.taxes = res.data.data.onboardingTaxes.map(tax => tax.taxId);
                            }
                            if(res.data.data.onboardingEvents) {
                                this.eventRemarks = [];
                                this.onboardingEventList = res.data.data.onboardingEvents.reverse()
                                this.onboardingEventList.forEach(event => {
                                    if(this.onboarding.status > 4 && event.eventType == 5 && event.remark != null && event.remark != '') {
                                        this.eventRemarks.push(event.remark)
                                    }
                                })
                            }
                        }
                    }
                });
            },
            openAccount(onboardingId, enterpriseName) {
                this.$confirm(`确定要为 "${enterpriseName}" 创建企业账户吗？`, '提示', {
                    type: 'warning'
                }).then(() => {
                    openAccount(onboardingId).then((res) => {
                        if (res.data.code === 0) {
                            Message.success('系统操作开户中');
                            this.getInfo();
                        }
                    });
                });
            },
            showExpressDig(id) {
                this.showExpress = true;
                this.expressInfo.attachmentId = id;
                this.expressInfo.onboardingId = this.onboardingId;
                this.getExpressInfo(id);
            },
            hideExpressDig() {
                this.showExpress = false;
                this.expressInfo = {
                    id: '',
                    onboardingId: this.onboardingId,
                    attachmentId: '',
                    deliveryName: '',
                    deliveryNo: ''
                }
            },
            saveExpressInfo() {
                this.$refs.expressInfoForm.validate((valid) => {
                    if (!valid) {
                        return false;
                    }
                    if(!this.expressInfo.attachmentId) {
                        Message.error('未找到对应的附件');
                        return;
                    }
                    saveDeliveryInfo(this.expressInfo).then((res) => {
                        if (res.data.code === 0) {
                            Message.success('保存成功');
                            this.hideExpressDig();
                        } else {
                            Message.error(res.date.msg);
                        }
                    });
                });

            },
            getExpressInfo(attachmentId) {
                getDeliveryInfo(this.onboardingId, attachmentId).then((res) => {
                    if (res.data.code == 0) {
                        if (res.data.data) {
                            this.expressInfo = res.data.data;
                        }
                    }
                });
            },
            additionalAttachmentsDialogClose() {
                this.additionalAttachments = {
                    businessLicenceList: [],
                    socialSecurityList: [],
                    doorPicList: [],
                    officePicList: [],
                    workingPicList: [],
                    basicBankInfo: [],
                    legalPersonIdCardFront: [],
                    legalPersonIdCardBack: []
                }
                this.contract = {
                    deliverFiles: [],
                    finalFiles: [],
                    deliverName: '',
                    deliverNo: ''
                }
            },
            changeFastCommonReply(val) {
                console.log(val)
                this.reviewComment += val;
            },
            openAttachment(url) {
                window.open(url, '_blank');
            },
            changeAttachmentValid(id, valid) {
                this.$confirm('确认修改附件有效性?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消', 
                    type: 'warning'
                }).then(() => {
                let params = {
                    onboardingId: this.onboardingId,
                    attachmentId: id,
                    valid: valid
                }
                updateAttachmentValid(params).then((res) => {
                    if (res.data.code === 0) {
                        Message.success('操作成功');
                            this.getInfo();
                        }
                    }); 
                });
            }
        },
        created() {
            this.fromList = this.$route.query.from_list;
            this.$route.query.id ? this.onboardingId = this.$route.query.id : ''
            categoryListAll().then((res) => {
                if (res.data.code == 0) {
                    if (res.data.data) {
                        res.data.data.forEach((element) => {
                            element.nameStr = element.parentName + '——' + element.name;
                        });
                        this.invoiceCategoryList = res.data.data;
                    }
                }
            });
            listTaxesByType({
                id: null,
                type: 2
            }).then((res) => {
                if (res.data.code == 0) {
                    this.taxesList = res.data.data;
                }
            });
            getChannelList().then((res) => {
                if (res.data.code == 0) {
                    this.channelList = res.data.data;
                }
            });
            this.getInfo();
        },
        watch: {
            contractDialogVisible(val) {
                if(val) {
                    this.contract = {
                        deliverFiles: [],
                        finalFiles: [],
                        deliverName: '',
                        deliverNo: ''
                    }
                }
            },
            showCloseOnboarding(val) {
                if(val) {
                    this.reviewComment = '';
                }
            }
        }
    }
</script>

<style scoped>
    .el-tag.el-tag--success {
        background-color: #ecf5ff;
        border-color: #d9ecff;
        color: #409EFF;
    }
</style>
