<template>
    <div>
        <el-card style="margin:20px; padding-bottom:20px" shadow="never">
            <el-row>
                <el-col style="text-align: left" :span="12">
                    <span style="font-size: 24px; color: #333; margin-bottom: 20px"> 进件申请 </span>
                </el-col>
                <el-col style="text-align: right; margin-right: 0" :span="12">
                    <el-tooltip class="item" effect="dark" content="点击复制进件单号" placement="top-start">
                        <span class="tipCopiable" title="通过进件号邀请管理员协助入录进件信息" @click="copyToClipboard(onboarding.onboardingNo)">
                            <img src="@/assets/css/bs-icons/info-circle.svg" style="color: #888; height: 12px" />
                            进件号：{{ onboarding.onboardingNo }}
                            <img src="@/assets/css/bs-icons/copy.svg" style="color: #888; height: 12px" />
                        </span>
                    </el-tooltip>
                    <el-button type="primary" @click="submit" alt="提交申请" size="medium">点击提交申请</el-button>
                </el-col>
            </el-row>
            <el-steps :active="step-1" finish-status="success" simple style="margin: 20px;">
                <el-step title="基本信息" ></el-step>
                <el-step title="业务信息" ></el-step>
                <el-step title="联系人信息" ></el-step>
                <el-step title="工商材料及实地照片" ></el-step>
            </el-steps>
            <el-row :gutter="20">
                <el-col :span="17" style="margin-left: 20px;">
                    <el-card shadow="never">
                        <div slot="header" class="clearfix">
                            <span v-if="step==1">基本信息</span>
                            <span v-if="step==2">业务信息</span>
                            <span v-if="step==3">联系人信息</span>
                            <span v-if="step==4">工商材料及实地照片</span>
                        </div>
                        <el-form ref="basicInfoForm" :model="basicInfo" :rules="basicInfoRules" label-width="120px">
                            <div v-if="step==1">
                                <el-form-item prop="id">
                                    <el-input v-model="basicInfo.id" type="hidden"></el-input>
                                </el-form-item>
                                <el-form-item label="企业名称" prop="enterpriseName">
                                    <el-input v-model.trim="basicInfo.enterpriseName" @input="changeBasicInfo" placeholder="请输入企业名称" maxlength="50" show-word-limit></el-input>
                                </el-form-item>
                                <el-form-item label="企业简称" prop="enterpriseAbbreviation">
                                    <el-input v-model.trim="basicInfo.enterpriseAbbreviation" @input="changeBasicInfo" placeholder="请输入企业简称" maxlength="30" show-word-limit></el-input>
                                </el-form-item>
                                <el-form-item label="所属渠道" prop="channel">
                                    <span>{{ channelName }} <i class="el-icon-edit" @click="dialogVisible = true" style="color: #409EFF; cursor: pointer; margin-left:10px"></i></span>
                                </el-form-item>
                                <el-form-item label="所属税地" prop="taxes">
                                    <span>{{ taxesNames }} <i class="el-icon-edit" @click="dialogVisible = true" style="color: #409EFF; cursor: pointer; margin-left:10px"></i></span>
                                </el-form-item>
                                <el-form-item label="纳税人识别号" prop="taxpayerIdentification">
                                    <el-input v-model.trim="basicInfo.taxpayerIdentification" @input="changeBasicInfo" placeholder="请输入纳税人识别号"></el-input>
                                </el-form-item>
                                <el-form-item label="纳税人资质" prop="taxpayerType">
                                    <el-select v-model="basicInfo.taxpayerType" placeholder="请选择纳税人资质" @change="changeBasicInfo">
                                        <el-option label="小规模纳税人" :value="1"></el-option>
                                        <el-option label="一般纳税人" :value="2"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="注册地址" prop="registeredAddress">
                                    <el-input v-model.trim="basicInfo.registeredAddress" @input="changeBasicInfo" placeholder="请输入注册地址"></el-input>
                                </el-form-item>
                                <el-form-item label="注册电话" prop="registeredPhone">
                                    <el-input v-model.trim="basicInfo.registeredPhone" @input="changeBasicInfo" placeholder="请输入注册电话"></el-input>
                                </el-form-item>
                                <el-form-item label="企业邮箱" prop="email">
                                    <el-input v-model.trim="basicInfo.email" @input="changeBasicInfo" placeholder="请输入企业邮箱"></el-input>
                                </el-form-item>
                                <el-form-item label="企业法人" prop="legalPersonName">
                                    <el-input v-model.trim="basicInfo.legalPersonName" @input="changeBasicInfo" placeholder="请输入法人姓名"></el-input>
                                </el-form-item>
                                <el-form-item label="企业银行账号" prop="bankAccount">
                                    <el-input v-model.trim="basicInfo.bankAccount" @input="changeBasicInfo" placeholder="请输入企业银行账号"></el-input>
                                </el-form-item>
                                <el-form-item label="企业开户银行" prop="bankName">
                                    <el-input v-model.trim="basicInfo.bankName" @input="changeBasicInfo" placeholder="请输入企业开户银行"></el-input>
                                </el-form-item>
                                <el-form-item label="企业主营业务" prop="mainBusiness">
                                    <el-input type="textarea" rows="5" @input="changeBasicInfo" v-model="basicInfo.mainBusiness" placeholder="请输入企业主营业务" maxlength="500" show-word-limit></el-input>
                                </el-form-item>
                            </div>
                        </el-form>

                        <el-form ref="businessInfoForm" :model="businessInfo" :rules="businessInfoRules" label-width="150px">
                            <div v-if="step==2">
                                <el-form-item prop="id">
                                    <el-input v-model="businessInfo.id" type="hidden"></el-input>
                                </el-form-item>
                                <el-form-item label="发票类目" prop="invoiceInfo">
                                    <el-input type="textarea" rows="5" @input="changeBusinessInfo" v-model="businessInfo.invoiceInfo" placeholder="请输入发票类目" maxlength="500" show-word-limit></el-input>
                                </el-form-item>
                                <el-form-item label="C端用工场景描述" prop="employmentScene">
                                    <el-input type="textarea" rows="5" @input="changeBusinessInfo" v-model="businessInfo.employmentScene" placeholder="请输入C端用工场景描述" maxlength="350" show-word-limit></el-input>
                                    <el-alert type="success" title="C端用工场景描述示例" :closable="false">
                                        <div>示例: 需要灵工人员利用空闲时间为[XX项目名称/XX业务内容/XX系统]等，通过线上/线下的XX方式，提供具体的XXXX等服务，按时完成相应的任务，交付[符合公司要求的XXX成果]，验收完成后，根据XXX规则结算服务费。</div>
                                    </el-alert>
                                </el-form-item>
                                <el-form-item label="发放体量（月度）" prop="monthlyAmount">
                                    <el-input type="number" placeholder="请输入预估的每月发放体量" @input="changeBusinessInfo" v-model.number="businessInfo.monthlyAmount"></el-input>
                                </el-form-item>
                                <el-form-item label="服务费结算规则" prop="salarySettleRule">
                                    <el-input type="textarea" rows="5" placeholder="请输入服务费结算规则" v-model="businessInfo.salarySettleRule" @input="changeBusinessInfo" maxlength="500" show-word-limit></el-input>
                                    <el-alert type="success" title="服务费结算规则示例" :closable="false">
                                        <div>示例1: 市场推广500-80000/天</div>
                                        <div>示例2: 根据下单金额5%-20%的提成不等</div>
                                        <div>示例3: 按博主粉丝量不一样，从50元-5000元费用一篇笔记</div>
                                    </el-alert>
                                </el-form-item>
                                <el-form-item label="结算费率" prop="settleRate">
                                    <el-input type="number" placeholder="请输入结算费率" v-model.number="businessInfo.settleRate" @input="changeBusinessInfo">
                                        <template slot="append">%</template>
                                    </el-input>
                                </el-form-item>
                            </div>
                        </el-form>

                        <el-form ref="contactInfoForm" :model="contactInfo" label-width="120px">
                            <el-card v-if="step==3" style="margin-bottom: 20px;" shadow="never" v-for="(contact, index) in contactInfo.list">
                                <div slot="header" class="clearfix">
                                    <span>联系人{{ index + 1 }}</span>
                                </div>
                                <div>
                                    <el-form-item :prop="'list.' + index + '.id'">
                                        <el-input v-model="contact.id" type="hidden"></el-input>
                                    </el-form-item>
                                    <el-form-item label="姓名" :prop="'list.' + index + '.name'" :rules="[{ required: true, message: '请填写姓名', trigger: 'blur' }]">
                                        <el-input placeholder="请输入姓名" v-model.trim="contact.name" @input="changeContactInfo"></el-input>
                                    </el-form-item>
                                    <el-form-item label="联系电话" :prop="'list.' + index + '.phone'" :rules="[{ required: true, message: '请填写联系电话', trigger: 'blur' }, {pattern: /^(\+?\d{0,3}-?)?([1-9*][0-9*]{0,2}[0-9*]{8}|(\d{3,4}-?)?[0-9*]{7,8})$/, message: '电话号码格式不正确', trigger: 'blur' }]">
                                        <el-input placeholder="请输入联系电话" v-model.trim="contact.phone" @input="changeContactInfo"></el-input>
                                    </el-form-item>
                                    <el-form-item label="邮箱" :prop="'list.' + index + '.email'" :rules="[{type: 'email', message: '邮箱格式不正确', trigger: 'blur'}]">
                                        <el-input placeholder="请输入邮箱" v-model.trim="contact.email" @input="changeContactInfo"></el-input>
                                    </el-form-item>
                                    <el-form-item label="收件地址" :prop="'list.' + index + '.address'" :rules="[{ required: true, message: '请填写收件地址', trigger: 'blur' }]">
                                        <el-input placeholder="请输入收件地址" v-model.trim="contact.address" @input="changeContactInfo"></el-input>
                                    </el-form-item>
                                    <el-form-item label="职务" :prop="'list.' + index + '.jobTitle'">
                                        <el-input placeholder="请输入职务" v-model.trim="contact.jobTitle" @input="changeContactInfo"></el-input>
                                    </el-form-item>
                                    <el-input v-model="contact.type" type="hidden"></el-input>
                                </div>
                                <el-row v-if='contact.type !== 4'>
                                    <el-col style="text-align: left;">
                                        <el-button type="primary" @click="deleteContact(index)" size="medium">删除</el-button>
                                    </el-col>
                                </el-row>
                            </el-card>
                        </el-form>

                        <el-form ref="attachmentForm" :model="attachments" :rules="attachmentRules" label-width="300px">
                            <div v-if="step==4">
                                <el-form-item label="营业执照扫描件" prop="businessLicenceList" :rules="[{ required: true, message: '请上传营业执照扫描件', trigger: 'blur' }]">
                                    <el-upload
                                        class="file-upload"
                                        :class="{ 'hide-upload': attachments.businessLicenceList.length > 0 }"
                                        ref="businessLicenceUploader"
                                        action=""
                                        :limit="1"
                                        :http-request="uploadFiles"
                                        :before-upload="beforeUpload"
                                        :on-success="businessLicenceUploadSuccess"
                                        :on-error="businessLicenceUploadError"
                                        :on-remove="businessLicenceeRemove"
                                        :on-cancel="businessLicenceeRemove"
                                        :file-list="attachments.businessLicenceList"
                                        :on-exceed="businessLicenceExceed"
                                        accept=".jpg,.jpeg,.png,.pdf,.zip,.rar"
                                        list-type="picture-card">
                                        <i class="el-icon-plus avatar-uploader-icon"></i>
                                        <div slot="tip" class="el-upload__tip">只能上传jpg、png、pdf、zip和rar文件，且不超过10MB</div>
                                    </el-upload>
                                    <el-alert type="success" title="营业执照照片要求" :closable="false">
                                        <div>照片清晰且内容完整，要确保营业执照的四个角以及边缘都在照片内，不能有缺失的部分，不得含有其他业务水印批注， 照片必须是彩色原件，如果是复印件，需要加盖公章以证明其与原件一致。</div>
                                    </el-alert>
                                </el-form-item>
                                <el-form-item label="社保证明、完税证明或上下游发票" prop="socialSecurityList">
                                    <el-upload
                                        class="file-upload"
                                        :class="{ 'hide-upload': attachments.socialSecurityList.length > 4 }"
                                        action=""
                                        :limit="5"
                                        :http-request="uploadFiles"
                                        :before-upload="beforeUpload"
                                        :on-success="socialSecurityUploadSuccess"
                                        :on-error="socialSecurityUploadError"
                                        :on-remove="socialSecurityRemove"
                                        :on-cancel="socialSecurityRemove"
                                        :file-list="attachments.socialSecurityList"
                                        accept=".jpg,.jpeg,.png,.pdf,.zip,.rar"
                                        list-type="picture-card">
                                        <i class="el-icon-plus avatar-uploader-icon"></i>
                                        <div slot="tip" class="el-upload__tip">只能上传jpg、png、pdf、zip和rar文件，且不超过10MB</div>
                                    </el-upload>
                                </el-form-item>
                                <el-form-item label="企业门头照" prop="doorPicList" :rules="[{ required: true, message: '请上传企业门头照', trigger: 'blur' }]">
                                    <el-upload
                                        class="file-upload"
                                        :class="{ 'hide-upload': attachments.doorPicList.length > 4 }"
                                        action=""
                                        :limit="5"
                                        :http-request="uploadFiles"
                                        :before-upload="beforeUpload"
                                        :on-success="doorPicUploadSuccess"
                                        :on-error="doorPicUploadError"
                                        :on-remove="doorPicRemove"
                                        :on-cancel="doorPicRemove"
                                        :file-list="attachments.doorPicList"
                                        accept=".jpg,.jpeg,.png,.pdf,.zip,.rar"
                                        list-type="picture-card">
                                        <i class="el-icon-plus avatar-uploader-icon"></i>
                                        <div slot="tip" class="el-upload__tip">只能上传jpg、png、pdf、zip和rar文件，且不超过10MB</div>
                                    </el-upload>
                                    <el-tooltip effect="dark" placement="top-start">
                                        <template slot="content">
                                            <a href="https://cdn.qiyixin.net.cn/businessLicenseUrl/20240925/1727250707429_808851da.jpg" target="_blank" rel="noopener noreferrer">
                                                <img src="https://cdn.qiyixin.net.cn/businessLicenseUrl/20240925/1727250707429_808851da.jpg" style="max-width: 700px; max-height: 700px; cursor: pointer;" />
                                            </a>
                                        </template>
                                        <el-alert type="success" title="门头照照片要求" :closable="false">
                                            <div>照片必须清晰，须包含完整的公司名称和门头外观，不能只拍摄部分门头或只拍摄部分公司名称（适当距离拍摄）</div>
                                        </el-alert>
                                    </el-tooltip>
                                </el-form-item>
                                <el-form-item label="办公室场景照" prop="officePicList" :rules="[{ required: true, message: '请上传办公室场景照', trigger: 'blur' }]">
                                    <el-upload
                                        class="file-upload"
                                        :class="{ 'hide-upload': attachments.officePicList.length > 4 }"
                                        action=""
                                        :limit="5"
                                        :http-request="uploadFiles"
                                        :before-upload="beforeUpload"
                                        :on-success="officePicUploadSuccess"
                                        :on-error="officePicUploadError"
                                        :on-remove="officePicRemove"
                                        :on-cancel="officePicRemove"
                                        :file-list="attachments.officePicList"
                                        accept=".jpg,.jpeg,.png,.pdf,.zip,.rar"
                                        list-type="picture-card">
                                        <i class="el-icon-plus avatar-uploader-icon"></i>
                                        <div slot="tip" class="el-upload__tip">只能上传jpg、png、pdf、zip和rar文件，且不超过10MB</div>
                                    </el-upload>
                                </el-form-item>
                                <el-form-item label="C端用工场景照" prop="workingPicList" :rules="[{ required: true, message: '请上传C端用工场景照', trigger: 'blur' }]">
                                    <el-upload
                                        class="file-upload"
                                        :class="{ 'hide-upload': attachments.workingPicList.length > 4 }"
                                        action=""
                                        :limit="5"
                                        :http-request="uploadFiles"
                                        :before-upload="beforeUpload"
                                        :on-success="workingPicUploadSuccess"
                                        :on-error="workingPicUploadError"
                                        :on-remove="workingPicRemove"
                                        :on-cancel="workingPicRemove"
                                        :file-list="attachments.workingPicList"
                                        accept=".jpg,.jpeg,.png,.pdf,.zip,.rar"
                                        list-type="picture-card">
                                        <i class="el-icon-plus avatar-uploader-icon"></i>
                                        <div slot="tip" class="el-upload__tip">只能上传jpg、png、pdf、zip和rar文件，且不超过10MB</div>
                                    </el-upload>
                                </el-form-item>
                                <el-form-item label="基本存款账户信息照片" prop="basicBankInfo" :rules="[{required: true, message: '请上传基本存款账户信息照片', trigger: 'blur'}]">
                                    <el-upload
                                        class="file-upload"
                                        :class="{ 'hide-upload': attachments.basicBankInfo.length > 0 }"
                                        action=""
                                        :limit="1"
                                        :http-request="uploadFiles"
                                        :before-upload="beforeUpload"
                                        :on-success="basicBankInfoUploadSuccess"
                                        :on-error="basicBankInfoUploadError"
                                        :on-remove="basicBankInfoRemove"
                                        :on-cancel="basicBankInfoRemove"
                                        :file-list="attachments.basicBankInfo"
                                        accept=".jpg,.jpeg,.png,.pdf,.zip,.rar"
                                        list-type="picture-card">
                                        <i class="el-icon-plus avatar-uploader-icon"></i>
                                        <div slot="tip" class="el-upload__tip">只能上传jpg、png、pdf、zip和rar文件，且不超过10MB</div>
                                    </el-upload>
                                </el-form-item>
                                <el-form-item label="法人身份证正面照" prop="legalPersonIdCardFront" :rules="[{required: true, message: '请上传法人身份证正面照', trigger: 'blur'}]">
                                    <el-upload
                                        class="file-upload"
                                        :class="{ 'hide-upload': attachments.legalPersonIdCardFront.length > 0 }"
                                        action=""
                                        :limit="1"
                                        :http-request="uploadFiles"
                                        :before-upload="beforeUpload"
                                        :on-success="legalPersonIdCardFrontUploadSuccess"
                                        :on-error="legalPersonIdCardFrontUploadError"
                                        :on-remove="legalPersonIdCardFrontRemove"
                                        :on-cancel="legalPersonIdCardFrontRemove"
                                        :file-list="attachments.legalPersonIdCardFront"
                                        accept=".jpg,.jpeg,.png,.pdf,.zip,.rar"
                                        list-type="picture-card">
                                        <i class="el-icon-plus avatar-uploader-icon"></i>
                                        <div slot="tip" class="el-upload__tip">只能上传jpg、png、pdf、zip和rar文件，且不超过10MB</div>
                                    </el-upload>
                                </el-form-item>
                                <el-form-item label="法人身份证反面照" prop="legalPersonIdCardBack" :rules="[{required: true, message: '请上传法人身份证反面照', trigger: 'blur'}]">
                                    <el-upload
                                        class="file-upload"
                                        :class="{ 'hide-upload': attachments.legalPersonIdCardBack.length > 0 }"
                                        action=""
                                        :limit="1"
                                        :http-request="uploadFiles"
                                        :before-upload="beforeUpload"
                                        :on-success="legalPersonIdCardBackUploadSuccess"
                                        :on-error="legalPersonIdCardBackUploadError"
                                        :on-remove="legalPersonIdCardBackRemove"
                                        :on-cancel="legalPersonIdCardBackRemove"
                                        :file-list="attachments.legalPersonIdCardBack"
                                        accept=".jpg,.jpeg,.png,.pdf,.zip,.rar"
                                        list-type="picture-card">
                                        <i class="el-icon-plus avatar-uploader-icon"></i>
                                        <div slot="tip" class="el-upload__tip">只能上传jpg、png、pdf、zip和rar文件，且不超过10MB</div>
                                    </el-upload>
                                </el-form-item>
                            </div>
                        </el-form>

                        <el-row>
                            <el-col :span="8" style="text-align: left;display: flex;">
                                <el-button type="primary" @click="save(step)" size="medium">保存</el-button>
                                <el-alert title="修改未保存" v-if="showNeedSave" type="warning" :closable="false" style="margin-left: 10px;"></el-alert>
                            </el-col>
                            <el-col :span="16" style="text-align: right;">
                                <el-button type="primary" @click="addContact" size="medium" v-if="step == 3">新增联系人</el-button>

                                <el-button type="primary" size="medium" v-if="step > 1" @click="prevStep">上一步</el-button>

                                <el-button type="primary" size="medium" v-if="step < 4" @click="nextStep">下一步</el-button>
                            </el-col>
                        </el-row>
                    </el-card>
                </el-col>
                <el-col :span="6" v-if="onboarding.status < 0">
                    <el-card shadow="never" style="color: red;">
                        <div slot="header" class="clearfix">
                            <span>审核意见</span>
                        </div>
                        <div v-for="onboardingEvent in onboardingEventList" style="margin-bottom: 20px">
                            <el-row><h4>{{ onboardingEvent.eventName }} <span style="font-size: 12px;">({{ onboardingEvent.createTime }})</span></h4></el-row>
                            <el-row v-html="onboardingEvent.remark.replace(/\n/g, '<br>')"></el-row>
                        </div>
                    </el-card>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="4" :offset="19" style="margin-top: 10px">
                    <el-alert title="基本信息未保存" v-if="basicInfoChanged" type="error" :closable="false" style="margin-top: 10px;"></el-alert>
                    <el-alert title="业务信息未保存" v-if="businessInfoChanged" type="error" :closable="false" style="margin-top: 10px;"></el-alert>
                    <el-alert title="联系人信息未保存" v-if="contactInfoChanged" type="error" :closable="false" style="margin-top: 10px;"></el-alert>
                    <el-alert title="材料和照片未保存" v-if="attachmentsChanged" type="error" :closable="false" style="margin-top: 10px;"></el-alert>
                </el-col>
            </el-row>
            <el-row style="margin: 20px">
                <el-col style="text-align: right;">
                    <el-button type="primary" @click="saveAll" size="medium">保存全部</el-button>
                    <el-button type="primary" @click="submit" size="medium">点击提交申请</el-button>
                </el-col>
            </el-row>
        </el-card>

        <el-dialog title="修改 渠道与税源地" :visible.sync="dialogVisible">
            <el-form :model="onboarding">
                <el-form-item label="所属渠道" prop="channelId">
                    <el-select v-model="onboarding.channelId" placeholder="请选择渠道">
                        <el-option
                            v-for="item in channelList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="所属税源地" prop="taxes">
                    <el-select v-model="taxes" multiple placeholder="请选择渠道">
                        <el-option
                            v-for="item in taxesList"
                            :key="item.taxesId"
                            :label="item.taxesName"
                            :value="item.taxesId">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="editChannelAndTax">保 存</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import { listTaxesByType, getChannelList } from '@/api/branch/enterprise.js';
    import { getOnboardingInfo, saveOnboarding, applyOnboarding, editOnboarding } from '@/api/onboarding.js'
    import { fileUpload } from '@/api/system/user.js'
    import {Message} from 'element-ui'
    import {cdnHost} from '@/config/env.js'
    export default {
        computed: {
            channelName() {
                if(this.channelList.length > 0 && this.onboarding.channelId) {
                    return this.channelList.find(item => item.id === this.onboarding.channelId).name;
                } else {
                    return '';
                }
            },
            taxesNames() {
                if(this.taxesList.length === 0) {
                    return '';
                }
                return this.taxes.map(taxId => {
                    let info = this.taxesList.find(item => item.taxesId === taxId);
                    return info ? info.taxesName : '';
                }).join(', ');
            }
        },
        data() {
            return {
                dialogVisible: false,
                onboarding: {},
                taxesList: [],
                channelList: [],
                step: 1,
                basicInfo: {
                    enterpriseName: ''
                },
                basicInfoRules: {
                    enterpriseName: [{ required: true, message: '请填写企业名称', trigger: 'blur' }, { max: 50, message: '企业名称最多50个字符', trigger: 'blur' }],
                    taxpayerIdentification: [{ required: true, message: '请填写纳税人识别号', trigger: 'blur' }, { pattern: /^[A-Za-z0-9]{15,20}$/, message: '纳税人识别号格式不正确', trigger: 'blur' }],
                    taxpayerType: [{ required: true, message: '请选择纳税人资质', trigger: 'blur' }],
                    registeredAddress: [{ required: true, message: '请填写注册地址', trigger: 'blur' }],
                    registeredPhone: [
                        { required: true, message: '请填写注册电话', trigger: 'blur' },
                        {
                            pattern: /^(\+?\d{0,3}-?)?([1-9*][0-9*]{0,2}[0-9*]{8}|(\d{3,4}-?)?[0-9*]{7,8})$/,
                            message: '电话号码格式不正确',
                            trigger: 'blur'
                        }
                    ],
                    email: [{ type: 'email', message: '邮箱格式不正确', trigger: 'blur' }],
                    legalPersonName: [{ required: true, message: '请填写企业法人', trigger: 'blur' }],
                    bankAccount: [{ required: true, message: '请填写企业银行账号', trigger: 'blur' }],
                    bankName: [{ required: true, message: '请填写企业开户银行', trigger: 'blur' }],
                    mainBusiness: [{ required: true, message: '请填写企业主营业务', trigger: 'blur' }],
                },
                businessInfo: {
                },
                businessInfoRules: {
                    invoiceInfo: [{ required: true, message: '请填写发票类目', trigger: 'blur' }],
                    employmentScene: [{ required: true, message: '请填写C端用工场景描述', trigger: 'blur' }],
                    salarySettleRule: [{ required: true, message: '请填写服务费结算规则', trigger: 'blur' }],
                    settleRate: [{ required: true, message: '请填写结算费率', trigger: 'blur' }, { type: 'number', message: '结算费率必须为数字', trigger: 'blur' }],
                },
                contactInfo: {
                    onboardingId: '',
                    list: [{ type: 4 }]
                },
                attachments: {
                    businessLicenceList: [],
                    socialSecurityList: [],
                    doorPicList: [],
                    officePicList: [],
                    workingPicList: [],
                    basicBankInfo: [],
                    legalPersonIdCardFront: [],
                    legalPersonIdCardBack: []
                },
                attachmentRules: {
                },
                taxes: [],
                basicInfoChanged: false,
                businessInfoChanged: false,
                contactInfoChanged: false,
                attachmentsChanged: false,
                showNeedSave: false,
                onboardingEventList: []
            }
        },
        props: {
            onboardingId: {
                type: String,
                default: ''
            }
        },
        methods: {
            async copyToClipboard(text) {
                try {
                    await navigator.clipboard.writeText(text);
                    Message.success('复制进件单号成功')
                } catch (err) {
                    console.error('Failed to copy: ', err);
                }
            },
            changeBasicInfo() {
                this.basicInfoChanged = true;
                this.showNeedSaveAlert();
            },
            changeBusinessInfo() {
                this.businessInfoChanged = true;
                this.showNeedSaveAlert();
            },
            changeContactInfo() {
                this.contactInfoChanged = true;
                this.showNeedSaveAlert();
            },
            changeAttachments() {
                this.attachmentsChanged = true;
                this.showNeedSaveAlert();
            },
            showNeedSaveAlert() {
                this.showNeedSave = true;
            },
            hideNeedSave() {
                this.showNeedSave = false;
            },
            prevStep() {
                if (this.step > 1) {
                    this.step--;
                    this.changeStep();
                }
            },
            nextStep() {
                if (this.step < 4) {
                    this.step++;
                    this.changeStep();
                }
            },
            changeStep() {
                this.showNeedSave = false;
                switch(this.step) {
                    case 1:
                        if(this.basicInfoChanged) {
                            this.showNeedSave = true;
                        }
                        break;
                    case 2:
                        if(this.businessInfoChanged) {
                            this.showNeedSave = true;
                        }
                        break;
                    case 3:
                        if(this.contactInfoChanged) {
                            this.showNeedSave = true;
                        }
                        break;
                    case 4:
                        if(this.attachmentsChanged) {
                            this.showNeedSave = true;
                        }
                        break;
                }
            },
            addContact() {
                this.contactInfo.list.push({ type: 5 });
            },
            deleteContact(index) {
                let _this = this
                this.$confirm('确定删除此联系人吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    _this.contactInfoChanged = true;
                    _this.contactInfo.list.splice(index, 1);
                }).catch(() => {

                });
            },
            saveAll() {
                if(this.basicInfoChanged) {
                    this.save(1);
                }
                if(this.businessInfoChanged) {
                    this.save(2);
                }
                if(this.contactInfoChanged) {
                    this.save(3);
                }
                if(this.attachmentsChanged) {
                    this.save(4);
                }
            },
            save(step) {
                console.log("step = ", step);
                let _this = this
                switch (step) {
                    case 1:
                        this.$refs.basicInfoForm.validate((valid) => {
                            if (valid) {
                                // FIXME: 做联系电话和邮箱地址的校验
                                saveOnboarding({
                                    ...this.basicInfo,
                                    onboardingId: this.onboardingId,
                                    savingType: 1
                                }).then((res) => {
                                    if (res.data.code == 0) {
                                        _this.basicInfoChanged = false;
                                        _this.hideNeedSave()
                                        Message.success('基本信息保存成功')
                                    }
                                });
                            } else {
                                Message.success('基本信息不完善，保存失败，请检查');
                                return false;
                            }
                        });
                        break;
                    case 2:
                        this.$refs.businessInfoForm.validate((valid) => {
                            if (valid) {
                                if(this.businessInfo.settleRate >= 100) {
                                    Message.error('结算费率应小于100%');
                                    return false;
                                }
                                saveOnboarding({
                                    ...this.businessInfo,
                                    onboardingId: this.onboardingId,
                                    savingType: 2
                                }).then((res) => {
                                    if (res.data.code == 0) {
                                        _this.hideNeedSave()
                                        _this.businessInfoChanged = false;
                                        Message.success('业务信息保存成功')
                                    }
                                });
                            } else {
                                Message.success('业务信息不完善，保存失败，请检查');
                                return false;
                            }
                        });
                        break;
                    case 3:
                        this.$refs.contactInfoForm.validate((valid) => {
                            if (valid) {
                                // FIXME: 做联系电话和邮箱地址的校验
                                saveOnboarding({
                                    contacts: this.contactInfo.list,
                                    onboardingId: this.onboardingId,
                                    savingType: 3
                                }).then((res) => {
                                    if (res.data.code == 0) {
                                        _this.hideNeedSave()
                                        _this.contactInfoChanged = false;
                                        Message.success('保存联系信息成功')
                                    }
                                });
                            } else {
                                Message.success('联系信息不完善，保存失败，请检查');
                                return false;
                            }
                        });
                        break;
                    case 4:
                        this.$refs.attachmentForm.validate((valid) => {
                            if (valid) {
                                console.log("attachments = ", this.attachments);
                                saveOnboarding({
                                    businessLicence: this.attachments.businessLicenceList.map(item => item.uri),
                                    socialSecurity: this.attachments.socialSecurityList.map(item => item.uri),
                                    doorPic: this.attachments.doorPicList.map(item => item.uri),
                                    officePic: this.attachments.officePicList.map(item => item.uri),
                                    workingPic: this.attachments.workingPicList.map(item => item.uri),
                                    basicBankInfo: this.attachments.basicBankInfo.map(item => item.uri),
                                    legalPersonIdCardFront: this.attachments.legalPersonIdCardFront.map(item => item.uri),
                                    legalPersonIdCardBack: this.attachments.legalPersonIdCardBack.map(item => item.uri),
                                    onboardingId: this.onboardingId,
                                    savingType: 4
                                }).then((res) => {
                                    if (res.data.code == 0) {
                                        _this.hideNeedSave()
                                        _this.attachmentsChanged = false;
                                        Message.success('保存照片和材料成功')
                                    }
                                });
                            } else {
                                Message.success('工商材料及实地照片不完善，保存失败，请检查');
                                return false;
                            }
                        });
                        break;
                }
            },
            editChannelAndTax() {
                editOnboarding({
                    id: this.onboarding.id,
                    channelId: this.onboarding.channelId,
                    taxIds: this.taxes
                }).then((res) => {
                    if (res.data.code == 0) {
                        this.$message.success('修改成功');
                        this.dialogVisible = false;
                    }
                });
            },
            beforeUpload(file) {
                if(file.size > 10 * 1024 * 1024 ) {
                    Message.error('文件大小不能超过10MB');
                    return false;
                }
            },
            uploadFiles(option) {
                const _this = this;
                const file = option.file;
                let formData = new FormData();
                formData.append('file', file);
                formData.append('path', 'onboarding');

                fileUpload(formData).then((res) => {
                    if (res != undefined && res.data.code == 0) {
                        let new_file = {
                            name: file.name,
                            response: res,
                            percentage: 0,
                            raw: file,
                            size: file.size,
                            status: 'success',
                            uid: file.uid,
                            url: res.data.data
                        }
                        option.onSuccess(res, new_file);
                    } else {
                        option.onError(res, file);
                    }
                });
            },

            businessLicenceUploadSuccess(res, file, fileList) {
                console.log("res = ", res);
                console.log("file = ", file);
                console.log("fileList = ", fileList);
                if (res.status == 200 && res.data.code == 0) {
                    Message.success('上传成功');
                    file.uri = res.data.data;
                    file.url = cdnHost + res.data.data;
                    this.attachments.businessLicenceList = [file];
                    this.changeAttachments();
                } else {
                    Message.error('上传失败');
                }
            },
            businessLicenceeRemove(file, fileList) {
                console.log("file = ", file);
                console.log("fileList = ", fileList);
                this.attachments.businessLicenceList = fileList;
                this.changeAttachments();
            },
            businessLicenceExceed(files) {
                this.$refs.businessLicenceUploader.clearFiles();
                this.$refs.businessLicenceUploader.handleStart(files[0]);
                this.uploadFiles({
                    file: files[0],
                    onSuccess: this.businessLicenceUploadSuccess,
                    onError: this.businessLicenceUploadError
                });
            },
            businessLicenceUploadError(err, file, fileList) {

            },

            socialSecurityUploadSuccess(res, file, fileList) {
                if (res.status == 200 && res.data.code == 0) {
                    Message.success('上传成功');
                    file.uri = res.data.data;
                    file.url = cdnHost + res.data.data;
                    this.attachments.socialSecurityList = [...this.attachments.socialSecurityList, file];
                    this.changeAttachments();
                } else {
                    Message.error('上传失败');
                }
            },
            socialSecurityUploadError(err, file, fileList) {
                console.log("err = ", err);
                console.log("file = ", file);
                console.log("fileList = ", fileList);
                Message.error('上传失败');
            },
            socialSecurityRemove(file, fileList) {
                console.log("file = ", file);
                console.log("fileList = ", fileList);
                this.attachments.socialSecurityList = fileList;
                this.changeAttachments();
            },

            doorPicUploadSuccess(res, file, fileList) {
                if (res.status == 200 && res.data.code == 0) {
                    Message.success('上传成功');
                    file.uri = res.data.data;
                    file.url = cdnHost + res.data.data;
                    this.attachments.doorPicList = [...this.attachments.doorPicList, file];
                    this.changeAttachments();
                } else {
                    Message.error('上传失败');
                }
            },
            doorPicUploadError(err, file, fileList) {
                console.log("err = ", err);
                console.log("file = ", file);
                console.log("fileList = ", fileList);
                Message.error('上传失败');
            },
            doorPicRemove(file, fileList) {
                console.log("file = ", file);
                console.log("fileList = ", fileList);
                this.attachments.doorPicList = fileList;
                this.changeAttachments();
            },

            officePicUploadSuccess(res, file, fileList) {
                if (res.status == 200 && res.data.code == 0) {
                    Message.success('上传成功');
                    file.uri = res.data.data;
                    file.url = cdnHost + res.data.data;
                    this.attachments.officePicList = [...this.attachments.officePicList, file];
                    this.changeAttachments();
                } else {
                    Message.error('上传失败');
                }
            },
            officePicUploadError(err, file, fileList) {
                console.log("err = ", err);
                console.log("file = ", file);
                console.log("fileList = ", fileList);
                Message.error('上传失败');
            },
            officePicRemove(file, fileList) {
                console.log("file = ", file);
                console.log("fileList = ", fileList);
                this.attachments.officePicList = fileList;
                this.attachmentsChanged = true;
                this.changeAttachments();
            },

            workingPicUploadSuccess(res, file, fileList) {
                if (res.status == 200 && res.data.code == 0) {
                    Message.success('上传成功');
                    file.uri = res.data.data;
                    file.url = cdnHost + res.data.data;
                    this.attachments.workingPicList = [...this.attachments.workingPicList, file];
                    this.changeAttachments();
                } else {
                    Message.error('上传失败');
                }
            },
            workingPicUploadError(err, file, fileList) {
                console.log("err = ", err);
                console.log("file = ", file);
                console.log("fileList = ", fileList);
                Message.error('上传失败');
            },
            workingPicRemove(file, fileList) {
                console.log("file = ", file);
                console.log("fileList = ", fileList);
                this.attachments.workingPicList = fileList;
                this.changeAttachments();
            },

            basicBankInfoUploadSuccess(res, file, fileList) {
                if (res.status == 200 && res.data.code == 0) {
                    Message.success('上传成功');
                    file.uri = res.data.data;
                    file.url = cdnHost + res.data.data;
                    this.attachments.basicBankInfo = [file];
                    this.changeAttachments();
                } else {
                    Message.error('上传失败');
                }
            },
            basicBankInfoUploadError(err, file, fileList) {
                console.log("err = ", err);
                console.log("file = ", file);
                console.log("fileList = ", fileList);
                Message.error('上传失败');
            },
            basicBankInfoRemove(file, fileList) {
                console.log("file = ", file);
                console.log("fileList = ", fileList);
                this.attachments.basicBankInfo = fileList;
                this.changeAttachments();
            },

            legalPersonIdCardFrontUploadSuccess(res, file, fileList) {
                if (res.status == 200 && res.data.code == 0) {
                    Message.success('上传成功');
                    file.uri = res.data.data;
                    file.url = cdnHost + res.data.data;
                    this.attachments.legalPersonIdCardFront = [file];
                    this.changeAttachments();
                } else {
                    Message.error('上传失败');
                }
            },
            legalPersonIdCardFrontUploadError(err, file, fileList) {
                console.log("err = ", err);
                console.log("file = ", file);
                console.log("fileList = ", fileList);
                Message.error('上传失败');
            },
            legalPersonIdCardFrontRemove(file, fileList) {
                console.log("file = ", file);
                console.log("fileList = ", fileList);
                this.attachments.legalPersonIdCardFront = fileList;
                this.changeAttachments();
            },

            legalPersonIdCardBackUploadSuccess(res, file, fileList) {
                if (res.status == 200 && res.data.code == 0) {
                    Message.success('上传成功');
                    file.uri = res.data.data;
                    file.url = cdnHost + res.data.data;
                    this.attachments.legalPersonIdCardBack = [file];
                    this.changeAttachments();
                } else {
                    Message.error('上传失败');
                }
            },
            legalPersonIdCardBackUploadError(err, file, fileList) {
                console.log("err = ", err);
                console.log("file = ", file);
                console.log("fileList = ", fileList);
                Message.error('上传失败');
            },
            legalPersonIdCardBackRemove(file, fileList) {
                console.log("file = ", file);
                console.log("fileList = ", fileList);
                this.attachments.legalPersonIdCardBack = fileList;
                this.changeAttachments();
            },

            submit() {
                if(this.basicInfoChanged || this.businessInfoChanged || this.contactInfoChanged || this.attachmentsChanged) {
                    this.$alert('您还有编辑的内容未保存，请先保存再提交申请', '保存提示', {
                        confirmButtonText: '确定',
                    });
                    return;
                }
                if(
                    this.basicInfo.enterpriseName.length < 1
                    || this.businessInfo.employmentScene.length < 1
                    || this.contactInfo.list.length < 1
                    || this.attachments.businessLicenceList.length < 1
                    || this.attachments.doorPicList.length < 1
                    || this.attachments.officePicList.length < 1
                    || this.attachments.workingPicList.length < 1
                    || this.attachments.basicBankInfo.length < 1
                    || this.attachments.legalPersonIdCardFront.length < 1
                    || this.attachments.legalPersonIdCardBack.length < 1) {
                    this.$alert('请完善所有信息后再提交申请', '提示', {
                        confirmButtonText: '确定',
                    });
                    return;
                }
                let hasContact = true;
                this.contactInfo.list.forEach(contact => {
                    if(contact.name === undefined || contact.phone === undefined || contact.address === undefined || contact.name.length < 1 || contact.phone.length < 1 || contact.address.length < 1) {
                        hasContact = false
                        return;
                    }
                });
                if(!hasContact) {
                    this.$alert('请完善所有联系人信息后再提交申请', '提示', {
                        confirmButtonText: '确定',
                    });
                    return;
                }
                let _this = this
                applyOnboarding(this.onboardingId).then((res) => {
                    if (res.data.code == 0) {
                        Message.success(res.data.data)
                        _this.$router.push({ path: '/onboarding/list' })
                    } else {
                        Message.error(res.data.data)
                    }
                });
            }
        },
        created() {
            listTaxesByType({
                id: null,
                type: 2
            }).then((res) => {
                if (res.data.code == 0) {
                    this.taxesList = res.data.data;
                }
            });
            getChannelList().then((res) => {
                if (res.data.code == 0) {
                    this.channelList = res.data.data;
                }
            });

            getOnboardingInfo(this.onboardingId).then((res) => {
                if (res.data.code == 0) {
                    if (res.data.data) {
                        this.onboarding = res.data.data.onboarding;
                        if(res.data.data.onboardingInfoBasic) {
                            this.basicInfo = res.data.data.onboardingInfoBasic;
                        } else {
                            this.basicInfo.enterpriseName = this.onboarding.enterpriseName;
                        }
                        if(res.data.data.onboardingTaxes) {
                            this.taxes = res.data.data.onboardingTaxes.map(tax => tax.taxId)
                        }
                        res.data.data.onboardingInfoBusiness ? this.businessInfo = res.data.data.onboardingInfoBusiness : false;
                        this.contactInfo.onboardingId = this.onboardingId;
                        if(res.data.data.onboardingInfoContacts.length > 0) {
                            this.contactInfo.list = res.data.data.onboardingInfoContacts;
                        }
                        if(res.data.data.onboardingInfoAttachments) {
                            let attachments = res.data.data.onboardingInfoAttachments
                            attachments.forEach(attachment => {
                                if(attachment.attachmentUrl.indexOf('http') === -1) {
                                    attachment.uri = attachment.attachmentUrl
                                    attachment.url = cdnHost + attachment.attachmentUrl;
                                } else {
                                    let url = new URL(attachment.attachmentUrl);
                                    attachment.uri = url.pathname.substring(1) + url.search;
                                    attachment.url = attachment.attachmentUrl;
                                }
                                if(attachment.attachmentType == 1) {
                                    this.attachments.businessLicenceList.push({
                                        uri: attachment.uri,
                                        url: attachment.url
                                    })
                                } else if(attachment.attachmentType == 2) {
                                    this.attachments.socialSecurityList.push({
                                        uri: attachment.uri,
                                        url: attachment.url
                                    })
                                } else if(attachment.attachmentType == 3) {
                                    this.attachments.doorPicList.push({
                                        uri: attachment.uri,
                                        url: attachment.url
                                    })
                                } else if(attachment.attachmentType == 4) {
                                    this.attachments.officePicList.push({
                                        uri: attachment.uri,
                                        url: attachment.url
                                    })
                                } else if(attachment.attachmentType == 5) {
                                    this.attachments.workingPicList.push({
                                        uri: attachment.uri,
                                        url: attachment.url
                                    })
                                } else if(attachment.attachmentType == 9) {
                                    this.attachments.basicBankInfo.push({
                                        uri: attachment.uri,
                                        url: attachment.url
                                    })
                                } else if(attachment.attachmentType == 10) {
                                    this.attachments.legalPersonIdCardFront.push({
                                        uri: attachment.uri,
                                        url: attachment.url
                                    })
                                } else if(attachment.attachmentType == 11) {
                                    this.attachments.legalPersonIdCardBack.push({
                                        uri: attachment.uri,
                                        url: attachment.url
                                    })
                                }
                            })
                        }
                        if(res.data.data.onboardingEvents) {
                            let list = res.data.data.onboardingEvents;
                            let rejectTypes = [4, 6, 22];
                            for(let item of list) {
                                if(rejectTypes.includes(item.eventType)) {
                                    this.onboardingEventList.push(item);
                                } else {
                                    return;
                                }
                            }
                        }
                    }
                }
            });
        },
        watch: {
            onboarding: {
                deep: true,
                handler(val, oldVal) {
                    if(val && oldVal && val.status !== oldVal.status && val.status < 0) {
                        this.$alert('您的进件申请需要补充材料，请根据页面显示的审核意见，修改后重新提交审核', '提示', {
                            confirmButtonText: '确定',
                        });
                    }
                }
            }
        }
    }
</script>

<style>
.tipCopiable {
    font-size: 14px;
    color: #888;
    vertical-align: middle;
    padding: 8px 12px;
    margin: 0px 50px;
    background-color: #f0f0f0;
    cursor: pointer;
    position: relative;
}
@media screen and (max-width: 1000px) {
    .tipCopiable {
        display: none;
    }
}
@media screen and (max-width: 1100px) {
    .tipCopiable {
        zoom: 0.55;
    }
}
@media screen and (max-width: 1250px) {
    .tipCopiable {
        zoom: 0.75;
    }
}
.file-upload {
    &.hide-upload {
        .el-upload--picture-card {
            display: none;
        }
    }
}
</style>
