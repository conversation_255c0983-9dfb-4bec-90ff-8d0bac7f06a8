<template>
    <div>
        <el-card shadow='never'>
            <div slot='header' class='clearfix'>
                <span>进件列表</span>
            </div>
            <el-tabs @tab-click="toClickTab" v-model="editableTabsValue" class="tab-title-lg" style="box-shadow: none;">
                <el-tab-pane :label="item.name" :name="item.id.toString()" v-for="(item, index) in statusBox" :key="index">
                    <el-form :model='form' :inline='true' class='demo-form-inline'>
                        <el-row type='flex' align='bottom'>
                            <el-form-item label="进件号：">
                                <el-input v-model='form.onboardingNo' placeholder="请输入完整进件号" style='width: 200px;' clearable></el-input>
                            </el-form-item>
                            <el-form-item label="企业名称：">
                                <el-input v-model='form.enterpriseName' placeholder="请输入企业名称" style='width: 220px;' clearable></el-input>
                            </el-form-item>
                            <el-form-item label="所属渠道" prop="channel">
                                <el-select v-model="form.channelId" placeholder="请选择渠道" filterable>
                                    <el-option
                                        v-for="item in channelList"
                                        :key="item.id"
                                        :label="item.name"
                                        :value="item.id">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-row>
                        <el-row>
                            <el-form-item label="创建时间">
                                <el-date-picker
                                    v-model="form.date"
                                    type="daterange"
                                    range-separator="~"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    value-format="yyyy-MM-dd"
                                    clearable
                                    @change="toChangeTime"
                                >
                                </el-date-picker>
                            </el-form-item>
                            <el-form-item label="缺失材料">
                                <el-select v-model="form.missingAttachmentType" placeholder="请选择" clearable>
                                    <el-option v-for="item in missingAttachmentTypeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="我的进件">
                                <el-switch v-model="form.filterMine" @change="queryOnboardingList"></el-switch>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" icon="el-icon-search" size="medium" @click="queryOnboardingList">查询</el-button>
                            </el-form-item>
                        </el-row>
                    </el-form>
                    <el-table :data='tableData.records' style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                        <el-table-column prop="onboardingNo" fixed label="进件号" width="200"></el-table-column>
                        <el-table-column prop="enterpriseName" label="企业名称" min-width="250">
                            <template slot-scope='scope'>
                                <span>{{ scope.row.enterpriseName }}</span>
                                <el-tooltip v-if="scope.row.enterpriseExist" class="item" effect="dark" content="企业已存在" placement="top-start">
                                    <i class="el-icon-warning" style="color: red; margin-left: 2px;"></i>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                        <el-table-column prop="channelName" label="渠道名称" min-width="200"></el-table-column>
                        <el-table-column label="关联税地" min-width="200">
                            <template slot-scope='scope'>
                                <span>{{ scope.row.taxNames.join(", ") }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="statusName" label="进件状态" width="110" align="center">
                            <template slot-scope='scope'>
                                <div :class="scope.row.status === -1 ? 'status_reject' : 'status'">{{ scope.row.statusName }}</div>
                                <div v-if="scope.row.attachmentMissing && scope.row.status < 99" style="color: red; font-weight: 500; font-size: 12px">资料缺失</div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="typeName" label="进件类型" width="100"></el-table-column>
                        <el-table-column prop="referrerNames" label="推荐人" width="150">
                            <template slot-scope='scope'>
                                <span>{{ scope.row.referrerNames.join(", ") }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="createTime" label="创建时间" width="170"></el-table-column>
                        <el-table-column label="操作" fixed='right' width="150">
                            <template slot-scope='scope'>
                                <el-button type="text" style="font-size: 15px;" @click="viewOnboarding(scope.row.id)">查看</el-button>
                                <el-button v-if='scope.row.status <= 1 && scope.row.privileges != undefined && scope.row.privileges.editable' type="text" style="font-size: 15px;"  @click="editOnboarding(scope.row.id)">编辑</el-button>
                                <el-button v-if='scope.row.status == 1 && scope.row.privileges != undefined && scope.row.privileges.deletable' type="text" style="font-size: 15px;"  @click="delOnboarding(scope.row.id)">删除</el-button>
                                <el-button v-if='scope.row.status === 3 && scope.row.privileges != undefined && scope.row.privileges.operatorReviewable' type="text" style="font-size: 15px;" @click="auditOnboarding(scope.row.id)">运营审核</el-button>
                                <el-button v-if='scope.row.status === 4 && scope.row.privileges != undefined && scope.row.privileges.riskControlReviewable' type="text" style="font-size: 15px;" @click="auditOnboarding(scope.row.id)">风控审核</el-button>
                                <el-button v-else-if='[5,6].includes(scope.row.status) && scope.row.privileges != undefined && scope.row.privileges.documentAchievable' type="text" style="font-size: 15px;" @click="showContractDialog(scope.row)">签约归档</el-button>
                                <el-button v-else-if='scope.row.status === 7 && scope.row.privileges != undefined && scope.row.privileges.openAccountable' type="text" style="font-size: 15px;" @click="openAccount(scope.row.id, scope.row.enterpriseName)">开户</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="pagination">
                        <el-pagination
                            @current-change="handleCurrentPageChange"
                            :current-page="tableData.currentPage"
                            :page-size="tableData.pageSize"
                            layout="total, prev, pager, next, jumper"
                            :total="tableData.total"
                        ></el-pagination>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </el-card>

        <el-dialog title="签约归档" :visible.sync="contractDialogVisible">
            <el-form :model="contract" ref="contractForm">
                <el-form-item label="待邮寄/待签约合同扫描件：" prop="files" v-if="currentRow.status == 5">
                    <el-upload
                        class="file-upload"
                        :class="{ 'hide-upload': contract.deliverFiles.length > 4 }"
                        action=""
                        :limit="5"
                        :http-request="uploadFiles"
                        :before-upload="beforeUpload"
                        :on-success="deliverContractUploadSuccess"
                        :on-error="deliverContractUploadError"
                        :on-remove="deliverContractRemove"
                        :on-cancel="deliverContractRemove"
                        :file-list="contract.deliverFiles"
                        accept=".doc,.docx,.pdf,.jpg,.jpeg,.png,.xls,.xlsx,.csv,.zip,.rar,.7z"
                        list-type="">
                        <el-button slot="trigger" size="small" type="primary">点击上传</el-button>
                        <div slot="tip" class="el-upload__tip">可上传jpg、png、pdf、Word、Excel、zip和rar等文件格式，且不超过10MB</div>
                        <a target='_blank' :href="onboardingTemplateURL" style="color: red; margin-left: 10px;">点此下载合同模版</a>
                    </el-upload>
                </el-form-item>
                <el-form-item label="终版（双方已盖章确认）合同扫描件：" prop="files" v-if="currentRow.status == 6">
                    <el-upload
                        class="file-upload"
                        :class="{ 'hide-upload': contract.finalFiles.length > 4 }"
                        action=""
                        :limit="5"
                        :http-request="uploadFiles"
                        :before-upload="beforeUpload"
                        :on-success="finalContractUploadSuccess"
                        :on-error="finalContractUploadError"
                        :on-remove="finalContractRemove"
                        :on-cancel="finalContractRemove"
                        :file-list="contract.finalFiles"
                        accept=".doc,.docx,.pdf,.jpg,.jpeg,.png,.xls,.xlsx,.csv,.zip,.rar,.7z"
                        list-type="">
                        <el-button size="small" type="primary">点击上传</el-button>
                        <div slot="tip" class="el-upload__tip">可上传jpg、png、pdf、Word、Excel、zip和rar等文件格式，且不超过10MB</div>
                    </el-upload>
                </el-form-item>
                <el-form-item prop="deliverName" label='快递公司' v-if="currentRow.status > 4">
                    <el-input id="deliverName" v-model.trim="contract.deliverName" @input="changeDeliverName" placeholder="请输入快递公司名称"></el-input>
                </el-form-item>
                <el-form-item prop="deliverNo" label='快递单号' v-if="currentRow.status > 4">
                    <el-input id="deliverNo" v-model.trim="contract.deliverNo" @input="changeDeliverNo" placeholder="请输入快递单号"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="hideContractDialog">取 消</el-button>
                <el-button type="primary" @click="saveContractInfo">保 存</el-button>
                <el-button type="primary" @click="ignoreContract" v-if="currentRow.status == 5 || currentRow.status == 6">暂时跳过</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { getOnboardingList, saveContractInfo, openAccount, deleteOnboarding } from '@/api/onboarding';
import { getChannelList } from '@/api/branch/enterprise.js';
import { fileUpload } from '@/api/system/user.js'
import { Message } from 'element-ui'
import { ONBOARDING_CONTRACT_TEMPLATE_URL } from '@/api/config';

export default {
    data() {
        return {
            statusBox: [
                {
                    name: '全部',
                    id: 0,
                },
                {
                    name: '待完善材料',
                    id: -1
                },
                {
                    name: '待提交',
                    id: 1
                },
                {
                    name: '待风控审核',
                    id: 4
                },
                {
                    name: '待签约',
                    id: 5
                },
                {
                    name: '合同已邮寄',
                    id: 6
                },
                {
                    name: '待开户',
                    id: 7
                },
                {
                    name: '开户中',
                    id: 8
                },
                {
                    name: '已完成',
                    id: 9
                },
                {
                    name: '已终结',
                    id: 99
                }
            ],
            missingAttachmentTypeList: [
                {
                    id: 1,
                    name: '营业执照'
                },
                {
                    id: 2,
                    name: '社保/完税/发票'
                },
                {
                    id: 3,
                    name: '企业门头照'
                },
                {
                    id: 4,
                    name: '办公场景照'
                },
                {
                    id: 5,
                    name: 'C端用工场景照'
                },
                {
                    id: 7,
                    name: '终版合同'
                }
            ],
            form: {
                onboardingNo: '',
                enterpriseName: '',
                filterMine: false,
                startTime: '',
                endTime: ''
            },
            tableData: {
                records: [],
                total: 0,
                pageSize: 10,
                currentPage: 1
            },
            contractDialogVisible: false,
            contract: {
                deliverFiles: [],
                finalFiles: [],
                deliverName: '',
                deliverNo: ''
            },
            currentOnboardingId: '',
            currentRow: {},
            editableTabsValue: '0',
            channelList: [],
            onboardingTemplateURL: ONBOARDING_CONTRACT_TEMPLATE_URL
        }
    },
    methods: {
        toClickTab(tab) {
            this.editableTabsValue = tab.name;
            this.form.status = parseInt(this.editableTabsValue) ? parseInt(this.editableTabsValue) : null;
            this.tableData.currentPage = 1;
            this.fetchData();
        },
        handleCurrentPageChange(pageIndex) {
            this.tableData.currentPage = pageIndex;
            this.fetchData();
        },
        fetchData() {
            getOnboardingList({
                ...this.form,
                pageIndex: this.tableData.currentPage,
                pageSize: this.tableData.pageSize,
            }).then((res) => {
                if (res.data.code === 0) {
                    this.tableData.records = res.data.data.records;
                    this.tableData.total = res.data.data.total;
                }
            });
        },
        queryOnboardingList() {
            this.handleCurrentPageChange(1);
        },
        viewOnboarding(onboardingId) {
            this.$router.push({ path: '/onboarding/detail', query: { id: onboardingId} });
        },
        editOnboarding(onboardingId) {
            this.$router.push({ path: '/onboarding/edit', query: { id: onboardingId } });
        },
        auditOnboarding(onboardingId) {
            this.$router.push({ path: '/onboarding/review', query: { id: onboardingId } });
        },
        openAccount(onboardingId, enterpriseName) {
            this.$confirm(`确定要为 "${enterpriseName}" 创建企业账户吗？`, '提示', {
                type: 'warning'
            }).then(() => {
                openAccount(onboardingId).then((res) => {
                    if (res.data.code === 0) {
                        Message.success('系统操作开户中');
                        this.fetchData();
                    }
                });
            });
        },
        beforeUpload(file) {
            if(file.size > 10 * 1024 * 1024 ) {
                Message.error('文件大小不能超过10MB');
                return false;
            }
        },
        uploadFiles(option) {
            const _this = this;
            const file = option.file;
            let formData = new FormData();
            formData.append('file', file);
            formData.append('path', 'onboarding');

            fileUpload(formData).then((res) => {
                if (res != undefined && res.data.code == 0) {
                    let new_file = {
                        name: file.name,
                        response: res,
                        percentage: 0,
                        raw: file,
                        size: file.size,
                        status: 'success',
                        uid: file.uid,
                        url: res.data.data
                    }
                    option.onSuccess(res, new_file);
                } else {
                    option.onError(res, file);
                }
            });
        },
        toChangeTime(date) {
            if (date) {
                this.form.startTime = date[0] + ' ' + '00:00:00';
                this.form.endTime = date[1] + ' ' + '23:59:59';
            } else {
                this.form.startTime = '';
                this.form.endTime = '';
            }
        },
        deliverContractUploadSuccess(res, file, fileList) {
            if (res.status == 200 && res.data.code == 0) {
                Message.success('上传成功');
                file.url = res.data.data;
                this.contract.deliverFiles = [...this.contract.deliverFiles, file];
            } else {
                Message.error('上传失败');
            }
        },
        deliverContractUploadError(err, file, fileList) {
            this.contract.deliverFiles = fileList;
            Message.error('上传失败');
        },
        deliverContractRemove(file, fileList) {
            this.contract.deliverFiles = fileList;
        },
        changeDeliverName() {
            // this.$refs.contract.validateField('deliverName');
        },
        changeDeliverNo() {
            // this.$refs.contract.validateField('deliverNo');
        },
        finalContractUploadSuccess(res, file, fileList) {
            if (res.status == 200 && res.data.code == 0) {
                Message.success('上传成功');
                file.url = res.data.data;
                this.contract.finalFiles = [...this.contract.finalFiles, file];
            } else {
                Message.error('上传失败');
            }
        },
        finalContractUploadError(err, file, fileList) {
            this.contract.finalFiles = fileList;
            Message.error('上传失败');
        },
        finalContractRemove(file, fileList) {
            this.contract.finalFiles = fileList;
        },
        showContractDialog(row) {
            this.contractDialogVisible = true;
            this.currentRow = row;
        },
        hideContractDialog() {
            this.contractDialogVisible = false;
            this.currentRow = {};
        },
        saveContractInfo() {
            this.$refs.contractForm.validate((valid) => {
                if (!valid) {
                    return false;
                }
                let deliverContractUrls = this.contract.deliverFiles.map(file => file.url)
                let finalContractUrls = this.contract.finalFiles.map(file => file.url)
                if(deliverContractUrls.length === 0 && finalContractUrls.length === 0) {
                    Message.error('请选择要上传的合同');
                    return;
                }
                if((this.contract.deliverName != undefined && this.contract.deliverName.trim() != '') && (this.contract.deliverNo === undefined || this.contract.deliverNo.trim() === '')) {
                    Message.error('请填写快递单号');
                    return;
                }
                saveContractInfo({
                    onboardingId: this.currentRow.id,
                    deliverContractUrls: deliverContractUrls,
                    finalContractUrls: finalContractUrls,
                    deliverName: this.contract.deliverName,
                    deliverNo: this.contract.deliverNo,
                    ignoreFinalContract: false
                }).then((res) => {
                    if (res.data.code === 0) {
                        Message.success('保存成功');
                        this.fetchData();
                        this.contract = {
                            deliverFiles: [],
                            finalFiles: [],
                            deliverName: '',
                            deliverNo: ''
                        };
                        this.contractDialogVisible = false;
                    }
                });
            });
        },
        ignoreContract() {
            this.$confirm('确定要跳过吗？', '提示', {
                type: 'warning'
            }).then(() => {
                saveContractInfo({
                    onboardingId: this.currentRow.id,
                    deliverContractUrls: [],
                    deliverName: "",
                    deliverNo: "",
                    finalContractUrls: [],
                    ignoreFinalContract: true
                }).then((res) => {
                    if (res.data.code === 0) {
                        Message.success('已跳过');
                        this.fetchData();
                        this.contract = {
                            deliverFiles: [],
                            finalFiles: [],
                            deliverName: '',
                            deliverNo: ''
                        };
                        this.contractDialogVisible = false;
                    }
                });
            });
        },
        delOnboarding(id) {
            this.$confirm('确定删除该进件吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                deleteOnboarding(id).then((res) => {
                    if (res.data.code == 0) {
                        Message({
                            type: 'success',
                            message: '删除成功!'
                        });
                        this.fetchData();
                    } else {
                        Message({
                            type: 'error',
                            message: res.data.msg
                        });
                    }
                });
            }).catch(() => {
                Message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        }
    },
    created() {
        this.handleCurrentPageChange(1);
        getChannelList().then((res) => {
            if (res.data.code === 0) {
                this.channelList = res.data.data;
            }
        });
    }
}
</script>

<style scoped>
    .status_reject {
        color: red;
        font-weight: 800;
    }

    .status {
        font-weight: 800;
    }
</style>
