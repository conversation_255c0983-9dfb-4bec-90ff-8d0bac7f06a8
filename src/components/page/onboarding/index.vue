<template>
    <div>
        <el-row v-if="!showAddForm">
            <el-col :span="10"  :offset="2">
                <el-card class="box-card" shadow="never">
                    <h1 class="title">我要推荐客户</h1>
                    <el-form label-width="80px" :rules="rules" ref="form" :model="form">
                        <el-form-item label="企业名称" prop="cropName">
                            <el-input placeholder="请输入要推荐的企业名称" autofocus v-model.trim="form.cropName" @keyup.enter.native="showAdd()" maxlength="50" show-word-limit></el-input>
                        </el-form-item>
                        <el-form-item label="所属渠道" prop="channel">
                            <el-select v-model="form.channel" placeholder="请选择渠道" filterable @keyup.enter.native="showAdd()">
                                <el-option
                                    v-for="item in channelList"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="所属税地" prop="taxes">
                            <el-select v-model="form.taxes" multiple placeholder="请选择税地" filterable @keyup.enter.native="showAdd()">
                                <el-option
                                    v-for="item in taxesList"
                                    :key="item.taxesId"
                                    :label="item.taxesName"
                                    :value="item.taxesId">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <div style="text-align: right;">
                            <el-button type="primary" @click="showAdd()" size="medium">开始进件</el-button>
                        </div>
                    </el-form>
                </el-card>
            </el-col>
        </el-row>
        <el-row v-if="showAddForm">
            <add :onboardingId="onboardingId"></add>
        </el-row>
    </div>
</template>

<script>
    import { listTaxesByType, getChannelList } from '@/api/branch/enterprise.js';
    import { addOnboarding } from '@/api/onboarding.js'
    import add from './components/add.vue'
    export default {
        data() {
            return {
                onboardingId: '',
                taxesList: [],
                channelList: [],
                showAddForm: false,
                form: {
                    cropName: '',
                    channel: '',
                    taxes: []
                },
                rules: {
                    cropName: [
                        { required: true, message: '请填写客户名称', trigger: 'blur' },
                        { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
                    ],
                    channel: [{ required: true, message: '请选择渠道', trigger: 'change' }],
                    taxes: [{ required: true, message: '请选择税地', trigger: 'change' }],
                },
            }
        },
        components: {
            add
        },
        created() {
            listTaxesByType({
                id: null,
                type: 2
            }).then((res) => {
                if (res.data.code === 0) {
                    this.taxesList = res.data.data;
                }
            });
            getChannelList().then((res) => {
                if (res.data.code === 0) {
                    this.channelList = res.data.data;
                }
            });
        },
        methods: {
            showAdd() {
                this.$refs.form.validate((valid) => {
                    if (valid) {
                        addOnboarding({
                            enterpriseName: this.form.cropName,
                            channelId: this.form.channel,
                            taxIds: this.form.taxes
                        }).then((res) => {
                            console.log("res = ", res)
                            if (res.data.code === 0) {
                                this.onboardingId = res.data.data;
                                this.showAddForm = true
                            }
                        });
                    } else {
                        return false;
                    }
                });
            }
        }
    }
</script>

<style >
    .title {
        font-size: 24px;
        color: #333;
        margin-bottom: 20px;
        text-align: center;
 }

</style>