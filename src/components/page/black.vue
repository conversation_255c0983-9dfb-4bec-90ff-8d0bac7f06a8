<template>
    <div></div>
</template>

<script>
import Schart from 'vue-schart';
import bus from '../common/bus';

export default {
    name: 'dashboard',
    data() {
        return {
            name: localStorage.getItem('ms_username'),
            value2: '',
            value1: ''
        };
    },
    components: {
        Schart
    },
    computed: {
        role() {
            return this.name === 'admin' ? '超级管理员' : '普通用户';
        }
    },
    // created() {
    //     this.handleListener();
    //     this.changeDate();
    // },
    // activated() {
    //     this.handleListener();
    // },
    // deactivated() {
    //     window.removeEventListener('resize', this.renderChart);
    //     bus.$off('collapse', this.handleBus);
    // },
    mounted() {
        this.getEchartData();
    },
    created() {
        this.$router.go(-1);
    },
    methods: {
        changeDate() {
            const now = new Date().getTime();
            this.data.forEach((item, index) => {
                const date = new Date(now - (6 - index) * 86400000);
                item.name = `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;
            });
        },
        getEchartData() {
            const chart = this.$refs.chart;
            if (chart) {
                const myChart = this.$echarts.init(chart);
                const option = {
                    legend: {},
                    tooltip: {},
                    dataset: {
                        source: [
                            ['2019-06', 85.8],
                            ['2019-07', 73.4],
                            ['2019-08', 65.2],
                            ['2019-09', 53.9],
                            ['2019-10', 53.9],
                            ['2019-11', 53.9],
                            ['2019-12', 53.9],
                            ['2020-1', 53.9]
                        ]
                    },
                    xAxis: { type: 'category' },
                    yAxis: {},

                    series: [{ type: 'line' }]
                };
                myChart.setOption(option);
                window.addEventListener('resize', function () {
                    myChart.resize();
                });
            }
            this.$on('hook:destroyed', () => {
                window.removeEventListener('resize', function () {
                    myChart.resize();
                });
            });
        }

        // handleListener() {
        //     bus.$on('collapse', this.handleBus);
        //     // 调用renderChart方法对图表进行重新渲染
        //     window.addEventListener('resize', this.renderChart);
        // },
        // handleBus(msg) {
        //     setTimeout(() => {
        //         this.renderChart();
        //     }, 200);
        // },
        // renderChart() {
        //     this.$refs.bar.renderChart();
        //     this.$refs.line.renderChart();
        // }
    }
};
</script>


<style scoped>
.el-row {
    margin-bottom: 20px;
}

.grid-content {
    display: flex;
    align-items: center;
    height: 100px;
}

.grid-cont-right {
    flex: 1;
    text-align: center;
    font-size: 14px;
    color: #999;
}

.grid-num {
    font-size: 30px;
    font-weight: bold;
}

.grid-con-icon {
    font-size: 50px;
    width: 100px;
    height: 100px;
    text-align: center;
    line-height: 100px;
    color: #fff;
}

.grid-con-1 .grid-con-icon {
    background: rgb(45, 140, 240);
}

.grid-con-1 .grid-num {
    color: rgb(45, 140, 240);
}

.grid-con-2 .grid-con-icon {
    background: rgb(100, 213, 114);
}

.grid-con-2 .grid-num {
    color: rgb(45, 140, 240);
}

.grid-con-3 .grid-con-icon {
    background: rgb(242, 94, 67);
}

.grid-con-3 .grid-num {
    color: rgb(242, 94, 67);
}

.user-info {
    display: flex;
    align-items: center;
    padding-bottom: 20px;
    border-bottom: 2px solid #ccc;
    margin-bottom: 20px;
}

.user-avator {
    width: 120px;
    height: 120px;
    border-radius: 50%;
}

.user-info-cont {
    padding-left: 50px;
    flex: 1;
    font-size: 14px;
    color: #999;
}

.user-info-cont div:first-child {
    font-size: 30px;
    color: #222;
}

.user-info-list {
    font-size: 14px;
    color: #999;
    line-height: 25px;
}

.user-info-list span {

    margin-left: 50px;


}

.mgb20 {
    margin-bottom: 20px;
}

.todo-item {
    font-size: 14px;
}

.todo-item-del {
    text-decoration: line-through;
    color: #999;
}

.schart {
    width: 100%;
    height: 300px;
}
.name_title {
    font-size: 14px;
    margin-bottom: 10px;
}
.name_money {
    font-size: 20px;
}
.company_name {
    display: inline-block;
    flex: 1;
}
.topinfo {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.bank_name {
    font-size: 14px;
    border: 1px solid #999;
    padding: 5px;
    border-radius: 5px;
    color: #999;
}
.num_money {
    font-size: 40px;
    text-align: center;
    margin-top: 40px;
}
.word_info {
    text-align: center;
    margin-top: 10px;
    margin-bottom: 60px;
}
</style>
