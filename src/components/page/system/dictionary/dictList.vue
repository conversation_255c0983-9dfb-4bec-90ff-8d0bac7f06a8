<template>
    <el-card shadow="never">
        <div slot="header" class="clearfix">
            <span>字典项</span>
        </div>
        <el-form :inline="true" :model="Form">
            <el-form-item label="字典名称">
                <el-input v-model="Form.name" placeholder="请输入角色名称" clearable></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button type="primary" @click="handleAdd" v-has="'platform_role_add'" icon="el-icon-search">新增字典项</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
            <el-table-column type="index" label="序号" width="50" fixed="left"></el-table-column>
            <el-table-column prop="label" label="字典标签" min-width="260"></el-table-column>
            <el-table-column prop="value" label="字典值" min-width="260">
              
            </el-table-column>
            <el-table-column prop="remark" label="角色描述" min-width="260" show-overflow-tooltip></el-table-column>
            <el-table-column fixed="right" label="操作" min-width="180">
                <template slot-scope="scope">
                    <el-button type="text" icon="el-icon-edit" @click="handleEdi(scope.row)" v-has="'platform_role_edi'" style="font-size: 15px;"
                        >编辑</el-button
                    >
                    <el-button type="text" icon="el-icon-delete" @click="handleDel(scope.row)" v-has="'platform_role_del'" style="font-size: 15px;"
                        >删除</el-button
                    >
                    <el-button
                        type="text"
                        size="small"
                        icon="el-icon-plus"
                        @click="handlePermissions(scope.row)"
                        v-has="'platform_role_assign'"
                        >权限
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="pagination">
            <el-pagination
                @current-change="handleCurrentChange"
                :current-page="tableData.current"
                layout="prev, pager, next, jumper"
                :total="tableData.total"
            ></el-pagination>
        </div>

        <el-dialog title="字典添加" :visible.sync="visiblePermissions" :close-on-click-modal="false" width="800px">
            <permissions v-if="visiblePermissions" :row="form" @close="visiblePermissions = false"></permissions>
        </el-dialog>
    </el-card>
</template>

<script>
import { dictList,  } from '@/api/system/dict';
import AddRole from './add';
import permissions from './permissions';
import { Message } from 'element-ui';
import { currentPage } from '@/utils/common.js';

export default {
    props: {},
    components: {
        AddRole,
        permissions
    },
    data() {
        return {
            Form: {
                name: ''
            },
            current: 1,
            size: 10,
            visible: false,
            //权限对话框
            visiblePermissions: false,
            tableData: {},
            dialogType: 0,
            form: {}
        };
    },
    mounted() {
        this.handleList();
    },
    methods: {
        // 新增角色
        handleAdd() {
            this.dialogType = 0;
            this.visiblePermissions = true;
        },
        // 编辑角色
        handleEdi(row) {
            this.form = row;
            this.dialogType = 1;
            this.visiblePermissions = true;
        },
        // 删除
        handleDel(row) {
            this.$confirm('确认删除该角色?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
             
                })
                .catch(() => {});
        },
        //列表
        handleList(current = this.current, size = this.size) {
            let values = Object.create(null);
            values.current = current;
            values.size = size;
            values.dictTypeId = this.$route.query.id
            dictList(values).then((res) => {
                if (res.data.code == 0) {
                    this.tableData = res.data.data;
                } else {
                    Message.error(res.data.msg);
                }
            });
        },
        //分页-每页条数改变
        handleSizeChange(size) {
            this.size = size;
            this.handleList(1);
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.current = current;
            this.handleList(current);
        },
        //检索
        handleSearch() {
            this.handleList();
        },
        // 分配权限
        handlePermissions(row) {
            this.form = row;
            this.visiblePermissions = true;
        }
    }
};
</script>
<style lang="scss" scoped>
</style>