<template>
  <div>
    <div style="min-height: 300px; overflow-y: auto; max-height: 500px">
      <el-tree :data="data" show-checkbox node-key="id" :default-checked-keys="checkedArr" ref="tree"
               :props="defaultProps">
      </el-tree>
    </div>
    <div style="text-align: right">
      <el-button @click="$emit('close')">取 消</el-button>
      <el-button type="primary" @click="handleUpdate">更 新</el-button>
    </div>
  </div>
</template>

<script>
import {Message} from 'element-ui';
import {listMenusIdsByRoleId, updateRoleMenu, listTreeMenuBySelect} from '@/api/system/role';

export default {
  props: {
    row: {
      type: Object
    }
  },
  data() {
    return {
      data: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      //选中项
      checkedArr: []
    };
  },
  mounted() {
    this.handleList();
  },
  methods: {
    //权限列表
    handleList() {
      listMenusIdsByRoleId(this.row.id)
          .then((res) => {
            if (res.data.code == 0) {
              this.checkedArr = res.data.data;
              return listTreeMenuBySelect();
            } else {
              Message.error(res.data.msg);
            }
          })
          .then((res) => {
            if (res.data.code == 0) {
              this.data = res.data.data;
              // 解析出所有的选中节点
              this.checkedArr = this.resolveAllEunuchNodeId(this.data, this.checkedArr, []);
            } else {
              Message.error(res.data.msg);
            }
          });
    },
    //解析出所有的选中节点id
    resolveAllEunuchNodeId(json, idArr, temp) {
      for (let i = 0; i < json.length; i++) {
        const item = json[i];
        // 存在子节点，递归遍历;不存在子节点，将json的id添加到临时数组中
        if (item.children && item.children.length !== 0) {
          this.resolveAllEunuchNodeId(item.children, idArr, temp);
        } else {
          temp.push(idArr.filter((id) => id === item.id));
        }
      }
      return temp;
    },
    //修改
    handleUpdate() {
      if (this.$refs.tree.getCheckedKeys().length == 0) {
        Message.error('至少勾选一项');
        return;
      }
      let values = {
        roleId: this.row.id,
        menuIds: this.$refs.tree.getCheckedKeys().concat(this.$refs.tree.getHalfCheckedKeys())
      };
      updateRoleMenu(values).then((res) => {
        if (res.data.code == 0) {
          Message.success('操作成功');
          this.$emit('close');
        } else {
          Message.error(res.data.msg);
        }
      });
    }
  }
};
</script>
<style>
</style>