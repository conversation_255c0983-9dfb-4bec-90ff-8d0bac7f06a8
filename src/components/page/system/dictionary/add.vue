<template>
    <div>
        <el-form :model="form" label-width="80px" :rules="rules" ref="form" class="button_box">
            <el-form-item label="字典名称" prop="description">
                <el-input v-model="form.description" clearable placeholder="请输入字典名称"></el-input>
            </el-form-item>
            <el-form-item label="字典类型" prop="type">
                <el-input v-model="form.type" clearable placeholder="请输入类型名称"></el-input>
            </el-form-item>
            <el-form-item label="是否系统内置" prop="dataType">
                <el-select v-model="form.systemFlag" style="width: 100%">
                    <el-option label="是" value="1"></el-option>
                    <el-option label="否" value="0"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="类型备注">
                <el-input
                    v-model="form.remark"
                    type="textarea"
                    :autosize="{ minRows: 2, maxRows: 4 }"
                    clearable
                    maxlength="100"
                    placeholder="请输入角色描述"
                ></el-input>
            </el-form-item>
        </el-form>
        <div style="text-align: center">
            <el-button @click="$emit('close')">取 消</el-button>
            <el-button type="primary" @click="submitForm('form')">确 定</el-button>
        </div>
    </div>
</template>

<script>
import { saveDictType, updateDictType } from '@/api/system/dict';
import { Message } from 'element-ui';

export default {
    props: {
        dialogType: {
            type: Number,
            default: 0
        },
        row: {
            type: Object
        }
    },
    data() {
        return {
            form: {
                description: '',
                remark: '',
                dataType: 1
            },
            rules: {
                description: [{ required: true, message: '请输入字典名称', trigger: 'blur' }],
                type: [{ required: true, message: '请输入类型名称', trigger: 'blur' }]
            }
        };
    },
    mounted() {
        if (this.dialogType == 1) {
            let { description, remark, systemFlag,type } = this.row;
            this.form = {
                description,
                systemFlag,
                remark,
                type
            };
        }
    },
    methods: {
        //提交
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let values = Object.assign({}, this.form);
                    if (this.dialogType == 0) {
                        // 添加
                        saveDictType(values).then((res) => {
                            if (res.data.code == 0) {
                                Message.success('操作成功');
                                this.$emit('close');
                                this.$emit('reload');
                            } else {
                                Message.error(res.data.msg);
                            }
                        });
                    } else {
                        values.id = this.row.id;
                        updateDictType(values).then((res) => {
                            if (res.data.code == 0) {
                                Message.success('操作成功');
                                this.$emit('close');
                                this.$emit('reload');
                            } else {
                                Message.error(res.data.msg);
                            }
                        });
                    }
                }
            });
        }
    }
};
</script>
<style scoped>
</style>