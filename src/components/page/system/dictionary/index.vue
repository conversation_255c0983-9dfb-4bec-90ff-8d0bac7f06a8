<template>
    <el-card>
        <el-tabs @tab-click="handleClick" style="box-shadow: none;" class="tab-title-lg">
            <el-tab-pane label="发票类型管理">
                <el-button type="primary" style="margin-bottom: 20px" @click="toAddType" size="medium">新增发票类型</el-button>
                <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                    <el-table-column prop="name" label="发票类型名称" min-width="120"></el-table-column>
                    <el-table-column label="状态">
                        <template slot-scope="scope">
                            {{ scope.row.status == 0 ? '启用' : '禁用' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="createTime" label="创建时间"></el-table-column>
                    <el-table-column label="操作">
                        <template slot-scope="scope">
                            <el-button type="text" @click="toChangeStatus(scope.row.id, scope.row.status)" style="font-size: 15px;">{{
                                scope.row.status == 0 ? '禁用' : '启用'
                            }}</el-button>
                            <el-button type="text" @click="toDetel(scope.row.id)" style="font-size: 15px;">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="pagination">
                    <el-pagination
                        @current-change="handleCurrentChange"
                        :current-page="tableData.current"
                        layout="prev, pager, next, jumper"
                        :total="tableData.total"
                    ></el-pagination>
                </div>
                <el-dialog title="新增发票类型" :visible.sync="dialogVisible" width="60%" @close="closeDia">
                    <el-form>
                        <el-form-item label="分类名称：">
                            <el-input v-model="typeName" style="width: 250px"></el-input>
                        </el-form-item>
                    </el-form>
                    <span slot="footer" class="dialog-footer">
                        <el-button @click="closeDia">取 消</el-button>
                        <el-button type="primary" @click="sureAdd">确 定</el-button>
                    </span>
                </el-dialog>
            </el-tab-pane>
            <el-tab-pane label="开票类目管理">
                <el-button type="primary" style="margin-bottom: 20px" @click="toAddCategory" size="medium">新增类目管理</el-button>
                <el-table :data="tableDatas.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                    <el-table-column prop="parentName" label="开票种类名称" min-width="120"></el-table-column>
                    <el-table-column prop="name" label="开票内容" min-width="120"></el-table-column>
                    <el-table-column label="状态">
                        <template slot-scope="scope">
                            {{ scope.row.status == 0 ? '启用' : '禁用' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="createTime" label="创建时间"></el-table-column>
                    <el-table-column label="操作">
                        <template slot-scope="scope">
                            <el-button type="text" @click="toChangeStatusCate(scope.row.id, scope.row.status)" style="font-size: 15px;">{{
                                scope.row.status == 0 ? '禁用' : '启用'
                            }}</el-button>
                            <el-button type="text" @click="toDetelCategory(scope.row.id)" style="font-size: 15px;">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="pagination">
                    <el-pagination
                        @current-change="handleCurrentChange"
                        :current-page="tableDatas.current"
                        layout="prev, pager, next, jumper"
                        :total="tableDatas.total"
                    ></el-pagination>
                </div>
                <el-dialog title="新增开票类目" :visible.sync="dialogVisibles" width="40%" @open="toOpenAdd" @close="closeDia">
                    <el-form label-width="120px">
                        <el-form-item label="开票种类名称：">
                            <el-radio-group v-model="radio" @change="toChooseChange">
                                <el-radio label="1">选择已有</el-radio>
                                <el-radio label="2">新增</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="开票种类名称：" v-show="radio == 2">
                            <el-input v-model="forms.name" style="width: 250px"></el-input>
                        </el-form-item>
                        <el-form-item label="开票种类名称：" v-show="radio == 1">
                            <el-select v-model="forms.parentId" filterable>
                                <el-option :label="item.name" :value="item.id" v-for="item in option" :key="item.id"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="开票内容：" v-show="radio == 1">
                            <el-input v-model="forms.name" style="width: 250px"></el-input>
                        </el-form-item>
                    </el-form>
                    <span slot="footer" class="dialog-footer">
                        <el-button @click="closeDia">取 消</el-button>
                        <el-button type="primary" @click="sureAddCategroy">确 定</el-button>
                    </span>
                </el-dialog>
            </el-tab-pane>
        </el-tabs>
    </el-card>
</template>

<script>
import {
    dictTypeList,
    typeDelete,
    typeAdd,
    typeState,
    dictCategoryList,
    categoryFirstList,
    categoryAdd,
    categoryDelete,
    categoryState
} from '@/api/system/invoice.js';
import { Message } from 'element-ui';
export default {
    props: {},
    components: {},
    data() {
        return {
            tableData: {},
            form: {
                size: 10,
                current: 1
            },
            dialogVisible: false,
            typeName: '',
            dialogVisibles: false,
            forms: {},
            tableDatas: {},
            radio: undefined,
            option: []
        };
    },
    mounted() {
        this.getData();
    },
    methods: {
        getData() {
            dictTypeList({
                ...this.form
            }).then((res) => {
                if (res.data.code == 0) {
                    this.tableData = res.data.data;
                }
            });
            dictCategoryList({
                ...this.form
            }).then((res) => {
                if (res.data.code == 0) {
                    this.tableDatas = res.data.data;
                }
            });
        },
        handleClick(tab) {
            this.form.current = 1;
            this.getData();
        },
        toDetel(id) {
            this.$confirm(`确定要删除该类型吗？`, '提示', {
                type: 'warning'
            })
                .then(() => {
                
                    typeDelete(id).then((res) => {
                        if (res.data.code == 0) {
                            Message.success(res.data.msg);
                            this.getData();
                        }
                    });
                })
                .catch(() => {});
        },
        toDetelCategory(id) {
            this.$confirm(`确定要删除该类目吗？`, '提示', {
                type: 'warning'
            })
                .then(() => {
                    categoryDelete({
                        invoiceCategoryId:id
                    }).then((res) => {
                        if (res.data.code == 0) {
                            Message.success(res.data.msg);
                            this.getData();
                        }
                    });
                })
                .catch(() => {});
        },
        toOpenAdd() {
            categoryFirstList().then((res) => {
                if (res.data.code == 0) {
                    this.option = res.data.data;
                }
            });
        },
        toChangeStatus(id, status) {
            let state = undefined;
            if (status == 0) {
                state = 1;
            } else {
                state = 0;
            }
            this.$confirm(`确定要${status == 0 ? '禁用' : '启用'}该类型吗？`, '提示', {
                type: 'warning'
            })
                .then(() => {
                    // let data = new FormData();
                    // data.append('invoiceTypeId', id); //id直接从data中一个个拿
                    // data.append('state', state);
                    typeState({
                        invoiceTypeId:id,
                        state:state,
                    }).then((res) => {
                        if (res.data.code == 0) {
                            Message.success(res.data.msg);
                            this.getData();
                        }
                    });
                })
                .catch(() => {});
        },
        toChangeStatusCate(id, status) {
            let state = undefined;
            if (status == 0) {
                state = 1;
            } else {
                state = 0;
            }
            this.$confirm(`确定要${status == 0 ? '禁用' : '启用'}该类型吗？`, '提示', {
                type: 'warning'
            })
                .then(() => {
                    // let data = new FormData();
                    // data.append('invoiceCategoryId', id); //id直接从data中一个个拿
                    // data.append('state', state);
                    categoryState({
                        invoiceCategoryId:id, 
                        state:state, 
                    }).then((res) => {
                        if (res.data.code == 0) {
                            Message.success(res.data.msg);
                            this.getData();
                        }
                    });
                })
                .catch(() => {});
        },
        toChooseChange() {
            this.forms = {};
        },
        sureAdd() {
            typeAdd({
                name: this.typeName
            }).then((res) => {
                if (res.data.code == 0) {
                    Message.success(res.data.msg);
                    this.dialogVisible = false;
                    this.form.current = 1;
                    Message.success(res.data.msg);
                    this.getData();
                }
            });
        },
        sureAddCategroy() {
            categoryAdd({
                ...this.forms
            }).then((res) => {
                if (res.data.code == 0) {
                    this.dialogVisibles = false;
                    this.form.current = 1;
                    Message.success(res.data.msg);
                    this.getData();
                }
            });
        },
        toAddType() {
            this.dialogVisible = true;
        },
        toAddCategory() {
            this.dialogVisibles = true;
        },
        closeDia() {
            this.dialogVisible = false;
            this.typeName = '';
            this.dialogVisibles = false;
            this.forms = {};
        },
        handleCurrentChange(current) {
            this.form.current = current;
            this.getData();
        },
        handleSizeChange(size) {
            this.form.size = size;
            this.form.current = 1;
            this.getData();
        },
        toSeachList() {
            this.form.current = 1;
            this.getData();
        }
    }
};
</script>
<style>
    .tab-title-lg .el-tabs__item {
        font-size: 16px;
        line-height: 1.5;
        font-weight: 400;
    }

    .tab-title-lg .el-tabs__header {
        margin-bottom: 20px;
    }
</style>