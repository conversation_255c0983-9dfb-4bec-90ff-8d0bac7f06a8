<template>
    <div>
        <el-form label-position="right" :model="Form" :rules="rules" ref="Form" label-width="100px">
            <el-form-item label="账号名称" prop="account" v-if="dialogType == 0">
                <el-input
                    v-model.trim="Form.account"
                    maxlength="20"
                    placeholder="请输入账号名称"
                    :disabled="dialogType == 1 ? true : false"
                ></el-input>
            </el-form-item>
            <el-form-item label="姓名" prop="name">
                <el-input v-model.trim="Form.name" maxlength="20" placeholder="请输入姓名"></el-input>
            </el-form-item>
            <el-form-item label="性别" prop="sex">
                <el-select v-model="Form.sex" placeholder="请选择性别" clearable style="width: 100%">
                    <el-option label="男" :value="0"></el-option>
                    <el-option label="女" :value="1"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="设置手机号" >
                <el-input
                    placeholder="请输入"
                    v-model.trim="Form.phone"
                    maxlength="11"
                ></el-input>
            </el-form-item>
            <el-form-item label="登录密码" prop="password" v-if="dialogType == 0">
                <el-input v-model="Form.password" maxlength="100" placeholder="请输入密码"></el-input>
            </el-form-item>
            <el-form-item label="角色" prop="roles">
                <el-select v-model="Form.roles" multiple placeholder="请选择角色" style="width: 100%">
                    <el-option v-for="item in roleList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="备注">
                <el-input v-model="Form.remark" maxlength="20" placeholder="请输入备注"></el-input>
            </el-form-item>
           
        </el-form>
        <div style="text-align: center; margin-top: 50px">
            <el-button @click="$emit('close')">取消</el-button>
            <el-button type="primary" @click="submitForm('Form')">保存</el-button>
        </div>
    </div>
</template>

<script>
import { Message } from 'element-ui';
import { delNullObj } from '@/utils/common.js';
import { validatorPhone } from '@/utils/rules.js';
import { addAccount, listAllRole, updateAccount } from '@/api/system/user.js';

export default {
    props: {
        dialogType: {
            type: Number
        },
        row: {
            type: Object
        }
    },
    mounted() {
        //都完成后再给待编辑的数据赋值
        Promise.all([this.handleList()]).then(() => {
            if (this.dialogType == 1) {
                let { name, account, phone, password, roleList, sex, lockFlag,remark } = this.row;
                let roles = [];
                roleList.map((item) => {
                    roles.push(item.id);
                });
                this.Form = {
                    name,
                    account,
                    phone,
                    password,
                    roles,
                    sex,
                    lockFlag,
                    remark
                };
            }
        });
    },
    data() {
        let validatorAccount = (rule, value, callback) => {
            let account = /^[A-Za-z0-9_]+$/;
            if (Array.from(value).length < 6) {
                return callback(new Error('账号不能少于6位'));
            } else if (value && !account.test(value)) {
                return callback(new Error('账号只能包含英文、数字包括下划线'));
            } else {
                callback();
            }
        };
        return {
            Form: {
                account: '',
                name: '',
                sex: 0,
                phone: '',
                password: 'rapid#123456',
                roles: '',
                lockFlag: 0
            },
            rules: {
                // account: [
                //   {required: true, message: '请输入账号名称', trigger: 'blur'},
                //   {
                //     validator: validatorAccount,
                //     trigger: 'blur'
                //   }
                // ],
                name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
                phone: [
                  {required: true, message: '请输入手机号', trigger: 'blur'},
                  {validator: validatorPhone, trigger: 'blur'}
                ],
                password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
                roles: [{ required: true, message: '请选择角色', trigger: 'blur,change' }],
                lockFlag: [{ required: true, message: '请选择状态', trigger: 'change' }]
            },
            roleList: [],
            visible: false
        };
    },
    methods: {
        //角色列表
        async handleList() {
            await listAllRole().then((res) => {
                if (res.data.code === 0) {
                    this.roleList = res.data.data;
                } else {
                    Message.error(res.data.msg);
                }
            });
        },
        //表单提交
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if (this.dialogType == 0) {
                        let values = { ...this.Form };
                        addAccount(values).then((res) => {
                            if (res.data.code == 0) {
                                Message.success('操作成功');
                                this.$emit('reload', 1);
                                this.$emit('close');
                            } else {
                                Message.error(res.data.msg);
                            }
                        });
                    } else {
                        let { name, roles, sex, lockFlag, remark,phone } = this.Form;
                        let values = {
                            id: this.row.id,
                            name,
                            roles,
                            sex,
                            remark,
                            phone,
                            lockFlag
                        };
                        updateAccount(delNullObj(values)).then((res) => {
                            if (res.data.code == 0) {
                                Message.success('操作成功');
                                this.$emit('reload', 1);
                                this.$emit('close');
                            } else {
                                Message.error(res.data.msg);
                            }
                        });
                    }
                }
            });
        }
    }
};
</script>
<style>
</style>