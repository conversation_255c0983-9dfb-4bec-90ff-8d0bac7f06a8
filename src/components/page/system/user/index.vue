<template>
    <div>
        <el-card shadow="never">
            <el-form :inline="true" :model="Form" class="demo-form-inline">
                <el-form-item label="姓名">
                    <el-input v-model="Form.name" placeholder="请输入姓名" maxlength="20" clearable></el-input>
                </el-form-item>
                <el-form-item label="手机号">
                    <el-input v-model="Form.phone" placeholder="请输入手机号" maxlength="11" clearable></el-input>
                </el-form-item>
                <el-form-item>
                    <el-badge :value="peopleNum" :hidden="true" >
                        <el-button type="primary" @click="handleSearch" icon="el-icon-search" size="medium">查 询 </el-button>
                    </el-badge>
                    <el-button type="primary" @click="handleAdd" v-if="backDoor" icon="el-icon-plus" size="medium">添加账号1</el-button>
                    <el-badge :value="peopleNum" :hidden="peopleNum > 0 ? false : true" class="item-number">
                        <el-button type="primary" @click="toShowAddQrcode" v-has="'platform_user_add'" icon="el-icon-plus" size="medium"
                            >添加账号</el-button
                        >
                    </el-badge>
                </el-form-item>
            </el-form>
            <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                <el-table-column type="index" label="序号" width="80" fixed="left"></el-table-column>
                <el-table-column prop="name" label="姓名" min-width="80" show-overflow-tooltip fixed="left"></el-table-column>
                <el-table-column prop="wechatNickName" label="绑定微信昵称" min-width="100"> </el-table-column>
                <el-table-column prop="sex" label="性别" min-width="60">
                    <template slot-scope="scope">
                        <span>{{ scope.row.sex == 0 ? '男' : scope.row.sex == 1 ? '女' : '' }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="account" label="账号名称" min-width="120" show-overflow-tooltip></el-table-column>
                <el-table-column prop="remark" label="备注" min-width="120" show-overflow-tooltip></el-table-column>
                <el-table-column label="角色" min-width="150" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span v-for="(item, index) in scope.row.roleList" :key="item.id">{{
                            item.name + (index != scope.row.roleList.length - 1 ? '，' : '')
                        }}</span>
                    </template>
                </el-table-column>
                <!-- <el-table-column prop="lockFlag" label="状态" min-width="80">
                    <template slot-scope="scope">
                        <span class="btn" :style="scope.row.lockFlag == 0 ? 'background:#409EFF' : 'background:#FF6600'">{{
                            scope.row.lockFlag == 0 ? '有效' : '锁定'
                        }}</span>
                    </template>
                </el-table-column> -->
                <el-table-column prop="createTime" label="创建时间" width="200" show-overflow-tooltip></el-table-column>
                <el-table-column min-width="150" label="操作" fixed="right">
                    <template slot-scope="scope">
                        <el-button @click="handleEdit(scope.row)" v-has="'platform_user_edi'" type="text" icon="el-icon-edit" style="font-size: 15px;"
                            >编辑</el-button
                        >
                        <el-button @click="handleDel(scope.row)" v-has="'platform_user_del'" type="text" icon="el-icon-delete" style="font-size: 15px;"
                            >移除</el-button
                        >
                        <el-button
                            @click="handleReset(scope.row)"
                            style="font-size: 15px;"
                            v-has="'platform_user_reset'"
                            type="text"
                            icon="el-icon-setting"
                            v-if="backDoor"
                            >重置密码</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page="tableData.current"
                    layout="prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
            <!-- 编辑 -->
            <el-dialog
                :title="dialogType == 0 ? '添加账号' : '修改账号'"
                :visible.sync="visible"
                :close-on-click-modal="false"
                width="600px"
            >
                <add
                    v-if="visible"
                    @close="visible = false"
                    :row="form"
                    @reload="handleList(tableData.current)"
                    :dialogType="dialogType"
                ></add>
            </el-dialog>

            <el-dialog title="重置密码" :visible.sync="resetVisible" :close-on-click-modal="false" width="400px">
                <el-input v-model="password" placeholder="请输入密码" maxlength="20"></el-input>
                <div style="text-align: center; margin-top: 20px">
                    <el-button @click="resetVisible = false">取消</el-button>
                    <el-button type="primary" @click="resetUserPwd">重置</el-button>
                </div>
            </el-dialog>
        </el-card>
        <qrcodeVue :dialogVisible="dialogVisible" :qrcode="qrcode" @closeDia="closeDias"></qrcodeVue>
    </div>
</template>

<script>
import Add from './add';
import { Message } from 'element-ui';
import { currentPage } from '@/utils/common';
import { accountList, deleteAccount, resetAccountPwd, getUserCode, plRmEnterprise, getAuditUserList } from '@/api/system/user.js';
import qrcodeVue from './qrcode.vue';
export default {
    components: {
        Add,
        qrcodeVue
    },
    computed: {},
    data() {
        return {
            Form: {
                name: '',
                phone: ''
            },
            password: '',
            tableData: {},
            current: 1,
            size: 10,
            visible: false,
            resetVisible: false,
            distributeVisible: false,
            form: {},
            dialogType: 0,
            manageVisible: false,
            userId: '',
            backDoor: false,
            qrcode: {},
            dialogVisible: false,
            peopleNum: 0
        };
    },
    created() {
        if (this.getQueryString('backDoor') == 'xudong') {
            this.backDoor = true;
        }
    },
    mounted() {
        this.handleList();
        this.getPeopleNum();
    },
    methods: {
        getQueryString(name) {
            return (
                decodeURIComponent(
                    (new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || [, ''])[1].replace(/\+/g, '%20')
                ) || null
            );
        },
        // 添加
        handleAdd() {
            this.dialogType = 0;
            this.visible = true;
        },
        // 编辑
        handleEdit(row) {
            this.dialogType = 1;
            this.form = row;
            this.visible = true;
        },
        // 删除
        handleDel(row) {
            this.$saveCode()
                .then((res) => {
                    plRmEnterprise({
                        userId: row.id,
                        password:res.password
                    }).then((res) => {
                        if (res.data.code == 0) {
                            Message.success('操作成功');
                            let { current, size, total } = this.tableData;
                            this.handleList(currentPage(current, size, total));
                        } else {
                            Message.error(res.data.msg);
                        }
                    });
                })
                .catch(() => {});
        },
        // 用户列表
        handleList(current = this.current, size = this.size) {
            let values = Object.create(null);
            values.current = current;
            values.size = size;
            values.name = this.Form.name;
            values.phone = this.Form.phone;
            accountList(values).then((res) => {
                if (res.data.code == 0) {
                    this.tableData = res.data.data;
                } else {
                    Message.error(res.data.msg);
                }
            });
        },
        // 查询
        handleSearch() {
            this.handleList(1);
        },
        //重置密码
        handleReset(row) {
            this.resetVisible = true;
            this.password = '';
            this.form = row;
        },
        // 重置密码
        resetUserPwd() {
            if (this.password.length < 6) {
                Message.error('密码再6-20个字符之间');
                return;
            }
            let values = {
                password: this.password,
                userId: this.form.id
            };
            resetAccountPwd(values).then((res) => {
                if (res.data.code == 0) {
                    Message.success('操作成功');
                    this.resetVisible = false;
                } else {
                    Message.error(res.data.msg);
                }
            });
        },
        //分页-每页条数改变
        handleSizeChange(size) {
            this.size = size;
            this.handleList(1, size);
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.current = current;
            this.handleList(current);
        },
        async toShowAddQrcode() {
            let res = await getUserCode();
            if (res.data.code == 0) {
                this.qrcode = res.data.data;
                this.dialogVisible = true;
            }
        },
        closeDias() {
            this.qrcode = {};
            this.dialogVisible = false;
            this.handleList();
            this.getPeopleNum();
        },
        getPeopleNum() {
            getAuditUserList().then((res) => {
                if (res.data.code != 0) return false;
                this.peopleNum = res.data.data.length;
            });
        }
    }
};
</script>

<style scoped lang="scss">
.btn {
    color: #ffffff;
    padding: 5px 10px;
    border-radius: 5px;
}
.item-number{
    margin-left: 6px;
}
</style>
