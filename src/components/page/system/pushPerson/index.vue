<template>
    <div>
        <el-card shadow="never">
            <div slot="header" class="clearfix">
                <span>充值推送人员</span>
            </div>
            <el-form :inline="true" :model="Form" class="demo-form-inline">
                <el-form-item label="姓名">
                    <el-input v-model="Form.userName" placeholder="请输入姓名" maxlength="20" clearable></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearch" icon="el-icon-search" size="medium">查 询 </el-button>
                    <el-button type="primary" @click="handleAdd" icon="el-icon-plus" size="medium">添加人员</el-button>
                </el-form-item>
            </el-form>
            <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                <el-table-column type="index" label="序号" width="50" fixed="left"></el-table-column>
                <el-table-column prop="userName" label="姓名" min-width="80" show-overflow-tooltip fixed="left"></el-table-column>
                <el-table-column prop="wechatNickName" label="绑定微信昵称" min-width="80"> </el-table-column>
                <el-table-column prop="sex" label="性别" min-width="60">
                    <template slot-scope="scope">
                        <span>{{ scope.row.sex == 0 ? '男' : scope.row.sex == 1 ? '女' : '' }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="account" label="账号名称" min-width="120" show-overflow-tooltip></el-table-column>
                <el-table-column label="角色" min-width="150" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span v-for="(item, index) in scope.row.roleList" :key="item.id">{{
                            item.name + (index != scope.row.roleList.length - 1 ? '，' : '')
                        }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="createTime" label="创建时间" min-width="100" show-overflow-tooltip></el-table-column>
                <el-table-column width="120" label="操作" fixed="right">
                    <template slot-scope="scope">
                        <el-button @click="handleDel(scope.row)" v-has="'platform_user_del'" type="text" icon="el-icon-delete" style="font-size: 15px;"
                            >移除</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page="tableData.current"
                    layout="prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
            <!-- 编辑 -->
            <el-dialog
                :title="dialogType == 0 ? '添加充值人员' : '修改账号'"
                :visible.sync="visible"
                :close-on-click-modal="false"
                width="60%"
                @open="toOpenDia"
            >
                <add v-if="visible" @close="visible = false" :list="peopleList" @addNewPeople="addNewPeople" :dialogType="dialogType"></add>
            </el-dialog>
        </el-card>
    </div>
</template>

<script>
import Add from './add';
import { Message } from 'element-ui';
import { currentPage } from '@/utils/common';
import { getRechargePushUserPage, getUserList, addPushPeople, deletePushPeople } from '@/api/system/pushPerson.js';
export default {
    components: {
        Add
    },
    computed: {},
    data() {
        return {
            Form: {},
            password: '',
            tableData: {},
            current: 1,
            size: 10,
            visible: false,
            resetVisible: false,
            distributeVisible: false,
            form: {},
            dialogType: 0,
            manageVisible: false,
            userId: '',
            backDoor: false,
            qrcode: {},
            dialogVisible: false,
            peopleNum: 0,
            peopleList: []
        };
    },
    created() {
        if (this.getQueryString('backDoor') == 'xudong') {
            this.backDoor = true;
        }
    },
    mounted() {
        this.handleList();
    },
    methods: {
        getQueryString(name) {
            return (
                decodeURIComponent(
                    (new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || [, ''])[1].replace(/\+/g, '%20')
                ) || null
            );
        },
        // 添加
        handleAdd() {
            this.dialogType = 0;
            this.visible = true;
        },
        // 编辑
        handleEdit(row) {
            this.dialogType = 1;
            this.form = row;
            this.visible = true;
        },
        // 删除
        handleDel(row) {
            this.$confirm(`确定将该用户移除推送名单`,`提示`).then(res=>{
                deletePushPeople(row.id).then((res) => {
                    if (res.data.code == 0) {
                        Message.success('操作成功');
                        let { current, size, total } = this.tableData;
                        this.handleList(currentPage(current, size, total));
                    } else {
                        Message.error(res.data.msg);
                    }
                });
            })
        },
        // 用户列表
        handleList(current = this.current, size = this.size) {
            let values = Object.create(null);
            values.current = current;
            values.size = size;
            values.userName = this.Form.userName;
            getRechargePushUserPage(values).then((res) => {
                if (res.data.code == 0) {
                    this.tableData = res.data.data;
                } else {
                    Message.error(res.data.msg);
                }
            });
        },
        // 查询
        handleSearch() {
            this.handleList(1);
        },
        //分页-每页条数改变
        handleSizeChange(size) {
            this.size = size;
            this.handleList(1, size);
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.current = current;
            this.handleList(current);
        },
        closeDias() {
            this.dialogVisible = false;
            this.handleList();
            this.getPeopleNum();
        },
        async toOpenDia() {
            let res = await getUserList();
            if (res.data.code != 0) return false;
            this.peopleList = res.data.data;
        },
        async addNewPeople(e) {
            let res = await addPushPeople({
                ids: e
            });
            if (res.data.code != 0) return false;
            this.$message.success('添加成功！');
            this.visible = false;
            this.current = 1;
            this.handleList();
        }
    }
};
</script>

<style scoped lang="scss">
.btn {
    color: #ffffff;
    padding: 5px 10px;
    border-radius: 5px;
}
.item-number {
    margin-left: 6px;
}
</style>
