<template>
    <div>
        <el-table :data="list" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" :selectable="selectEnable"> </el-table-column>
            <el-table-column prop="userName" label="姓名" min-width="80" show-overflow-tooltip fixed="left"></el-table-column>
            <el-table-column prop="wechatNickName" label="绑定微信昵称" min-width="80"> </el-table-column>
            <el-table-column prop="sex" label="性别" min-width="60">
                <template slot-scope="scope">
                    <span>{{ scope.row.sex == 0 ? '男' : scope.row.sex == 1 ? '女' : '' }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="account" label="账号名称" min-width="120" show-overflow-tooltip></el-table-column>
            <el-table-column label="角色" min-width="150" show-overflow-tooltip>
                <template slot-scope="scope">
                    <span v-for="(item, index) in scope.row.roleList" :key="item.id">{{
                        item.name + (index != scope.row.roleList.length - 1 ? '，' : '')
                    }}</span>
                </template>
            </el-table-column>
        </el-table>
        <div style="text-align: center; margin-top: 50px">
            <el-button @click="$emit('close')">取消</el-button>
            <el-button type="primary" @click="addNewPeole()">保存</el-button>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        dialogType: {
            type: Number
        },
        list: {
            type: Array
        }
    },
    mounted() {},
    data() {
        return {
            tableData: [],
            roleList: [],
            visible: false,
            multipleSelection: []
        };
    },
    methods: {
        selectEnable(row) {
            if (row.isAddition) {
                return false; // 禁用
            } else {
                return true; // 不禁用
            }
        },
       async addNewPeole() {
            let res = await this.$confirm('确定要添加推送人员吗？', '提示')
            if(res){
                this.$emit('addNewPeople', this.multipleSelection);
            }
        },
        handleSelectionChange(val) {
            let list = [];
            val.forEach((item) => {
                list.push(item.id);
            });
            this.multipleSelection = list;
        }
    }
};
</script>
<style>
</style>