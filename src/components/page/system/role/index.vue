<template>
    <el-card shadow="never">
        <el-form :inline="true" :model="Form">
            <el-form-item label="角色名称">
                <el-input v-model="Form.name" placeholder="请输入角色名称" clearable></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="handleSearch" size="medium">查 询</el-button>
                <el-button type="primary" @click="handleAdd" v-has="'platform_role_add'" icon="el-icon-search" size="medium">新增角色</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
            <el-table-column type="index" label="序号" width="80" fixed="left"></el-table-column>
            <el-table-column prop="name" label="角色名称" min-width="260"></el-table-column>
            <el-table-column prop="dataType" label="数据权限" min-width="260">
                <template slot-scope="scope">
                    <span>{{ scope.row.dataType === 1 ? '全部' : scope.row.dataType === 2 ? '当前项目' : '' }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="remark" label="角色描述" min-width="260" show-overflow-tooltip></el-table-column>
            <el-table-column fixed="right" label="操作" min-width="180">
                <template slot-scope="scope">
                    <el-button type="text" icon="el-icon-edit" @click="handleEdi(scope.row)" v-has="'platform_role_edi'" style="font-size: 15px;"
                        >编辑</el-button
                    >
                    <el-button type="text" icon="el-icon-delete" @click="handleDel(scope.row)" v-has="'platform_role_del'" style="font-size: 15px;"
                        >删除</el-button
                    >
                    <el-button
                        type="text"
                        size="small"
                        icon="el-icon-plus"
                        @click="handlePermissions(scope.row)"
                        v-has="'platform_role_assign'" 
                        style="font-size: 15px;"
                        >权限
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="pagination">
            <el-pagination
                @current-change="handleCurrentChange"
                :current-page="tableData.current"
                layout="prev, pager, next, jumper"
                :total="tableData.total"
            ></el-pagination>
        </div>

        <el-dialog :title="dialogType === 1 ? '修改角色' : '新增角色'" :visible.sync="visible" :close-on-click-modal="false" width="600px">
            <add-role
                v-if="visible"
                :dialogType="dialogType"
                :row="form"
                @close="visible = false"
                @reload="handleList(tableData.current)"
            ></add-role>
        </el-dialog>
        <el-dialog title="分配权限" :visible.sync="visiblePermissions" :close-on-click-modal="false" width="800px">
            <permissions v-if="visiblePermissions" :row="form" @close="visiblePermissions = false"></permissions>
        </el-dialog>
    </el-card>
</template>

<script>
import { listRole, deleteRole } from '@/api/system/role';
import AddRole from './add';
import permissions from './permissions';
import { Message } from 'element-ui';
import { currentPage } from '@/utils/common.js';

export default {
    props: {},
    components: {
        AddRole,
        permissions
    },
    data() {
        return {
            Form: {
                name: ''
            },
            current: 1,
            size: 10,
            visible: false,
            //权限对话框
            visiblePermissions: false,
            tableData: {},
            dialogType: 0,
            form: {}
        };
    },
    mounted() {
        this.handleList();
    },
    methods: {
        // 新增角色
        handleAdd() {
            this.dialogType = 0;
            this.visible = true;
        },
        // 编辑角色
        handleEdi(row) {
            this.form = row;
            this.dialogType = 1;
            this.visible = true;
        },
        // 删除
        handleDel(row) {
            this.$saveCode()
                .then((res) => {
                    deleteRole({ id: row.id, password: res.password }).then((res) => {
                        if (res.data.code == 0) {
                            Message.success('操作成功');
                            let { current, size, total } = this.tableData;
                            this.handleList(currentPage(current, size, total));
                        } else {
                            Message.error(res.data.msg);
                        }
                    });
                })
                .catch(() => {});
        },
        //列表
        handleList(current = this.current, size = this.size) {
            let values = Object.create(null);
            values.current = current;
            values.size = size;
            values.name = this.Form.name;
            listRole(values).then((res) => {
                if (res.data.code == 0) {
                    this.tableData = res.data.data;
                } else {
                    Message.error(res.data.msg);
                }
            });
        },
        //分页-每页条数改变
        handleSizeChange(size) {
            this.size = size;
            this.handleList(1);
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.current = current;
            this.handleList(current);
        },
        //检索
        handleSearch() {
            this.handleList();
        },
        // 分配权限
        handlePermissions(row) {
            this.form = row;
            this.visiblePermissions = true;
        }
    }
};
</script>
<style lang="scss" scoped>
</style>