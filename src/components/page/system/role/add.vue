<template>
  <div>
    <el-form :model="form" label-width="80px" :rules="rules" ref="form" class="button_box">
      <el-form-item label="角色名称" prop="name">
        <el-input v-model="form.name" maxlength="30" clearable placeholder="请输入角色名称"></el-input>
      </el-form-item>
      <el-form-item label="数据权限" prop="dataType">
        <el-select v-model="form.dataType" placeholder="请选择数据权限" style="width:100%">
          <el-option label="全部" :value="1"></el-option>
          <el-option label="当前项目" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="角色描述">
        <el-input
            v-model="form.remark"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
            clearable
            maxlength="100"
            placeholder="请输入角色描述"
        ></el-input>
      </el-form-item>
    </el-form>
    <div style="text-align: center">
      <el-button @click="$emit('close')">取 消</el-button>
      <el-button type="primary" @click="submitForm('form')">确 定</el-button>
    </div>
  </div>
</template>

<script>
import {saveRole, updateRole} from '@/api/system/role';
import {Message} from 'element-ui';

export default {
  props: {
    dialogType: {
      type: Number,
      default: 0
    },
    row: {
      type: Object
    }
  },
  data() {
    return {
      form: {
        name: '',
        remark: '',
        dataType: 1
      },
      rules: {
        name: [{required: true, message: '请输入角色名称', trigger: 'blur'}],
        dataType: [{required: true, message: '请选择数据权限', trigger: 'change'}],
      }
    };
  },
  mounted() {
    if (this.dialogType == 1) {
      let {name, remark, dataType} = this.row;
      this.form = {
        name,
        dataType,
        remark
      };
    }
  },
  methods: {
    //提交
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let values = Object.assign({}, this.form);
          if (this.dialogType == 0) {
            // 添加
            saveRole(values).then((res) => {
              if (res.data.code == 0) {
                Message.success('操作成功');
                this.$emit('close');
                this.$emit('reload');
              } else {
                Message.error(res.data.msg);
              }
            });
          } else {
            values.id = this.row.id;
            updateRole(values).then((res) => {
              if (res.data.code == 0) {
                Message.success('操作成功');
                this.$emit('close');
                this.$emit('reload');
              } else {
                Message.error(res.data.msg);
              }
            });
          }
        }
      });
    }
  }
};
</script>
<style scoped>

</style>