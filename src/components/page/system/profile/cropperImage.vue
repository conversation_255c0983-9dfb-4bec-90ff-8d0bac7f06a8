<template>
    <div class="cropper-content">
        <div class="cropper-box">
            <el-row>
                <el-col :xs="24" :md="12" :style="{ height: '350px' }">
                    <vue-cropper
                        ref="cropper"
                        :img="option.img"
                        :info="option.info"
                        :autoCrop="option.autoCrop"
                        :autoCropWidth="option.autoCropWidth"
                        :autoCropHeight="option.autoCropHeight"
                        :full="option.full"
                        :fixedBox="option.fixedBox"
                        :original="option.original"
                        :height="option.height"
                        :enlarge="option.enlarge"
                        @realTime="realTime"
                    ></vue-cropper>
                </el-col>
                <el-col :xs="24" :md="12" :style="{ height: '350px' }">
                    <!-- 预览效果图 -->
                    <div class="show-preview">
                        <div :style="previews.div" class="preview">
                            <img :src="previews.url" :style="previews.img" />
                        </div>
                    </div>
                </el-col>
            </el-row>
            <!-- 底部操作工具按钮 -->
            <div class="tool">
                <el-upload
                    class="upload-demo"
                    :headers="headers"
                    action="#"
                    :show-file-list="false"
                    :on-success="handleAvatarSuccess"
                    :auto-upload="false"
                    :on-change="onChangeFile"
                >
                    <el-button size="mini" icon="el-icon-upload2">选择图像</el-button>
                </el-upload>
                <el-button size="mini" plain icon="el-icon-zoom-in" @click="changeScale(1)"></el-button>
                <el-button size="mini" plain icon="el-icon-zoom-out" @click="changeScale(-1)"></el-button>
                <el-button size="mini" plain @click="rotateLeft">↺</el-button>
                <el-button size="mini" plain @click="rotateRight">↻</el-button>
            </div>
            <div style="text-align: center; margin-top: 20px">
                <el-button @click="$emit('close')">取消</el-button>
                <el-button type="primary" @click="uploadImg()">确认</el-button>
            </div>
        </div>
    </div>
</template>

<script>
import { VueCropper } from 'vue-cropper';
import { updateUserAvatar } from '@/api/system/profile';
import { Message } from 'element-ui';
// import { timeFormat, uuid } from "@/utils/date.js";
import auth from '@/api/auth.js';
import { client, getFileNameUUID, getTimeNow } from '@/utils/oss.js';
import { OSS_URL } from '@/api/config';
export default {
    name: 'CropperImage',
    components: {
        VueCropper
    },
    props: ['Name', 'avatar', 'detail'],
    data() {
        return {
            name: this.Name,
            previews: {},
            fileName: '',
            headers: {
                Authorization: 'Bearer ' + auth.curUser().access_token
            },
            option: {
                img: '', //裁剪图片的地址
                info: true, //图片大小信息
                autoCrop: true, //是否默认生成截图框
                autoCropWidth: 200, //默认生成截图框宽度
                autoCropHeight: 200, //默认生成截图框高度
                full: false, //false按原比例裁切图片，不失真
                fixedBox: false, //固定截图框大小，不允许改变
                original: true, //上传图片按照原始比例渲染
                height: true, //是否按照设备的dpr 输出等比例图片
                enlarge: 1 //图片根据截图框输出比例倍数
            },
            photo: ''
        };
    },
    mounted() {
        this.setAvatarBase64();
    },
    methods: {
        // 设置头像base64
        setAvatarBase64() {
            let _this = this;
            let image = new Image();
            // 处理缓存
            image.src = this.avatar + '?v=' + Math.random();
            // 支持跨域图片
            image.crossOrigin = '*';
            image.onload = function () {
                _this.option.img = _this.transBase64FromImage(image);
            };
        },
        // 将网络图片转换成base64格式
        transBase64FromImage(image) {
            let canvas = document.createElement('canvas');
            canvas.width = image.width;
            canvas.height = image.height;
            let ctx = canvas.getContext('2d');
            ctx.drawImage(image, 0, 0, image.width, image.height);
            // 可选其他值 image/jpeg
            return canvas.toDataURL('image/png');
        },
        //图片缩放
        changeScale(num) {
            num = num || 1;
            this.$refs.cropper.changeScale(num);
        },
        //向左旋转
        rotateLeft() {
            this.$refs.cropper.rotateLeft();
        },
        //向右旋转
        rotateRight() {
            this.$refs.cropper.rotateRight();
        },
        //实时预览函数
        realTime(data) {
            this.previews = data;
        },
        //成功上传头像
        handleAvatarSuccess(res, file) {
            this.previews.url = URL.createObjectURL(file.raw);
        },
        //选择图片裁剪前的处理
        onChangeFile(file) {
            this.fileName = file.raw.name;
            if (file.raw.type.indexOf('image/') == -1) {
                this.$message.error('文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。');
            }
            // 本地图片转成base64，用于截图框显示本地图片
            this.imageToBase64(file.raw);
        },
        // 将本地图片转化为Base64，否则vue-cropper组件显示不出要本地需要剪裁的图片
        imageToBase64(file) {
            let reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => {
                // 截图框中的图片
                this.option.img = reader.result;
            };
            reader.onerror = function (error) {
                console.log('Error: ', error);
            };
        },
        async putObject(data) {
            // let newDate = timeFormat().split("-").join("");
            var fileName = '/avatar/' + getTimeNow() + '/' + getFileNameUUID() + '.jpg';
            console.log('头像地址====================================>',fileName)
            client()
                .multipartUpload(fileName, data, {
                    headers: {
                        'Content-Type': 'image/jpg'
                    },
                    progress: function (percentage, cpt) {
                        console.log('打印进度', percentage);
                    }
                })
                .then((res) => {
                    updateUserAvatar( fileName).then((res) => {
                        if (res.data.code == 0) {
                            Message.success('操作成功');
                            this.$emit('close');
                            this.$emit('fetchBaseInfo');
                        }
                    });
                });
        },
        //上传图片
        uploadImg() {
            this.$refs.cropper.getCropBlob((data) => {
                this.putObject(data);
            });
        }
    }
};
</script>

<style scoped lang="scss">
.cropper-content {
    display: flex;
    display: -webkit-flex;
    justify-content: flex-end;
    .cropper-box {
        flex: 1;
        width: auto;
    }
    .show-preview {
        flex: 1;
        -webkit-flex: 1;
        display: flex;
        display: -webkit-flex;
        justify-content: center;
        -webkit-justify-content: center;
        .preview {
            overflow: hidden;
            border-radius: 50%;
            border: 1px solid #67c23a;
            background: #cccccc;
        }
    }
}
::v-deep .el-upload {
    display: inline;
    margin-right: 10px;
}
.tool {
    display: flex;
    justify-content: flex-start;
    margin-top: 20px;
}
</style>
