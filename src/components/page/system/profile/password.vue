<template>
    <div>
        <el-form
            :model="ruleForm"
            status-icon
            :rules="rules"
            label-position="right"
            ref="ruleForm"
            label-width="100px"
            class="demo-ruleForm"
        >
            <el-form-item prop="oldPwd" label="旧密码:" v-if="passInit == 1">
                <el-input
                    type="password"
                    v-model="ruleForm.oldPwd"
                    auto-complete="off"
                    placeholder="请输入旧密码"
                    clearable
                    show-password
                ></el-input>
            </el-form-item>
            <el-form-item prop="newPwd" label="新密码：">
                <el-input
                    type="password"
                    v-model="ruleForm.newPwd"
                    auto-complete="off"
                    placeholder="请输入新密码"
                    clearable
                    show-password
                ></el-input>
            </el-form-item>
            <el-form-item prop="checkPass" label="确认密码">
                <el-input
                    type="password"
                    v-model="ruleForm.checkPass"
                    auto-complete="off"
                    placeholder="请确认密码"
                    clearable
                    show-password
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="submitForm('ruleForm')" size="medium">保 存</el-button>
                <el-button @click="close" size="medium">关 闭</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import { updateUserPwd, setSaveUserPwd } from '@/api/system/profile';
import { Message } from 'element-ui';
import { logout, getUserInfo } from '@/api/system/login';
import auth from '@/api/auth.js';
export default {
    props: {
        detail: {
            type: Object
        }
    },
    data() {
        let validatePass1 = (rule, value, callback) => {
            if (!value) {
                callback(new Error('请输入新密码'));
            } else if (value.toString().length < 6 || value.toString().length > 18) {
                callback(new Error('密码长度为6-18位'));
            } else if (value === this.ruleForm.oldPwd) {
                callback(new Error('新密码与旧密码一致！'));
            } else {
                callback();
            }
        };
        let validatePass2 = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('请再次输入密码'));
            } else if (value !== this.ruleForm.newPwd) {
                callback(new Error('两次输入的密码不一致'));
            } else {
                callback();
            }
        };
        return {
            ruleForm: {
                oldPwd: '',
                newPwd: '',
                checkPass: ''
            },
            rules: {
                oldPwd: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
                newPwd: [{ required: true, validator: validatePass1, trigger: 'blur' }],
                checkPass: [{ required: true, validator: validatePass2, trigger: 'blur' }]
            },
            passInit: undefined
        };
    },
    methods: {
        //提交修改
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    /**原来修改密码逻辑**/
                    // let values = Object.create(null);
                    // values.id = this.detail.id;
                    // values.name = this.detail.name;
                    // values.oldPwd = this.ruleForm.oldPwd;
                    // values.newPwd = this.ruleForm.newPwd;
                    // updateUserPwd(values).then((res) => {
                    //     if (res.data.code == 0) {
                    //         this.$message.success('修改成功，请重新登录!');
                    //         logout().then((res) => {
                    //             auth.doLogout();
                    //         });
                    //     } else {
                    //         Message.error(res.data.msg);
                    //     }
                    // });
                    /**新设置安全密码逻辑**/
                    let values = Object.create(null);
                    values.oldPassword = this.ruleForm.oldPwd;
                    values.newPassword = this.ruleForm.newPwd;
                    setSaveUserPwd(values).then((res) => {
                        if (res.data.code == 0) {
                            this.$message.success('设置成功！')
                            this.getPasswordInfo();
                        }
                    });
                } else {
                }
            });
        },
        //关闭返回上一个页面
        close() {
            this.$router.go(-1);
        },
        async getPasswordInfo() {
            let res = await getUserInfo();
            this.passInit = res.data.init;
        }
    },
    created() {
        this.getPasswordInfo();
    }
};
</script>