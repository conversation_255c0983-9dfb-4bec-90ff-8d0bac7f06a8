<template>
    <el-card shadow="never">
        <el-row :gutter="24">
            <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="6">
                <el-card class="box-card" shadow="never">
                    <div slot="header" class="clearfix">
                        <span>个人资料</span>
                    </div>
                    <div class="user-info">
                        <div class="user-avatar" @click="changeAvatar" :detail="detail">
                            <img :src="avatar" />
                        </div>
                    </div>
                    <ul class="list-group list-group-striped">
                        <li class="list-group-item">
                            <span>登录名称：</span>
                            <span>{{ detail.account }}</span>
                        </li>
                        <li class="list-group-item">
                            <span>创建时间：</span>
                            <span>{{ detail.createTime | dateFormat }}</span>
                        </li>
                    </ul>
                </el-card>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="16" :xl="18">
                <el-card class="box-card" shadow="never">
                    <div slot="header" class="clearfix">
                        <span>基本资料</span>
                    </div>
                    <div>
                        <el-tabs type="card" v-model="type" @tab-click="handleClick" style="box-shadow: none;">
                            <el-tab-pane label="基本资料">
                                <el-form ref="form" :model="form" :rules="rules" label-position="right" label-width="100px">
                                    <el-form-item label="用户名称：" prop="name">
                                        <el-input v-model="form.name" clearable></el-input>
                                    </el-form-item>
                                    <el-form-item label="手机号码：" prop="phone">
                                        <el-input maxlength="11" v-model="form.phone" clearable></el-input>
                                    </el-form-item>
                                    <el-form-item label="短信验证码" prop="msgCode" v-if="form.phone != detail.phone">
                                        <el-input v-model="form.msgCode" clearable>
                                            <el-button
                                                style="padding-right: 10px"
                                                slot="suffix"
                                                type="text"
                                                @click="sendCode"
                                                :disabled="disabled"
                                            >
                                                {{ btnInfo }}
                                            </el-button>
                                        </el-input>
                                    </el-form-item>
                                    <el-form-item label="性别：" prop="sex">
                                        <el-radio-group v-model="form.sex">
                                            <el-radio :label="0">男</el-radio>
                                            <el-radio :label="1">女</el-radio>
                                        </el-radio-group>
                                    </el-form-item>
                                    <el-form-item>
                                        <el-button type="primary" @click="submitForm('form')" size="medium">保 存</el-button>
                                        <el-button @click="close" size="medium">关 闭</el-button>
                                    </el-form-item>
                                </el-form>
                            </el-tab-pane>
                            <el-tab-pane label="安全密码">
                                <edi-password :detail="detail"></edi-password>
                            </el-tab-pane>
                        </el-tabs>
                    </div>
                </el-card>
            </el-col>
        </el-row>
        <!-- 裁剪组件弹窗 -->
        <el-dialog title="修改头像" :visible.sync="visible" width="800px" :close-on-click-modal="false" center>
            <cropper-image
                :Name="cropperName"
                @close="visible = false"
                :avatar="avatar"
                :detail="detail"
                @fetchBaseInfo="fetchBaseInfo"
                v-if="visible"
                @reload="getUserInfo"
            ></cropper-image>
        </el-dialog>
    </el-card>
</template>

<script>
import { getUserInfo, updateUserInfo, getPhoneSMSCode } from '@/api/system/profile';
import CropperImage from './cropperImage.vue';
import EdiPassword from './password.vue';
import { Message } from 'element-ui';
import auth from '@/api/auth.js';
import { timeFormat } from '@/utils/common.js';
import { OSS_URL } from '@/api/config.js';
export default {
    props: {},
    components: {
        EdiPassword,
        CropperImage
    },
    filters: {
        dateFormat(value) {
            return timeFormat(value);
        }
    },
    data() {
        let validatorPhone = (rule, value, callback) => {
            if (!value) {
                callback(new Error('请输入联系方式'));
            } else {
                const reg = /^1[3|4|5|6|7|8][0-9]\d{8}$/;
                if (reg.test(value)) {
                    callback();
                } else {
                    return callback(new Error('请输入正确的手机号'));
                }
            }
        };
        return {
            disabled: false,
            time: 59,
            btnInfo: '发送验证码',
            detail: {},
            avatar: '',
            form: {
                name: '',
                phone: '',
                sex: ''
            },
            rules: {
                name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
                phone: [
                    { required: true, message: '请输入手机号', trigger: 'blur' },
                    { required: true, validator: validatorPhone, trigger: 'blur,change' }
                ],
                msgCode: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
            },
            visible: false,
            cropperName: '',
            type: '0'
        };
    },
    mounted() {
        this.getUserInfo();
        if(this.$route.query.type==1){
            this.type = this.$route.query.type
        }
    },
    methods: {
        //修改头像
        changeAvatar(name) {
            this.visible = true;
            this.cropperName = name;
        },
        fetchBaseInfo() {
            // debugger
            this.getUserInfo();
        },
        //获取用户信息
        getUserInfo() {
            getUserInfo().then((res) => {
                if (res.data.code == 0) {
                    this.detail = res.data.data;
                    if (res.data.data.avatar) {
                        this.avatar = res.data.data.avatar.indexOf('http') > -1 ? res.data.data.avatar : OSS_URL + res.data.data.avatar;
                    } else {
                        this.avatar = '';
                    }
                    auth.doUserInfo(res.data.data);
                    let { name, phone, sex } = this.detail;
                    this.form = {
                        name,
                        phone,
                        sex
                    };
                    // debugger
                    console.log('sadasdas', this.avatar);
                } else {
                    Message.error(res.data.msg);
                }
            });
        },
        //验证码
        sendCode() {
            let values = {
                phone: this.form.phone
            };
            if (Array.from(values.phone).length == 11) {
                getPhoneSMSCode(values).then((res) => {
                    if (res.data.code == 0) {
                        Message.success('验证码已发送');
                        this.time = 60;
                        this.timer();
                    } else {
                        Message.error(res.data.msg);
                    }
                });
            } else {
                Message.error('手机号不合法');
            }
        },
        //修改基本资料保存
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let values = Object.assign({}, this.form);
                    values.id = this.detail.id;
                    if (this.form.phone === this.detail.phone) {
                        delete values.msgCode;
                    }
                    updateUserInfo(values).then((res) => {
                        if (res.data.code == 0) {
                            Message.success('修改成功');
                            this.getUserInfo();
                        } else {
                            Message.error(res.data.msg);
                        }
                    });
                }
            });
        },
        //发送验证码倒计时
        timer() {
            if (this.time > 0) {
                this.disabled = true;
                this.time--;
                this.btnInfo = this.time + '秒后重新发送验证码';
                setTimeout(this.timer, 1000);
            } else {
                this.time = 0;
                this.btnInfo = '发送验证码';
                this.disabled = false;
            }
        },
        //关闭返回上一个页面
        close() {
            this.$router.go(-1);
        },
        handleClick(tab, event) {
            console.log(tab, event);
        }
    }
};
</script>
<style scoped lang="scss">
.box-card {
    margin-bottom: 20px;
}

.user-info {
    padding-bottom: 20px;
    margin-bottom: 10px;
}

.user-avatar {
    display: flex;
    // vertical-align: middle;
    align-items: center;
    justify-content: center;
    // width: 120px;
    // height: 120px;
    // margin: 0 auto;
    border-radius: 50%;
}
.user-avatar img {
    display: flex;
    vertical-align: middle;
    align-items: center;
    justify-content: center;
    width: 120px;
    height: 120px;
    margin: 0 auto;
    border-radius: 50%;
}
.big-avatar {
    width: 120px;
    height: 120px;
}
.list-group-item {
    border-bottom: 1px solid #e7eaec;
    border-top: 1px solid #e7eaec;
    margin-bottom: -1px;
    padding: 15px 0px;
    font-size: 15px;
    color: #666;
    display: flex;
    justify-content: space-between;
}
</style>