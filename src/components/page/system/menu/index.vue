<template>
  <el-card shadow="never">
    <div style="text-align: right; margin-bottom: 18px">
      <el-button type="primary" size="medium" icon="el-icon-refresh" @click="handleList()">刷新列表</el-button>
      <el-button type="primary" size="medium" icon="el-icon-plus" @click="handleAdd()" v-has="'platform_menu_add'">添 加</el-button>
    </div>
    <el-table
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        row-key="id"
        border
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column type="index" label="序号" width="50" fixed="left"></el-table-column>
      <el-table-column prop="name" label="菜单名称" min-width="180" show-overflow-tooltip fixed="left"></el-table-column>
      <el-table-column prop="menuType" label="类型" min-width="80">
        <template slot-scope="scope">
                    <span
                        class="color-btn"
                        :style="
                            scope.row.menuType == 1
                                ? 'background:#409EFF'
                                : scope.row.menuType == 2
                                ? 'background:#FF6600'
                                : 'background:#FFCC00'
                        "
                    >{{ scope.row.menuType == 1 ? '目录' : scope.row.menuType == 2 ? '菜单' : '权限' }}</span
                    >
        </template>
      </el-table-column>
      <el-table-column prop="vuePath" label="地址" min-width="160" show-overflow-tooltip></el-table-column>
      <el-table-column prop="icon" label="图标" min-width="60" show-overflow-tooltip>
        <template slot-scope="scope">
          <i class="iconfont" :class="scope.row.icon" style="font-size: 20px"></i>
        </template>
      </el-table-column>
      <el-table-column prop="sort" label="排序" min-width="60"></el-table-column>
      <el-table-column prop="permission" label="权限标识" min-width="140" show-overflow-tooltip></el-table-column>
      <el-table-column prop="permissionUrl" label="权限url" min-width="120" show-overflow-tooltip></el-table-column>
      <el-table-column fixed="right" label="操作" min-width="180">
        <template slot-scope="scope">
          <el-button type="text" icon="el-icon-plus" @click="handleAdd(scope.row)" v-has="'platform_menu_add'" style="font-size: 15px;">添加</el-button>
          <el-button type="text" icon="el-icon-edit" @click="handleEdit(scope.row)" v-has="'platform_menu_edi'" style="font-size: 15px;">编辑</el-button>
          <el-button type="text" icon="el-icon-delete" @click="handleDel(scope.row)" v-has="'platform_menu_del'" style="font-size: 15px;">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog :title="dialogType == 1 ? '修改' : '新增'" :visible.sync="visible" :close-on-click-modal="false"
               width="600px">
      <add-menu v-if="visible" :dialogType="dialogType" :row="form" @close="visible = false"
                @reload="handleList(tableData.current)"></add-menu>
    </el-dialog>
  </el-card>
</template>

<script>
import {listTreeMenu, deleteMenu} from '@/api/system/menu';
import AddMenu from './add';
import {Message} from 'element-ui';

export default {
  props: {},
  components: {
    AddMenu
  },
  data() {
    return {
      visible: false,
      tableData: [],
      //对话框类型（0新增，1修改）
      dialogType: 0,
      //当前表单数据
      form: {}
    };
  },
  mounted() {
    this.handleList();
  },
  methods: {
    // 添加
    handleAdd(row) {
      this.dialogType = 0;
      this.visible = true;
      this.form = row;
      //增加下一层级
      if (row) {
        this.tableData.forEach((item) => {
          if (item.id === row.parentId) {
            this.form.parentName = item.name;
            return;
          } else {
            if (item.children.length > 0) {
              this.findByParentId(row.parentId, item.children);
            }
          }
        });
      }
    },
    // 修改
    handleEdit(row) {
      this.dialogType = 1;
      this.visible = true;
      this.form = row;
      this.tableData.forEach((item) => {
        if (item.id === row.parentId) {
          this.form.parentName = item.name;
          return;
        } else {
          if (item.children.length > 0) {
            this.findByParentId(row.parentId, item.children);
          }
        }
      });
    },
    // 删除
    handleDel(row) {
      this.$confirm('确认删除该条记录?', '提示', {  
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
          .then(() => {
            deleteMenu(row.id).then((res) => {
              if (res.data.code == 0) {
                Message.success('操作成功');
                this.handleList();
              } else {
                Message.error(res.data.msg);
              }
            });
          })
          .catch(() => {
          });
    },
    // 列表
    handleList() {
      listTreeMenu().then((res) => {
        if (res.data.code == 0) {
          this.tableData = res.data.data;
        } else {
          Message.error(res.data.msg);
        }
      });
    },
    // 调用递归方法,查找当前行的上一级
    findByParentId(parentId, item) {
      item.forEach((children) => {
        if (parentId == children.id) {
          this.form.parentName = children.name;
          return item;
        } else {
          if (children.length > 0) {
            this.findByParentId(parentId, children.children);
          }
        }
      });
      return item;
    }
  }
};
</script>
<style lang="scss" scoped>
/*按钮颜色*/
.color-btn {
  color: #ffffff;
  padding: 5px 10px;
  border-radius: 5px;
}
</style>