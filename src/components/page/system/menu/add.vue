<template>
    <div>
        <el-form :model="form" label-width="100px" :rules="rules" ref="form">
            <el-form-item label="上级菜单">
                <el-input v-model="parentName" disabled></el-input>
            </el-form-item>
            <el-form-item label="类型" prop="menuType">
                <el-radio-group v-model="form.menuType" @change="changeMenuType">
                    <el-radio label="1">目录</el-radio>
                    <el-radio label="2">菜单</el-radio>
                    <el-radio label="3">权限</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="菜单名称" prop="name">
                <el-input v-model.trim="form.name" placeholder="请输入菜单名称"></el-input>
            </el-form-item>
            <!-- 菜单 -->
            <el-form-item label="组件地址" v-show="show == 2">
                <el-input v-model.trim="form.vuePath" placeholder="/xx/xxx||http://"></el-input>
            </el-form-item>
            <el-form-item label="权限url" v-show="show != 1">
                <el-input v-model.trim="form.permissionUrl" placeholder="请输入权限url"></el-input>
            </el-form-item>
            <el-form-item label="权限标识" v-show="show == 3">
                <el-input v-model.trim="form.permission" placeholder="请输入权限标识"></el-input>
            </el-form-item>
            <el-form-item label="图标" v-show="show != 3">
                <el-input placeholder="请选择图标" v-model="form.icon" @focus="iconInputFocus" class="input-with-select">
                    <el-button slot="prepend" @click="iconInputFocus"><i class="iconfont" :class="form.icon" /></el-button>
                </el-input>
            </el-form-item>
            <el-form-item label="排序" prop="sort">
                <el-input-number
                    v-model="form.sort"
                    controls-position="right"
                    :min="1"
                    :max="1000"
                    placeholder="请输入排序"
                    style="width: 100%"
                ></el-input-number>
            </el-form-item>
        </el-form>
        <div style="text-align: center">
            <el-button @click="$emit('close')">取 消</el-button>
            <el-button type="primary" @click="submitForm('form')">确 定</el-button>
        </div>
        <el-dialog title="选择图标" :visible.sync="visible" append-to-body :close-on-click-modal="false" width="500px">
            <el-row :gutter="24">
                <el-col :span="3" v-for="(item, index) in iconList[0].list" :key="index">
                    <div class="icon iconfont" :class="item" @click="selectIcon(item)"></div>
                </el-col>
            </el-row>
        </el-dialog>
    </div>
</template>

<script>
import { saveMenu, updateMenu } from '@/api/system/menu';
import { Message } from 'element-ui';
import { delNullObj } from '@/utils/common.js';
import iconList from '@/api/iconList';

export default {
    props: {
        dialogType: {
            type: Number,
            default: 0
        },
        row: {
            type: Object
        }
    },
    components: {},
    data() {
        //排序验证
        let validatorSort = (rule, value, callback) => {
            if (value && Number.isInteger(value) == false) {
                callback('请输入一个整数类型的值');
            } else {
                callback();
            }
        };
        return {
            iconList: iconList,
            form: {
                parentId: '',
                name: '',
                menuType: '1',
                vuePath: '',
                sort: '',
                permission: '',
                permissionUrl: '',
                icon: ''
            },
            parentName: '无',
            show: 1,
            rules: {
                name: [{ required: true, message: '请输入菜单名称', trigger: 'blur' }],
                menuType: [{ required: true, message: '请选择类型', trigger: 'change' }],
                sort: [
                    { validator: validatorSort, trigger: 'blur' },
                    { required: true, message: '请输入排序', trigger: 'blur' }
                ]
            },
            visible: false
        };
    },
    mounted() {
        //新增
        if (this.dialogType == 0) {
            if (this.row) {
                let { id } = this.row;
                this.form.parentId = id;
                this.parentName = this.row.parentName ? this.row.parentName : '无';
            }
        }
        //修改
        else {
            let { parentId, name, menuType, vuePath, sort, permission, permissionUrl, icon } = this.row;
            this.show = menuType;
            this.form = {
                parentId,
                name,
                menuType,
                vuePath,
                sort,
                permission,
                permissionUrl,
                icon
            };
            this.parentName = this.row.parentName ? this.row.parentName : '无';
        }
    },
    methods: {
        //表单提交
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let values = Object.assign({}, this.form);
                    if (this.dialogType == 0) {
                        // 添加
                        saveMenu(delNullObj(values)).then((res) => {
                            if (res.data.code == 0) {
                                Message.success('操作成功');
                                this.$emit('close');
                                this.$emit('reload');
                            } else {
                                Message.error(res.data.msg);
                            }
                        });
                    } else {
                        values.id = this.row.id;
                        updateMenu(delNullObj(values)).then((res) => {
                            if (res.data.code == 0) {
                                Message.success('操作成功');
                                this.$emit('close');
                                this.$emit('reload');
                            } else {
                                Message.error(res.data.msg);
                            }
                        });
                    }
                }
            });
        },
        //切换类型
        changeMenuType(value) {
            this.show = value;
        },
        // 图标文本框聚焦
        iconInputFocus() {
            this.visible = true;
        },
        // 选择图标
        selectIcon(item) {
            this.form.icon = item;
            this.visible = false;
        }
    }
};
</script>
<style scoped>
.icon {
    font-size: 26px;
    padding-top: 20px;
}

.icon:hover {
    cursor: pointer;
    transform: scale(1.2);
}
</style>