<template>
    <div>
        <el-card shadow="never">
            <div slot="header" class="clearfix">
                <span>下载中心</span>
            </div>
            <el-form :inline="true" :model="Form" class="demo-form-inline">
                <el-form-item label="导出类型">
                    <el-select v-model="Form.type" placeholder="请选择" filterable clearable>
                        <el-option v-for="item in exportType" :key="item.name" :label="item.name" :value="item.name"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="导出状态">
                    <el-select v-model="Form.status" placeholder="请选择" filterable clearable>
                        <el-option v-for="item in exportStatus" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="生成时间">
                    <el-date-picker
                        v-model="value1"
                        type="daterange"
                        range-separator="~"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        clearable
                        value-format="yyyy-MM-dd"
                        @change="toChoseTime"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-button type="primary" @click="handleSearch" icon="el-icon-search" size="small">查询</el-button>
            </el-form>
            <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                <el-table-column type="index" label="序号" width="50" fixed="left"></el-table-column>
                <el-table-column prop="name" label="导出名称" show-overflow-tooltip width="500"></el-table-column>
                <el-table-column prop="subjectId" label="导出类型"></el-table-column>
                <el-table-column label="导出状态" width="100">
                    <template slot-scope="scope">
                        {{ getTypeStr(scope.row.type) }}
                    </template>
                </el-table-column>
                <el-table-column prop="updateTime" label="导出时间" width="220"> </el-table-column>
                <el-table-column prop="createTime" label="生成时间" show-overflow-tooltip width="220"></el-table-column>
                <el-table-column label="操作" fixed="right" width="100">
                    <template slot-scope="scope">
                        <template v-if="scope.row.type != 1">
                            <el-button @click="handleDownload(scope.row.id, scope.row.url)" type="text" :disabled="scope.row.type == 3"
                                >下载</el-button
                            >
                            <el-button @click="handleDel(scope.row.id)" type="text" style="color: red">删除</el-button>
                        </template>
                        <template v-else>
                            <el-button type="text">---</el-button>
                        </template>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page="tableData.current"
                    layout="prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
        </el-card>
    </div>
</template>

<script>
import { Message } from 'element-ui';
import { reqGetDownList, downLoadTimeUpdate, delFileById } from '@/api/system/user.js';
export default {
    computed: {},
    data() {
        return {
            Form: {
                current: 1,
                size: 10,
                startTime: '',
                endTime: '',
                type: '',
                status: ''
            },
            password: '',
            tableData: {},
            current: 1,
            size: 10,
            visible: false,
            resetVisible: false,
            distributeVisible: false,
            form: {},
            dialogType: 0,
            manageVisible: false,
            userId: '',
            backDoor: false,
            qrcode: {},
            dialogVisible: false,
            peopleNum: 0,
            exportType: [
                {
                    name: '流水账单'
                },
                {
                    name: '发放记录'
                },
                {
                    name: '付款批次'
                }
            ],
            exportStatus: [
                {
                    name: '导出中',
                    id: 1
                },
                {
                    name: '已完成',
                    id: 2
                },
                {
                    name: '已失效',
                    id: 3
                }
            ],
            value1: []
        };
    },
    created() {
        if (this.getQueryString('backDoor') == 'xudong') {
            this.backDoor = true;
        }
    },
    mounted() {
        this.handleList();
    },
    methods: {
        getTypeStr(type) {
            let nameStr = '';
            this.exportStatus.forEach((v) => {
                if (v.id == type) {
                    nameStr = v.name;
                }
            });
            return nameStr;
        },
        getQueryString(name) {
            return (
                decodeURIComponent(
                    (new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || [, ''])[1].replace(/\+/g, '%20')
                ) || null
            );
        },
        toChoseTime(val) {
            if (val) {
                this.Form.startTime = val[0];
                this.Form.endTime = val[1];
            } else {
                this.Form.startTime = '';
                this.Form.endTime = '';
            }
        },
        // 添加
        handleAdd() {
            this.dialogType = 0;
            this.visible = true;
        },
        // 删除
        async handleDel(id) {
            let res = await this.$confirm(`确定要删除文件吗？`, '提示', {
                type: 'warning'
            });
            if (res) {
                let result = await delFileById(id);
                if (result.data.code != 0) return false;
                this.$message.success('删除成功！');
                this.handleList();
            }
        },
        //下载这个文件
        async handleDownload(id, url) {
            let res = await downLoadTimeUpdate(id);
            if (res.data.code == 0) {
                window.open(url);
            }
            this.handleList();
        },
        // 用户列表
        handleList() {
            reqGetDownList(this.Form).then((res) => {
                if (res.data.code == 0) {
                    this.tableData = res.data.data;
                } else {
                    Message.error(res.data.msg);
                }
            });
        },
        // 查询
        handleSearch() {
            this.Form.current = 1;
            this.handleList();
        },
        //分页-每页条数改变
        handleSizeChange(size) {
            this.Form.size = size;
            this.Form.current = 1;
            this.handleList();
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.Form.current = current;
            this.handleList();
        }
    }
};
</script>

<style scoped lang="scss">
.btn {
    color: #ffffff;
    padding: 5px 10px;
    border-radius: 5px;
}
.item-number {
    margin-left: 6px;
}
</style>
