<template>
    <div>
        <el-dialog title="新增信息" :visible.sync="dialogTableVisible" @open="openDia" @close="closeDia" top="8vh">
            <el-form label-width="120px">
                <el-form-item label="推送对象">
                    <el-select v-model="form.type" style="width: 250px">
                        <el-option v-for="item in typeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="通知标题">
                    <el-input style="width: 250px" v-model="form.title" placeholder="请输入通知标题" maxlength="30"></el-input>
                </el-form-item>
                <el-form-item label="通知内容">
                    <!-- <el-input style="width: 250px" type="textarea" v-model="form.content" placeholder="请输入通知内容"></el-input> -->
                    <wangEditor v-model="wangEditorDetail" :isClear="isClear" @change="wangEditorChange" style="height: 500px"></wangEditor>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeDia">取 消</el-button>
                <el-button type="primary" @click="previewMessage">预 览</el-button>
                <el-button type="primary" @click="toAddInfoPeople">确 定</el-button>
            </span>
        </el-dialog>

        <!-- 预览弹窗 -->
        <el-dialog :visible.sync="previewDialogVisible" top="10vh" :close-on-click-modal="false" :show-close="true">
            <div slot="title" style="display: flex; justify-content: center; align-items: center;">
                <span style="font-size: 25px; font-weight: 600;">通 知</span>
            </div>
            <div style="margin: -20px 20px 0 20px;">
                <div>
                    <span style="font-size: 20px">{{ form.title }}</span>
                </div>
                <div style="margin-top: 10px;">
                    <div v-html="form.content"></div>
                </div>
                <div style="font-size:12px;text-align: right; margin-top: 10px;">
                    通知时间: {{ currentTime }}
                </div>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="previewDialogVisible = false">我知道了</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import wangEditor from '@/components/common/wangEditor';
export default {
    components: {
        wangEditor
    },
    data() {
        return {
            isClear: false, //设置为true的时候，这个可以用this.wangEditorDetail=''来替代
            wangEditorDetail: '',
            checkAll: false,
            checkedCities: [],
            cities: [],
            isIndeterminate: false,
            dialogTableVisible: false,
            groupInfoOption: [],
            form: {},
            typeList: [
                {
                    id: 0,
                    name: '全平台'
                },
                {
                    id: 1,
                    name: '税源地'
                },
                {
                    id: 2,
                    name: '企业'
                },
                {
                    id: 3,
                    name: '渠道'
                }
            ],
            previewDialogVisible: false,
            currentTime: ''
        };
    },
    props: {
        showAddDia: {
            type: Boolean,
            require: true
        },
        groupInfoShow: {
            type: Array,
            require: true
        }
    },
    watch: {
        showAddDia(val) {
            this.dialogTableVisible = val;
            this.isClear = val;
        },
        groupInfoShow(val) {
            this.groupInfoOption = val;
            this.groupInfoOption.forEach((s) => {
                if (s.workerList) {
                    s.workerList.forEach((v) => {
                        v.groupId = s.groupId;
                    });
                }
            });
            console.log(this.groupInfoOption);
        }
    },
    methods: {
        toAddInfoPeople() {
            this.$emit('toAddPeople', this.form);
        },
        closeDia() {
            this.$emit('closeDia');
            this.form = {};
        },
        openDia() {
            this.form = {};
        },
        wangEditorChange(val) {
            this.form.content = val;
        },
        previewMessage() {
            if (!this.form.title || !this.form.content) {
                this.$message.warning('请填写完整的通知标题和内容');
                return;
            }
            this.currentTime = new Date().toLocaleString();
            this.previewDialogVisible = true;
        }
    }
};
</script>

<style scoped>
::v-deep .el-divider--horizontal {
    margin: 8px 0 15px 0;
}
.group-item {
    margin-bottom: 8px;
}
.group-list {
    margin-bottom: 12px;
}
.info_box span {
    margin: 0 5px;
}
</style>