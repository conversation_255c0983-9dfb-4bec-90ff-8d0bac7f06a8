<template>
    <div>
        <el-card class="left" shadow="never">
            <div slot="header" class="clearfix">
                <span>站内信管理</span>
            </div>
            <el-button type="primary" @click="toAddNewMessage" style="margin-bottom: 20px" v-has="'platform_message_manage'" size="medium"
                >新增推送</el-button
            >
            <el-table :data="tableData.records" style="width: 100%; color: #5B5B5B" header-row-class-name="table_header" header-cell-class-name="table_header_cell" cell-class-name="table_cell">
                <el-table-column prop="createTime" label="发送时间" show-overflow-tooltip></el-table-column>
                <el-table-column prop="title" label="标题"></el-table-column>
                <el-table-column label="推送对象" show-overflow-tooltip>
                    <template slot-scope="scope">
                        {{ getTypeName(scope.row.type) }}
                    </template>
                </el-table-column>
                <el-table-column label="推送内容" min-width="200">
                    <template slot-scope="scope">
                        <div class="anwerContentLine">
                            {{ contentHtml(scope.row.content) }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="操作" prop="waitAuditNumber">
                    <template slot-scope="scope">
                        <el-button @click="lookDetail(scope.row.id)" type="text" icon="el-icon-edit" style="font-size: 15px;">详情</el-button>
                        <!-- <el-button @click="detelDetail(scope.row.id)" type="text" icon="el-icon-delete" v-has="'platform_message_manage'"
                            >删除</el-button
                        > -->
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page="tableData.current"
                    layout="prev, pager, next, jumper"
                    :total="tableData.total"
                ></el-pagination>
            </div>
        </el-card>
        <el-dialog title="通知详情" :visible.sync="dialogFormVisible" top="10vh">
            <el-form>
                <el-form-item label="发送时间：" label-width="140px">
                    {{ messageInfo.createTime }}
                </el-form-item>
                <el-form-item label="推送对象：" label-width="140px">
                    {{ getTypeName(messageInfo.type) }}
                </el-form-item>
                <el-form-item label="通知标题：" label-width="140px">
                    {{ messageInfo.title }}
                </el-form-item>
                <el-form-item label="通知内容：" label-width="140px">
                    <div v-html="messageInfo.content"></div>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="dialogFormVisible = false">关闭</el-button>
            </div>
        </el-dialog>
        <addMessage :showAddDia="showDia" @closeDia="closeDia" @toAddPeople="toAddMessage"></addMessage>
    </div>
</template>

<script>
import { messageList, messageDetail, addMsg, cancelMsg } from '@/api/message/message.js';
import addMessage from '../components/addMessage.vue';
export default {
    data() {
        return {
            tableData: {},
            current: 1,
            size: 10,
            dialogFormVisible: false,
            messageInfo: {},
            showDia: false,
            typeList: [
                {
                    id: 0,
                    name: '全平台'
                },
                {
                    id: 1,
                    name: '税源地'
                },
                {
                    id: 2,
                    name: '企业'
                },
                {
                    id: 3,
                    name: '渠道'
                }
            ]
        };
    },
    components: {
        addMessage
    },
    methods: {
        //分页-每页条数改变
        handleSizeChange(size) {
            this.size = size;
            this.current = 1;
            this.getMessageList();
        },
        //分页-当前页改变
        handleCurrentChange(current) {
            this.current = current;
            this.getMessageList(current);
        },
        getMessageList() {
            messageList({
                current: this.current,
                size: this.size
            }).then((res) => {
                this.tableData = res.data.data;
            });
        },
        contentHtml(content, index) {
            // 富文本编辑器的内容如何只获得文字去掉标签
            content = content.replace(/<[^>]+>/g, '');
            // 在上面的基础上还去掉了换行<br/>
            content = content.replace(/<[^>]+>/g, '').replace(/(\n)/g, '');
            // 请注意，我这个是自定义全局方法，用于超过字数显示省略号的...
            // 之前文章有写过，下面也放上了链接，自行查看
            return content;
        },
        getTypeName(type) {
            let tyepeName = '';
            this.typeList.map((s) => {
                if (s.id == type) {
                    tyepeName = s.name;
                }
            });
            return tyepeName;
        },
        lookDetail(id) {
            messageDetail({
                id
            }).then((res) => {
                if (res.data.code == 0) {
                    this.messageInfo = res.data.data;
                    this.dialogFormVisible = true;
                }
            });
        },
        toAddNewMessage() {
            this.showDia = true;
        },
        closeDia() {
            this.showDia = false;
        },
        toAddMessage(e) {
            addMsg(e).then((res) => {
                if (res.data.code == 0) {
                    this.current = 1;
                    this.getMessageList();
                    this.showDia = false;
                }
            });
        },
        detelDetail(id) {
            this.$confirm(`确定要删除该消息吗？`, '提示', {
                type: 'warning'
            })
                .then(() => {
                    cancelMsg({
                        id: id
                    }).then((res) => {
                        if (res.data.code == 0) {
                            this.getMessageList();
                        }
                    });
                })
                .catch(() => {});
        }
    },
    created() {
        this.getMessageList();
    }
};
</script>

<style scoped>
.anwerContentLine {
    max-width: 600px;
    white-space: nowrap;
    overflow: hidden;
    margin-right: 20px;
    text-overflow: ellipsis;
    line-height: 100%;
}
</style>