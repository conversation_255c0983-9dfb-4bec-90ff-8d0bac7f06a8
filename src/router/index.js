import Vue from 'vue';
import Router from 'vue-router';
import Home from '../components/common/Home.vue';

Vue.use(Router);

export default new Router({
    mode: 'hash',
    routes: [
        {
            path: '/',
            redirect: '/dashboard'
        },
        {
            path: '/',
            component: Home,
            meta: { title: '后台管理' },
            children: [
                {
                    path: '/dashboard',
                    component: () => import('../components/page/welcome.vue'),
                    meta: { title: '系统首页' }
                },
                {
                    path: '/tabs',
                    component: () => import('../components/page/Tabs.vue'),
                    meta: { title: 'tab选项卡' }
                },
                {
                    path: '/404',
                    component: () => import('../components/page/404.vue'),
                    meta: { title: '404' }
                },
                {
                    path: '/system/user/profile',
                    component: () => import('../components/page/system/profile/index.vue'),
                    meta: { title: '个人中心' }
                },
                {
                    path: '/system/dictionary',
                    component: () => import('../components/page/system/dictionary/index.vue'),
                    meta: { title: '字典类型' }
                },
                {
                    path: '/system/dictlist',
                    component: () => import('../components/page/system/dictionary/dictList.vue'),
                    meta: { title: '字典项' }
                },

                {
                    path: '/system/role',
                    component: () => import('../components/page/system/role/index.vue'),
                    meta: { title: '角色管理' }
                },
                {
                    path: '/system/menu',
                    component: () => import('../components/page/system/menu/index.vue'),
                    meta: { title: '菜单管理' }
                },
                {
                    path: '/system/user',
                    component: () => import('../components/page/system/user/index.vue'),
                    meta: { title: '用户管理' }
                },
                {
                    path: '/branch/tax',
                    component: () => import('../components/page/branch/tax/tax.vue'),
                    meta: { title: '税源地管理' }
                },
                {
                    path: '/branch/taxDetail',
                    component: () => import('../components/page/branch/tax/detail.vue'),
                    meta: { title: '税源地详情' }
                },
                {
                    path: '/branch/channel',
                    component: () => import('../components/page/branch/channel/channel.vue'),
                    meta: { title: '渠道管理' }
                },
                {
                    path: '/branch/channelDetail',
                    component: () => import('../components/page/branch/channel/detail.vue'),
                    meta: { title: '渠道详情' }
                },
                {
                    path: '/branch/enterprise',
                    component: () => import('../components/page/branch/enterprise/enterprise.vue'),
                    meta: { title: '企业管理' }
                },
                {
                    path: '/branch/enterpriseDetail',
                    component: () => import('../components/page/branch/enterprise/detail.vue'),
                    meta: { title: '企业详情' }
                },
                {
                    path: '/account/recharge',
                    component: () => import('../components/page/account/recharge/index.vue'),
                    meta: { title: '充值记录' }
                },
                {
                    path: '/account/bankEnterprise',
                    component: () => import('../components/page/account/bankEnterprise/index.vue'),
                    meta: { title: '银企直连到账记录' }
                },
                {
                    path: '/account/balance',
                    component: () => import('../components/page/account/balance/index.vue'),
                    meta: { title: '商户余额' }
                },
                {
                    path: '/account/balance/detail',
                    component: () => import('../components/page/account/balance/detail.vue'),
                    meta: { title: '商户详情' }
                },
                {
                    path: '/account/dailyBalance',
                    component: () => import('../components/page/account/dailyBalance/index.vue'),
                    meta: { title: '每日对账' }
                },
                {
                    path: '/account/invoicing',
                    component: () => import('../components/page/account/invoicing/index.vue'),
                    meta: { title: '开票记录' }
                },
                {
                    path: '/account/turnover',
                    component: () => import('../components/page/account/turnover/index.vue'),
                    meta: { title: '流水账单' }
                },
                {
                    path: '/personnel/roster',
                    component: () => import('../components/page/personnel/roster/index.vue'),
                    meta: { title: '人员名单' }
                },
                {
                    path: '/personnel/signing',
                    component: () => import('../components/page/personnel/signing/index.vue'),
                    meta: { title: '签约管理' }
                },
                {
                    path: '/system/message',
                    component: () => import('../components/page/message/letter/index.vue'),
                    meta: { title: '站内信息管理' }
                },
                {
                    path: '/project/list',
                    component: () => import('../components/page/project/list/index.vue'),
                    meta: { title: '项目列表' }
                },
                {
                    path: '/project/detail',
                    component: () => import('../components/page/project/detail/index.vue'),
                    meta: { title: '项目详情' }
                },
                {
                    path: '/project/update_req_list',
                    component: () => import('../components/page/project/update_req_list/index.vue'),
                    meta: { title: '项目变更申请列表' }
                },
                {
                    path: '/grant/list',
                    component: () => import('../components/page/grant/list/index.vue'),
                    meta: { title: '发放记录' }
                },
                {
                    path: '/grant/initiate',
                    component: () => import('../components/page/grant/initiate/index.vue'),
                    meta: { title: '发起付款' },
                    name: 'initiate'
                },
                {
                    path: '/grant/batch',
                    component: () => import('../components/page/grant/batch/index.vue'),
                    meta: { title: '付款批次' }
                },
                {
                    path: '/grant/batch_detail',
                    component: () => import('../components/page/grant/batch/batch_detail.vue'),
                    meta: { title: '批次详情' }
                },
                {
                    path: '/grant/sensitive',
                    component: () => import('../components/page/grant/sensitive/index.vue'),
                    meta: { title: '敏感词管理' }
                },
                {
                    path: '/grant/batch_apply',
                    component: () => import('../components/page/grant/batch/batch_apply.vue'),
                    meta: { title: '审核详情' }
                },
                {
                    path: '/monitor/real',
                    component: () => import('../components/page/Dashboard.vue'),
                    meta: { title: '实时监控' }
                },
                {
                    path: '/monitor/month',
                    component: () => import('../components/page/charts/monthChart.vue'),
                    meta: { title: '月度分析' }
                },
                {
                    path: '/monitor/monthv2',
                    component: () => import('../components/page/charts/monthChartV2.vue'),
                    meta: { title: '月度分析' }
                },
                {
                    path: '/account/channel',
                    component: () => import('../components/page/account/channel/channel.vue'),
                    meta: { title: '渠道提成' }
                },
                {
                    path: '/account/channelDetail',
                    component: () => import('../components/page/account/channel/detail.vue'),
                    meta: { title: '渠道提成' }
                },
                {
                    path: '/account/withdrawalRecords',
                    component: () => import('../components/page/account/withdrawal/index.vue'),
                    meta: { title: '商户提现记录' }
                },
                {
                    path: '/system/personnel',
                    component: () => import('../components/page/system/pushPerson/index.vue'),
                    meta: { title: '充值推送人员管理' }
                },
                {
                    path: '/system/downloadCenter',
                    component: () => import('../components/page/system/downCenter/index.vue'),
                    meta: { title: '下载中心' }
                },
                {
                    path: '/grant/initiateOffline',
                    component: () => import('../components/page/grant/initiate/index.vue'),
                    meta: { title: '线下发放' }
                },
                {
                    path: '/account/applyMoney',
                    component: () => import('../components/page/branch/enterprise/components/applyMoney.vue'),
                    meta: { title: '充值申请' }
                },
                {
                    path: '/black',
                    component: () => import('../components/page/black.vue')
                },
                {
                    path: '/onboarding/new',
                    component: () => import('../components/page/onboarding/index.vue'),
                    meta: { title: '推荐客户'}
                },
                {
                    path: '/onboarding/list',
                    component: () => import('../components/page/onboarding/list.vue'),
                    meta: { title: '进件记录'}
                },
                {
                    path: '/onboarding/detail',
                    component: () => import('../components/page/onboarding/detail.vue'),
                    meta: { title: '进件详情'}
                },
                {
                    path: '/onboarding/edit',
                    component: () => import('../components/page/onboarding/edit.vue'),
                    meta: { title: '进件信息编辑'}
                },
                {
                    path: '/onboarding/review',
                    component: () => import('../components/page/onboarding/detail.vue'),
                    meta: { title: '进件审核' }
                },
                {
                    path: '/risk-control/signingrate',
                    component: () => import('../components/page/riskControl/signingrate/index.vue'),
                    meta: { title: '签约率管理' }
                },
                {
                    path: '/risk-control/operation',
                    component: () => import('../components/page/riskControl/operation/index.vue'),
                    meta: { title: '运营风控管理' }
                },
                {
                    path: '/risk-control/finance',
                    component: () => import('../components/page/riskControl/finance/index.vue'),
                    meta: { title: '财务风控材料' }
                },
                {
                    path: '/risk-control/iuuse',
                    component: () => import('../components/page/riskControl/issue/index.vue'),
                    meta: { title: '发放风控管理' }
                },
                {
                    path: '/contract/list',
                    component: () => import('../components/page/contract/index.vue'),
                    meta: { title: '合约列表' }
                },
                {
                    path: '/contract/detail',
                    component: () => import('../components/page/contract/detail.vue'),
                    meta: { title: '合约详情' }
                },
                {
                    path: '/account/ticket_apply',
                    component: () => import('../components/page/branch/enterprise/components/ticket_apply.vue'),
                    meta: { title: '开票申请' }
                },
            ]
        },
        {
            path: '/login',
            component: () => import('../components/page/Login.vue'),
            meta: { title: '登录' }
        },
        {
            path: '*',
            redirect: '/404'
        }
    ]
});

