* {
    margin: 0;
    padding: 0;
}

html,
body,
#app,
.wrapper {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

body {
    /* font-family: 'PingFang SC', "Helvetica Neue", Helvetica, "microsoft yahei", arial, STHeiTi, sans-serif; */
    margin: 0;
    color: rgba(0,0,0,.65);
    font-size: 14px;
    font-family: -apple-system,BlinkMacSystemFont,Segoe UI,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol;
    font-variant: tabular-nums;
    line-height: 1.5;
    background-color: #fff;
    -webkit-font-feature-settings: "tnum";
    font-feature-settings: "tnum"
}

.money {
    font-family: SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;
    font-weight: 600;
    white-space: nowrap;
}
.money_normal {
    font-family: SFMono-Regular,<PERSON><PERSON>,Monaco,<PERSON><PERSON><PERSON>,Liberation Mono,Courier New,monospace;
    font-weight: 400;
    white-space: nowrap;
}
.table_header {
    color: #242424!important;
}

.table_cell {
    padding:20px 0!important;
    border-bottom:1px solid #E8E8E8!important;
    font-size: 14px!important;
}

.table_header_cell {
    border-bottom: 1px solid #E8E8E8!important;
    padding:15px 0!important;
    color: #242424!important;
    font-size: 16px!important;
}

.el-table__body .hover-row .el-table__cell {
    background-color: #E6F6FF!important;
}

.el-button--medium {
    font-size: 15px!important;
    padding: 8px 18px!important;
}

a {
    text-decoration: none
}

/* .content-box:not(.el-menu--collapse) {
    left: 64px;
} */

.content-box {
    position: relative;
    height: 93%;
    /* left: 210px; */
    width: 100%;
    right: 0;
    top: 0;
    padding-bottom: 30px;
    -webkit-transition: left .3s ease-in-out;
    transition: left .3s ease-in-out;
    background: #ecf0f5;
}
/* 
.sidebar:not(.el-menu--collapse) {
    width: 64px;
} */

.content {
    width: auto;
    height: 100%;
    padding: 10px;
    overflow-y: scroll;
    box-sizing: border-box;
}

.content-collapse {
    left: 65px;
}

.container {
    padding: 30px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.crumbs {
    margin: 10px 0;
}

.el-table th {
    background-color: #fafafa !important;
}

.pagination {
    margin: 20px 0;
    text-align: right;
}

.pagination .el-pager li {
    font-size: 14px;
    font-weight: 400;
    border: 1px solid #d9d9d9;
    border-radius: 5px;
    margin: 0 5px;
    color: rgba(0,0,0,.65);
    height: 30px;
}

.el-pager li.active+li {
    border-left: 1px solid #d9d9d9!important;
}

.el-pager li.active {
    border-color: #1890ff;
    color: #1890ff;
}

.el-pagination .btn-next,.el-pagination .btn-prev {
    border: 1px solid #d9d9d9;
    border-radius: 5px;
    color: rgba(0,0,0,.65);
    height: 30px;
    width: 30px;
    text-align: center;
    margin: 0 5px!important;
    padding: 0 6px;
    font-size: 14px;
    font-weight: 400;
}

.el-pager li.more {
    border: 0;
    color: rgba(0,0,0,.65)!important;
}

.el-pagination .btn-prev {
    padding-right: 0!important;
    padding-inline: 0;
}

.el-pagination .btn-next {
    padding-left: 0!important;
    padding-inline: 0;
}

.el-pagination__editor.el-input {
    margin: 0 5px;
}

.plugins-tips {
    padding: 20px 10px;
    margin-bottom: 20px;
}

.el-button + .el-tooltip {
    margin-left: 10px;
}

.el-table tr:hover {
    background: #f6faff;
}

.mgb20 {
    margin-bottom: 20px;
}

.move-enter-active,
.move-leave-active {
    transition: opacity .5s;
}

.move-enter,
.move-leave {
    opacity: 0;
}

/*BaseForm*/

.form-box {
    width: 600px;
}

.form-box .line {
    text-align: center;
}

.el-time-panel__content::after,
.el-time-panel__content::before {
    margin-top: -7px;
}

.el-time-spinner__wrapper .el-scrollbar__wrap:not(.el-scrollbar__wrap--hidden-default) {
    padding-bottom: 0;
}

/*Upload*/

.pure-button {
    width: 150px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    color: #fff;
    border-radius: 3px;
}

.g-core-image-corp-container .info-aside {
    height: 45px;
}

.el-upload--text {
    background-color: #fff;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    box-sizing: border-box;
    width: 360px;
    height: 180px;
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.el-upload--text .el-icon-upload {
    font-size: 67px;
    color: #97a8be;
    margin: 40px 0 16px;
    line-height: 50px;
}

.el-upload--text {
    color: #97a8be;
    font-size: 14px;
    text-align: center;
}

.el-upload--text em {
    font-style: normal;
}

/*VueEditor*/

.ql-container {
    min-height: 400px;
}

.ql-snow .ql-tooltip {
    transform: translateX(117.5px) translateY(10px) !important;
}

.editor-btn {
    margin-top: 20px;
}

/*markdown*/

.v-note-wrapper .v-note-panel {
    min-height: 500px;
}