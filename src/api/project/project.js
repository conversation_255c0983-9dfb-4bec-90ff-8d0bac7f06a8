import request from '../http.js';
import { CONSOLE_BASE_URL, LOCAL_BASE_URL } from '../config';
const NOW_BASE_URL = LOCAL_BASE_URL;
// 项目列表
export const projectList = (data) => {
    return request({
        url: NOW_BASE_URL + `platform/project/listProject`,
        method: 'post',
        data
    });
};
//项目新增
export const projectAdd = (data) => {
    return request({
        url: NOW_BASE_URL + `platform/project/insertProject?enterpriseId=${data.enterpriseId}`,
        method: 'post',
        data
    });
};
//项目基本详情
export const projectInfoDetail = (data) => {
    return request({
        url: NOW_BASE_URL + `platform/project/getProjectInfo?projectId=${data.projectId}`,
        method: 'get'
    });
};
//修改项目
export const projectUpdata = (data) => {
    let parmas = data;
    return request({
        url: NOW_BASE_URL + `platform/project/updateProject?projectId=${data.projectId}`,
        method: 'post',
        data
    });
};
//项目详情
export const projectDetail = (data) => {
    return request({
        url: NOW_BASE_URL + `platform/project/getProjectDetail?projectId=${data}`,
        method: 'get',
        data,
        headers:{
            isLoading:false
        }
    });
};
//项目小组添加
export const projectGroupAdd = (data) => {
    return request({
        url: NOW_BASE_URL + `platform/project/insertGroup`,
        method: 'post',
        data
    });
};
//项目小组修改
export const projectGroupEdit = (data) => {
    return request({
        url: NOW_BASE_URL + `platform/project/updateGroup`,
        method: 'post',
        data
    });
};
//查询管理员列表
export const projectAdmin = (data) => {
    return request({
        url: NOW_BASE_URL + `platform/project/listAdmin?enterpriseId=${data.enterpriseId}`,
        method: 'get'
    });
};
//选择管理员列表
export const choseAdmin = (data) => {
    return request({
        url: NOW_BASE_URL + `platform/project/addAdminForProject`,
        method: 'post',
        data
    });
};
//小组解散
export const deleteGroup = (data) => {
    return request({
        url: NOW_BASE_URL + `platform/project/deleteGroup`,
        method: 'post',
        data
    });
};
//删除管理员
export const deleteAdmin = (data) => {
    return request({
        url: NOW_BASE_URL + `platform/project/deleteAdminInProject?projectId=${data.projectId}&userId=${data.userId}`,
        method: 'delete'
    });
};
//获取二维码
export const groupQrcode = (data) => {
    return request({
        url: NOW_BASE_URL + `platform/qrCode/getCode?sceneId=${data.sceneId}`,
        method: 'post',
        data
    });
};
//删除项目
export const deleteProject = (data) => {
    return request({
        url: NOW_BASE_URL + `platform/project/deleteProject`,
        method: 'delete',
        data
    });
};
//删除项目
export const deleteWorker = (data) => {
    return request({
        url: NOW_BASE_URL + `platform/project/deleteWorker?groupId=${data.groupId}&workerId=${data.workerId}`,
        method: 'delete'
    });
};
//项目导出
export const excelProject = (data) => {
    return request({
        url: NOW_BASE_URL + `platform/project/projectXlsOut?projectId=${data.projectId}`,
        method: 'get',
        responseType: 'blob'
    });
};
//税源地列表
export const getTaxesList = (data) => {
    return request({
        url: NOW_BASE_URL + `platform/project/getTaxes?enterpriseId=${data.enterpriseId}`,
        method: 'get'
    });
};
//工作组名单下载
export const downloadGroupWorkers = (data) => {
    return request({
        url: NOW_BASE_URL + `platform/worker/downloadGroupWorkers?groupId=${data.groupId}`,
        method: 'post',
        data,
        responseType: 'blob'
    });
};
//待审核列表
export const listWaitWorker = (data) => {
    return request({
        url: NOW_BASE_URL + `platform/project/listWaitWorker?groupId=${data.groupId}&search=${data.search}`,
        method: 'get',
        data
    });
};
//审核用户
export const updateWorkerStatus = (data) => {
    return request({
        url: NOW_BASE_URL + `platform/project/updateWorkerStatus`,
        method: 'post',
        data
    });
};
//获取企业列表
export const proEnterpriseList = (data) => {
    return request({
        url: NOW_BASE_URL + `platform/project/enterpriseList?search=`,
        method: 'get',
    });
};

export const pageWaitWorkersByProjectId = (data) => {
    return request({
        url: NOW_BASE_URL + `platform/project/pageWaitWorkersByProjectId?projectId=${data.projectId}&page=${data.current}&size=${data.size}`,
        method: 'get',
    });
}

export const isShowCrowd = (data) => {
    return request({
        url: NOW_BASE_URL + `platform/project/isShowCrowd?projectId=${data.projectId}&enterpriseId=${data.enterpriseId}`,
        method: 'get',
    });
}

// 根据发放批次ID批量获取项目开票类目
export const getProjectInvoiceCategoryByIds = (data) => {
    return request({
        url: NOW_BASE_URL + `platform/project/getProjectInvoiceCategoryBySalaryIds`,
        method: 'post',
        data
    });
};

// 分页查询项目变更请求列表
export const pageQueryProjectUpdateRequests = (data) => {
    return request({
        url: NOW_BASE_URL + `platform/project/pageQueryProjectUpdateRequests`,
        method: 'post',
        data
    });
};

// 审核项目变更申请
export const auditUpdateRequest = (data) => {
    return request({
        url: NOW_BASE_URL + `platform/project/auditUpdateRequest`,
        method: 'post',
        data
    });
};
