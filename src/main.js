import Vue from 'vue';
import App from './App.vue';
import router from './router';
import ElementUI from 'element-ui';
import VueI18n from 'vue-i18n';
import auth from '@/api/auth.js';
import { messages } from './components/common/i18n';
import 'element-ui/lib/theme-chalk/index.css'; // 默认主题
// import './assets/css/theme-green/index.css'; // 浅绿色主题
import './assets/css/icon.css';
import './assets/css/theme/variables.css'; // 主题变量
import 'babel-polyfill';
import store from './store/index';
import { iconfontUrl, iconfontVersion } from '@/config/env';
import { loadStyle } from '@/utils/common.js';
import { initTheme } from '@/utils/theme.js'; // 导入主题初始化函数
import VueAMap from 'vue-amap';
import * as echarts from 'echarts';
import dataV from '@jiaminghi/data-view';
import saveCode from '@/components/common/saveCode.js';
import exportDia from '@/components/common/exportDia.js'
Vue.use(dataV);
Vue.use(VueAMap);
Vue.use(saveCode);
Vue.use(exportDia);
Vue.prototype.$echarts = echarts;
VueAMap.initAMapApiLoader({
    // 高德的key
    // key: '69429b465411ff676ffc505d2651b724',
    key: 'd504507d6e87b2095861a650c2c911d4',
    // 插件集合
    plugin: [
        'AMap.Geocoder',
        'AMap.Geolocation',
        'AMap.Autocomplete',
        'AMap.PlaceSearch',
        'AMap.Scale',
        'AMap.OverView',
        'AMap.ToolBar',
        'AMap.MapType',
        'AMap.PolyEditor',
        'AMap.CircleEditor'
    ],
    // 高德 sdk 版本，默认为 1.4.4
    v: '1.4.4'
});
Vue.config.productionTip = false;
Vue.use(VueI18n);
Vue.use(ElementUI, {
    size: 'small'
});
const i18n = new VueI18n({
    locale: 'zh',
    messages
});

//使用钩子函数对路由进行权限跳转
router.beforeEach((to, from, next) => {
    document.title = `${to.meta.title} | 税源地管理系统`;
    //未登录
    if (sessionStorage.getItem('enterpriseInfo')) {
        if (!auth.isLogin() && !auth.isLoginPath(to.path)) {
            // 保持用户当前访问的登录路径
            const currentLoginPath = to.path.startsWith('/user/') ? auth.userLoginUrl : auth.loginUrl;
            next(currentLoginPath + '?backurl=' + encodeURIComponent(to.fullPath));
        }
        //已登录
        else if (auth.isLogin() && auth.isLoginPath(to.path)) {
            next('/');
        } else {
            next();
        }
    } else if (!sessionStorage.getItem('enterpriseInfo') && !auth.isLoginPath(to.path)) {
        //判断是否选择过企业 没有选择直接跳
        const currentLoginPath = to.path.startsWith('/user/') ? auth.userLoginUrl : auth.loginUrl;
        next(currentLoginPath);
    } else {
        next();
    }
});

//动态往页面插入css
iconfontVersion.forEach((ele) => {
    loadStyle(iconfontUrl.replace('$key', ele));
});

//设置按钮权限，使用v-has控制显示/隐藏
auth.bindMenuAction();

// 初始化主题
initTheme();

new Vue({
    router,
    store,
    i18n,
    render: (h) => h(App)
}).$mount('#app');
