{"name": "vue-manage-system", "version": "4.2.0", "private": true, "scripts": {"dev": "vue-cli-service serve --open --mode dev --port 8080", "test": "vue-cli-service build --open --mode test", "prod": "vue-cli-service build --open --mode prod", "uat": "vue-cli-service build --open --mode uat"}, "dependencies": {"@jiaminghi/data-view": "^2.10.0", "ali-oss": "^6.17.1", "axios": "^0.18.0", "babel-polyfill": "^6.26.0", "crypto-js": "^4.1.1", "echarts": "^5.3.3", "element-ui": "^2.11.0", "mavon-editor": "^2.6.17", "minio": "^7.0.25", "qrcodejs2": "0.0.2", "qs": "^6.11.2", "vue": "^2.7.14", "vue-amap": "^0.5.10", "vue-cropper": "^0.5.6", "vue-cropperjs": "^3.0.0", "vue-i18n": "^8.10.0", "vue-quill-editor": "^3.0.6", "vue-router": "^3.0.3", "vue-schart": "^2.0.0", "vue-wxlogin": "^1.0.4", "vuedraggable": "^2.17.0", "wangeditor": "^4.7.15"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.9.0", "@vue/cli-service": "^3.9.0", "compression-webpack-plugin": "^6.1.1", "node-sass": "^5.0.0", "sass-loader": "^10.0.5", "vue-template-compiler": "^2.6.10", "vuex": "^3.6.0", "webpack": "^4.0.0"}}