let timeStamp = new Date().getTime();
module.exports = {
    publicPath: './',
    configureWebpack: {
        //重点
        output: {
            // 输出重构 打包编译后的js文件名称,添加时间戳.
            filename: `js/js[name].${timeStamp}.js`,
            chunkFilename: `js/chunk.[id].${timeStamp}.js`
        }
    },
    css: {
        //重点.
        extract: {
            // 打包后css文件名称添加时间戳
            filename: `css/[name].${timeStamp}.css`,
            chunkFilename: `css/chunk.[id].${timeStamp}.css`
        }
    },
    assetsDir: 'static',
    productionSourceMap: false,
    devServer: { disableHostCheck: true }
    // devServer: {
    //     proxy: {
    //         '/api':{
    //             target:'http://jsonplaceholder.typicode.com',
    //             changeOrigin:true,
    //             pathRewrite:{
    //                 '/api':''
    //             }
    //         }
    //     }
    // }
};
